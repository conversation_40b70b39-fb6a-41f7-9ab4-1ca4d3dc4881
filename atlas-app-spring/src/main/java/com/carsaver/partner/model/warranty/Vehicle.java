package com.carsaver.partner.model.warranty;

import com.carsaver.warranty.model.GetVehicleResponse;
import com.carsaver.warranty.model.VehicleConditionCode;
import com.carsaver.warranty.model.VehicleDriveType;
import com.carsaver.warranty.model.VehicleFuelType;
import lombok.Data;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.ZonedDateTime;

import static java.util.Optional.ofNullable;

@Data
public class Vehicle {

    @NotBlank(message="Vehicle VIN is required.")
    @Size(min=17, max=17, message = "Vehicle VIN must be 17 characters long.")
    private String vin;

    @NotNull(message="Vehicle year is required.")
    private Integer year;

    @NotBlank(message="Vehicle make is required.")
    private String make;

    @NotBlank(message="Vehicle model is required.")
    private String model;

    @NotBlank(message="Vehicle trim is required.")
    private String trim;
    private String vehicleType;
    private String bodyType;
    private String doors;
    private Integer grossWeight;

    @NotBlank(message="Vehicle drive type is required.")
    private String driveType;

    @NotBlank(message="Vehicle fuel type is required.")
    private String fuelType;

    private Integer engineCylindersOrCC;
    private String engineSize;
    private String engineSizeUom;
    private String engineAspiration;
    private Integer origMfgWarrMonths;
    private Integer origMfgWarrMiles;
    private String origMfgInServiceDate;

    @NotNull(message="Vehicle Odometer is required.")
    @Min(value=0, message = "Vehicle Odometer my not be less than 0.")
    private Integer odometer;

    @NotNull(message="Purchase Price is required.")
    private Double purchasePrice;

    private Double msrpOrNada;

    @NotBlank(message="Vehicle condition code is required.")
    private String conditionCode;

    private String stockNumber; //TODO - may be able to remove, not clear where there is used

    public String getDescription() {
        if(ObjectUtils.allNotNull(year, make, model)) {
            return year + " " + make + " " + model;
        }
        return null;
    }

    public String getEngineDescription() {
        String cylindersOrCcDesc = "-";
        if(engineCylindersOrCC != null) {
            cylindersOrCcDesc = engineCylindersOrCC + (engineCylindersOrCC <= 16 ? "-Cylinder" : " CC");
        }

        return engineSize != null
            ? engineSize + "L " + cylindersOrCcDesc
            : cylindersOrCcDesc;
    }

    public String getVehicleAge() {
        if(this.getYear() != null) {
            int diff  = ZonedDateTime.now().getYear() - this.getYear();

            return diff > 0 ? diff + " year(s)" : "New";
        }
        return "Unknown";
    }

    public static Vehicle from(GetVehicleResponse getVehicleResponse) {
        Vehicle vehicle = new Vehicle();
        vehicle.setVin(getVehicleResponse.getVIN());
        vehicle.setMsrpOrNada(getVehicleResponse.getMSRPOrNADA());
        BeanUtils.copyProperties(getVehicleResponse, vehicle);

        return vehicle;
    }

    public static Vehicle from(com.carsaver.warranty.model.Vehicle vehicle) {
        Vehicle v = new Vehicle();
        BeanUtils.copyProperties(vehicle, v);
        v.setDriveType(ofNullable(vehicle.getDriveType()).map(VehicleDriveType::getCode).orElse(null));
        v.setFuelType(ofNullable(vehicle.getFuelType()).map(VehicleFuelType::getCode).orElse(null));
        v.setConditionCode(ofNullable(vehicle.getConditionCode()).map(VehicleConditionCode::getCode).orElse(null));

        return v;
    }

    public com.carsaver.warranty.model.Vehicle toNwanVehicle() {
        com.carsaver.warranty.model.Vehicle vehicle = new com.carsaver.warranty.model.Vehicle();
        BeanUtils.copyProperties(this, vehicle);
        vehicle.setDriveType(ofNullable(driveType).map(VehicleDriveType::from).orElse(null));
        vehicle.setFuelType(ofNullable(fuelType).map(VehicleFuelType::from).orElse(null));
        vehicle.setConditionCode(ofNullable(conditionCode).map(VehicleConditionCode::from).orElse(null));

        return vehicle;
    }
}
