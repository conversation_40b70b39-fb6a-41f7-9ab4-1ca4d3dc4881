package com.carsaver.partner.client.configuration

import com.carsaver.configuration.v2.api.theme.ThemeResponse
import com.carsaver.partner.client.oauth.OAuthClient
import com.carsaver.partner.configuration.banners.BannerSettingsModel
import com.carsaver.partner.configuration.graphics.GraphicsConfigResponse
import com.carsaver.partner.configuration.leadpreferences.LeadPreferenceResponse
import com.carsaver.partner.configuration.mydealpage.MyDealPageRequest
import com.carsaver.partner.configuration.mydealpage.MyDealPageResponse
import com.carsaver.partner.configuration.ratesheet.FallbackRateSheet
import com.carsaver.partner.configuration.theming.ThemeRequest
import com.carsaver.partner.http.HttpRequest
import com.carsaver.partner.http.HttpService
import com.fasterxml.jackson.core.type.TypeReference
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

@Component
class ConfigServiceClient(
    @Value("\${configuration-service.api-uri}") private val activityServiceApiUri: String,
    private val httpService: HttpService,
    private val oAuthClient: OAuthClient
) {

    fun getThemingValues(dealerId: String?, domain: String?): ThemeResponse {
        // create the request
        val httpRequest = HttpRequest
            .get(activityServiceApiUri, "/theme")
            .queryParam("dealerId", dealerId)
            .queryParam("domain", domain)
            .bearerToken(oAuthClient.getAccessToken())

        return httpService.getSuccessResponse(httpRequest, object : TypeReference<ThemeResponse>() {})

    }

    fun saveThemingValues(dealerId: String?, userId: String, domain: String?, request: ThemeRequest) {
        // create the request
        val httpRequest = HttpRequest
            .post(activityServiceApiUri, "/theme")
            .queryParam("dealerId", dealerId)
            .queryParam("userId", userId)
            .queryParam("domain", domain)
            .ofJson(request)
            .bearerToken(oAuthClient.getAccessToken())

        httpService.assertResponse(httpRequest)

    }

    fun getMyDealPageValues(dealerId: String?, domain: String?): MyDealPageResponse {
        // create the request
        val httpRequest = HttpRequest
            .get(activityServiceApiUri, "/my-deal-page")
            .queryParam("dealerId", dealerId)
            .queryParam("domain", domain)
            .bearerToken(oAuthClient.getAccessToken())

        return httpService.getSuccessResponse(httpRequest, object : TypeReference<MyDealPageResponse>() {})
    }

    fun saveMyDealPageValues(dealerId: String?, userId: String, domain: String?, request: MyDealPageRequest) {
        // create the request
        val httpRequest = HttpRequest
            .post(activityServiceApiUri, "/my-deal-page")
            .queryParam("dealerId", dealerId)
            .queryParam("userId", userId)
            .queryParam("domain", domain)
            .ofJson(request)
            .bearerToken(oAuthClient.getAccessToken())

        httpService.assertResponse(httpRequest)

    }

    fun getRateSheet(dealerId: String?, domain: String?): FallbackRateSheet {
        // create the request
        val httpRequest = HttpRequest
            .get(activityServiceApiUri, "/fallback-rate-sheet")
            .queryParam("dealerId", dealerId)
            .queryParam("domain", domain)
            .bearerToken(oAuthClient.getAccessToken())

        return httpService.getSuccessResponse(httpRequest, object : TypeReference<FallbackRateSheet>() {})

    }

    fun saveRateSheet(dealerId: String?, userId: String, domain: String?, request: FallbackRateSheet) {
        // create the request
        val httpRequest = HttpRequest
            .post(activityServiceApiUri, "/fallback-rate-sheet")
            .queryParam("dealerId", dealerId)
            .queryParam("userId", userId)
            .queryParam("domain", domain)
            .ofJson(request)
            .bearerToken(oAuthClient.getAccessToken())

        httpService.assertResponse(httpRequest)
    }

    fun getLeadPreference(dealerId: String?, domain: String?): LeadPreferenceResponse {
        // create the request
        val httpRequest = HttpRequest
            .get(activityServiceApiUri, "/lead-preferences")
            .queryParam("dealerId", dealerId)
            .queryParam("domain", domain)
            .bearerToken(oAuthClient.getAccessToken())

        return httpService.getSuccessResponse(httpRequest, object : TypeReference<LeadPreferenceResponse>() {})
    }

    fun saveLeadPreference(dealerId: String?, userId: String, domain: String?, request: LeadPreferenceResponse) {
        // create the request
        val httpRequest = HttpRequest
            .post(activityServiceApiUri, "/lead-preferences")
            .queryParam("dealerId", dealerId)
            .queryParam("userId", userId)
            .queryParam("domain", domain)
            .ofJson(request)
            .bearerToken(oAuthClient.getAccessToken())

        httpService.assertResponse(httpRequest)
    }

    fun getBannerValues(dealerId: String?, domain: String?): BannerSettingsModel {
        // create the request
        val httpRequest = HttpRequest
            .get(activityServiceApiUri, "/banner")
            .queryParam("dealerId", dealerId)
            .queryParam("domain", domain)
            .bearerToken(oAuthClient.getAccessToken())

        return httpService.getSuccessResponse(httpRequest, object : TypeReference<BannerSettingsModel>() {})
    }

    fun saveBannerValues(dealerId: String?, userId: String, domain: String?, request: BannerSettingsModel) {
        // create the request
        val httpRequest = HttpRequest
            .post(activityServiceApiUri, "/banner")
            .queryParam("dealerId", dealerId)
            .queryParam("userId", userId)
            .queryParam("domain", domain)
            .ofJson(request)
            .bearerToken(oAuthClient.getAccessToken())

        httpService.assertResponse(httpRequest)

    }

    fun getGraphicsConfigByDealerId(dealerId: String?, domain: String?): GraphicsConfigResponse {

        val httpRequest = HttpRequest
            .get(activityServiceApiUri, "/graphics")
            .queryParam("dealerId", dealerId)
            .queryParam("domain", domain)
            .bearerToken(oAuthClient.getAccessToken())

        return httpService.getSuccessResponse(httpRequest, object : TypeReference<GraphicsConfigResponse>() {})

    }

}
