package com.carsaver.partner.configuration.graphics

import com.carsaver.magellan.auth.SessionUtils
import com.carsaver.partner.configuration.graphics.mappers.GraphicsConfigMultipartExtractorMapper
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile

private const val PAGE_PATH = "Graphics And Images"
@RestController
class GraphicsConfigController(
    private val graphicsConfigService: GraphicsConfigService
) {
    private val logger = org.slf4j.LoggerFactory.getLogger(GraphicsConfigController::class.java)

    @GetMapping(value = ["/api/configuration/builder"], params = ["page=${PAGE_PATH}"])
    fun getBuilderForGraphicsConfigs(
        @RequestParam(value = "dealerId", required = false) dealerId: String?,
        @RequestParam("page") page: String?,
        @RequestParam(value = "userId", required = false) userId: String?
    ): Any {
        val resource = this::class.java.classLoader.getResource("configurationBuilderDefinition/websiteConfigurations_graphicsAndImages.json")
        return resource?.readText() ?: throw IllegalStateException("Resource not found")
    }

    @GetMapping(value = ["/api/configuration"], params = ["dealerId", "page=${PAGE_PATH}"])
    fun getGraphicsConfigs(
        @RequestParam("dealerId") dealerId: String,
    ): Any {
        return graphicsConfigService.getGraphicsConfigByDealer(dealerId, null)
    }

    @PostMapping(value = ["/api/configuration"], params = ["dealerId", "page=${PAGE_PATH}"], consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
    fun saveGraphicsConfigByDealer(
        @RequestParam("dealerId") dealerId: String,
        @RequestParam("page") page: String,
        @RequestParam request: Map<String, Any?>,
    ) : ResponseEntity<Any> {
        logger.info("Saving graphics config for dealerId: $dealerId, page: $page")
        val loggedInUser = SessionUtils.getLoggedInUser()
            .orElseThrow { RuntimeException("logged-in user required") }

        val graphicsFiles = GraphicsConfigMultipartExtractorMapper.extractFiles(request)
        val payload = GraphicsConfigMultipartExtractorMapper.removeNotStringValues(request)
        graphicsConfigService.saveGraphicsConfigByDealer(dealerId, loggedInUser.id, null, payload, graphicsFiles)
        return ResponseEntity.ok().build()
    }

}
