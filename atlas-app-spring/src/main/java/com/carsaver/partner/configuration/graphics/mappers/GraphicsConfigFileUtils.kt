package com.carsaver.partner.configuration.graphics.mappers

import org.apache.commons.fileupload.disk.DiskFileItemFactory
import org.springframework.web.multipart.MultipartFile
import org.springframework.web.multipart.commons.CommonsMultipartFile
import java.awt.image.BufferedImage
import javax.imageio.ImageIO

class GraphicsConfigFileUtils {

    companion object {

        fun overrideFileNameAndSanitize(
            file: MultipartFile,
            index: Int,
            dealerId: String,
            prefix: String
        ): MultipartFile {
            val newName = "$dealerId-$prefix-graphics-${index + 1}.${file.originalFilename?.split(".")?.last()}"

            val factory = DiskFileItemFactory()
            val item = factory.createItem(newName, file.contentType, false, newName)

            val formatName = when (file.contentType?.lowercase()) {
                "image/jpeg", "image/jpg" -> "jpeg"
                "image/png" -> "png"
                else -> null // unsupported content type
            }

            val fos = item.outputStream
            if (formatName != null) {
                val image: BufferedImage = ImageIO.read(file.bytes.inputStream())
                ImageIO.write(image, formatName, fos)
            } else {
                file.bytes.let { fos.write(it) }
            }
            fos.close()
            return CommonsMultipartFile(item)
        }
    }
}
