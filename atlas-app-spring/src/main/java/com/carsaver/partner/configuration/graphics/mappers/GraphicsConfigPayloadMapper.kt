package com.carsaver.partner.configuration.graphics.mappers

import com.carsaver.partner.configuration.graphics.GraphicsConfigRequest
import com.fasterxml.jackson.databind.ObjectMapper

class GraphicsConfigPayloadMapper {

    companion object {
        val mapper = ObjectMapper()

        fun mapToConfig(payload: Map<String, String?>): GraphicsConfigRequest {
            val graphicsConfig = GraphicsConfigRequest()

            // Map logo properties
            payload["logoFile"]?.let { graphicsConfig.logoName = it }

            // Map favicon properties
            payload["faviconFile"]?.let { graphicsConfig.favIconName = it }

            // Map dealership images properties
            val mobileName = payload["dealershipFile1"]
            val desktopName = payload["dealershipFile2"]

            if (mobileName != null || desktopName != null) {
                graphicsConfig.dealershipImages = GraphicsConfigRequest.DealershipImages(
                    mobileName = mobileName,
                    desktopName = desktopName
                )
            }

            return graphicsConfig
        }
    }
}
