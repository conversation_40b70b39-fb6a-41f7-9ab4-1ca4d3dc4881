package com.carsaver.partner.configuration.banners

import com.carsaver.partner.client.configuration.ConfigServiceClient
import com.carsaver.partner.configuration.utils.FlatMapUtils
import com.carsaver.partner.configuration.utils.ModelMapperUtils
import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.stereotype.Service

@Service
class BannersConfigService (
    val configServiceClient: ConfigServiceClient,
    val objectMapper: ObjectMapper = ObjectMapper()
){

    fun getBannerConfiguration(
        dealerId: String?,
        domain: String?
    ): Map<String, Any?> {
        //get the response from config service
        val response = configServiceClient.getBannerValues(dealerId, domain)

        //convert model to map for FE
        val flatMap = FlatMapUtils.modelToMap(response)
        return flatMap
    }


    fun saveBannerConfiguration(
        dealerId: String?,
        userId: String,
        domain: String?,
        request: Map<String, Any?>
    ) {
        // Pre-process the request map to handle arrays
        val processedRequest = request.mapValues { (key, value) ->
            when (value) {
                is List<*> -> objectMapper.writeValueAsString(value)
                else -> value?.toString()
            }
        }

        val model = ModelMapperUtils.mapToModel(processedRequest, BannerSettingsModel())

        //send to config service
        configServiceClient.saveBannerValues(dealerId,userId, domain, model)
    }

}
