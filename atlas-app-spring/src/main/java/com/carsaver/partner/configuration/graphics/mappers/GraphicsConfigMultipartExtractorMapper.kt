package com.carsaver.partner.configuration.graphics.mappers

import org.springframework.web.multipart.MultipartFile

class GraphicsConfigMultipartExtractorMapper {

    companion object {
        fun extractFiles(request: Map<String, Any?>): MutableList<MultipartFile?> {
            val files = mutableListOf<MultipartFile?>()

            listOf(
                "logoFile",
                "faviconFile",
                "dealershipFile1",
                "dealershipFile2"
            ).forEach { fileKey ->
                files.add(request[fileKey]?.let {
                    if (it is MultipartFile) it
                    else throw IllegalArgumentException("Invalid file type. Key: $fileKey")
                })
            }
            return files
        }

        fun removeNotStringValues(request: Map<String, Any?>): Map<String, String> {
            return request.filter { it.value == null || it.value is String }
                .mapValues { it.value as String? ?: "" }
        }
    }
}
