package com.carsaver.partner.configuration.graphics.mappers

import org.springframework.web.multipart.MultipartFile

class GraphicsConfigMultipartExtractorMapper {

    companion object {
        fun extractFiles(request: Map<String, Any?>): MutableList<MultipartFile?> {
            val fileKeyToIndexMap = mapOf(
                "logoFile" to 0,
                "faviconFile" to 1,
                "dealershipFile1" to 2,
                "dealershipFile2" to 3
            )

            // Initialize with nulls for all 4 positions
            val files = mutableListOf<MultipartFile?>(null, null, null, null)

            // Only extract files that are actually MultipartFile objects in the request
            fileKeyToIndexMap.forEach { (fileKey, index) ->
                val value = request[fileKey]
                if (value is MultipartFile) {
                    files[index] = value
                }
            }

            return files
        }

        fun removeNotStringValues(request: Map<String, Any?>): Map<String, String> {
            return request.filter { it.value == null || it.value is String }
                .mapValues { it.value as String? ?: "" }
        }
    }
}
