package com.carsaver.partner.configuration.banners

import com.carsaver.magellan.auth.SessionUtils
import com.carsaver.partner.configuration.builder.LocalBuilderDefinitionService
import org.slf4j.LoggerFactory
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

private const val PAGE_PATH = "Banners"

@RestController
class BannersController(
    private val localBuilderDefinitionService: LocalBuilderDefinitionService,
    private val bannersConfigService: BannersConfigService
) {

    private val logger = LoggerFactory.getLogger(BannersController::class.java)

    @GetMapping(value = ["/api/configuration/builder"], params = ["page=${PAGE_PATH}"])
    fun getBuilderConfiguration(
        @RequestParam("page") page: String?
    ): Any {
        return localBuilderDefinitionService.getBuilderDefinition(page)
    }

    @GetMapping(value = ["/api/configuration"], params = ["dealerId", "page=${PAGE_PATH}"])
    fun getConfiguration(@RequestParam("dealerId") dealerId: String): Any {
        return bannersConfigService.getBannerConfiguration(dealerId, null)
    }

    @PostMapping(value = ["/api/configuration"], params = ["dealerId", "page=${PAGE_PATH}"], consumes = [MediaType.APPLICATION_JSON_VALUE])
    fun saveConfigurationForDealer(
        @RequestParam("dealerId") dealerId: String,
        @RequestParam("page") page: String,
        @RequestBody request: Map<String, Any?>,
    ): ResponseEntity<Any> {

        val loggedInUser = SessionUtils.getLoggedInUser()
            .orElseThrow { RuntimeException("logged-in user required") }

        bannersConfigService.saveBannerConfiguration(
            dealerId = dealerId,
            userId = loggedInUser.id,
            domain = null,
            request = request,
        )
        return ResponseEntity.ok().build()
    }
}
