package com.carsaver.partner.service;

import com.carsaver.magellan.api.exception.NotFoundException;
import com.carsaver.magellan.auth.AuthUtils;
import com.carsaver.magellan.client.CampaignClient;
import com.carsaver.magellan.client.DealerClient;
import com.carsaver.magellan.client.ProgramClient;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.client.prospect.UpgradeCampaignClient;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.campaign.CampaignView;
import com.carsaver.magellan.model.foundation.ProgramView;
import com.carsaver.partner.customer.CustomerDetailsLinkService;
import com.carsaver.partner.model.DealProgramAndDomain;
import com.carsaver.partner.model.subscription.Product;
import com.carsaver.partner.model.user.Customer;
import com.carsaver.partner.repository.DynamoDbRepository;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;
import software.amazon.awssdk.services.dynamodb.model.ScanRequest;
import software.amazon.awssdk.services.dynamodb.paginators.ScanIterable;
import software.amazon.awssdk.utils.ImmutableMap;

import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
public class ProgramApiService {

    private final ProgramClient programClient;
    private final CampaignClient campaignClient;
    private final DealerClient dealerClient;
    private final com.carsaver.partner.client.dealer.DealerClient localDealerClient;
    private final DynamoDbRepository dynamoDbRepository;
    private final SplitFeatureFlags splitFeatureFlags;
    private final UserClient userClient;
    private final String nissanDigitalRetailCampaignDomain;
    private final ProspectService prospectService;
    private final UpgradeCampaignClient upgradeCampaignClient;
    private final CustomerDetailsLinkService customerDetailsLinkService;

    public ProgramApiService(ProgramClient programClient,
                             CampaignClient campaignClient,
                             DealerClient dealerClient,
                             com.carsaver.partner.client.dealer.DealerClient localDealerClient,
                             DynamoDbRepository dynamoDbRepository,
                             SplitFeatureFlags splitFeatureFlags,
                             UserClient userClient,
                             @Value("${digital-retail.campaign-id}") String digitalRetailCampaignId,
                             ProspectService prospectService,
                             UpgradeCampaignClient upgradeCampaignClient,
                             CustomerDetailsLinkService customerDetailsLinkService) {
        this.programClient = programClient;
        this.campaignClient = campaignClient;
        this.dealerClient = dealerClient;
        this.localDealerClient = localDealerClient;
        this.dynamoDbRepository = dynamoDbRepository;
        this.splitFeatureFlags = splitFeatureFlags;
        this.userClient = userClient;
        this.nissanDigitalRetailCampaignDomain = Optional.ofNullable(this.campaignClient.findById(digitalRetailCampaignId)).map(CampaignView::getDomain).orElse(null);
        this.prospectService = prospectService;
        this.upgradeCampaignClient = upgradeCampaignClient;
        this.customerDetailsLinkService = customerDetailsLinkService;
    }

    public DealProgramAndDomain getDealProgramAndDomain(String userId, String campaignId, String dealerId, String dealerTable) {
        final CampaignView campaignView = Optional.ofNullable(campaignClient.findById(campaignId))
            .orElseThrow(NotFoundException::new);

        final ProgramView programView = getProgramView(campaignView);

        String domain = getSiteDomain(programView, userId, dealerId, dealerTable, campaignView);

        return DealProgramAndDomain.from(domain, programView);
    }

    public String getSiteDomain(ProgramView programView, String userId, String dealerId, String dealerTable, CampaignView campaignView) {
        final boolean isUpgradeProduct = Objects.equals(programView.getProduct().getId(), Product.UPGRADE);
        if (isUpgradeProduct) {
            String domain = campaignView.getDomain();
            if (splitFeatureFlags.isDealerizedProspectFeatureEnabled(userId)) {
                final String dealerNameWithoutWhiteSpaceAndSpecialCharacters = getDealerizedSubDomainOrEmpty(dealerId, dealerTable).orElse("");
                domain = dealerNameWithoutWhiteSpaceAndSpecialCharacters.concat(campaignView.getDomain());
            }
            return domain;
        }

        return customerDetailsLinkService.getCampaignDomain(campaignView);
    }

    private Optional<String> getDealerizedSubDomainOrEmpty(String dealerId, String dealerTable) {
        Optional<String> result = Optional.empty();
        ScanRequest scanRequest = createScanRequest(dealerTable, dealerId);
        ScanIterable scanResponses = dynamoDbRepository.getScanResponses(scanRequest);
        if (scanResponses.items().stream().findAny().isPresent()) {
            final Optional<DealerView> dealer = Optional.ofNullable(dealerClient.findById(dealerId));
            if (dealer.isPresent()) {
                String dealerName = dealer.map(DealerView::getName).orElseThrow();
                result = Optional.of(dealerName.replaceAll("[^a-zA-Z0-9]", "").toLowerCase().concat("."));
            }
        }

        return result;
    }

    private ScanRequest createScanRequest(String dealerTable, String dealerId) {
        return ScanRequest.builder()
            .tableName(dealerTable)
            .filterExpression("#attr = :value")
            .expressionAttributeNames(ImmutableMap.of("#attr", "dealerId"))
            .expressionAttributeValues(ImmutableMap.of(":value", AttributeValue.builder().s(dealerId).build()))
            .build();
    }

    public DealProgramAndDomain getDealProgramAndDomain(String userId, String dealerId, String dealerTable) {
        final Customer customerRecord = findCustomer(userId);
        final CampaignView campaignView = getCampaignView(customerRecord);
        final ProgramView programView = getProgramView(campaignView);

        boolean enabledForDigitalRetail = isProspectOrUserEnabledForDigitalRetail(campaignView);
        final String atlasUserId = AuthUtils.getUserIdFromSecurityContext().orElseThrow();
        String domain = getSiteDomain(programView, atlasUserId, dealerId, dealerTable, campaignView);

        return DealProgramAndDomain.from(domain, programView, enabledForDigitalRetail);
    }

    /**
     * Customer is an interface implemented by UserView and ProspectDTO
     *
     * @param customerId - can be either a user id or an upgrade prospect id or a whips prospect id
     * @return - an optional of Customer
     */
    private Customer findCustomer(String customerId) {
        return Optional.ofNullable(localDealerClient.getUserById(customerId))
            .map(Customer.class::cast)
            .or(() -> prospectService.findUpgradeProspectById(customerId))
            .or(() -> prospectService.findWhipsProspectById(customerId))
            .map(Customer.class::cast)
            .orElseThrow(NotFoundException::new);
    }

    private CampaignView getCampaignView(Customer customer) {
        String campaignId = customer.getCampaignId().orElseThrow(NotFoundException::new);
        CampaignView campaignView = campaignClient.findById(campaignId);
        if (campaignView == null) {
            throw new NotFoundException();
        }
        return campaignView;
    }

    private ProgramView getProgramView(CampaignView campaignView) {
        return programClient.findById(campaignView.getProgramId()).orElseThrow(NotFoundException::new);
    }

    private boolean isProspectOrUserEnabledForDigitalRetail(CampaignView campaignView) {
        return "digital-retail".equalsIgnoreCase(campaignView.getChannel());
    }

}
