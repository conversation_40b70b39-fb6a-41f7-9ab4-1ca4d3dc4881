package com.carsaver.partner.web.api.warranty;

import com.carsaver.core.DealerStatus;
import com.carsaver.elasticsearch.ElasticClient;
import com.carsaver.elasticsearch.ElasticResponse;
import com.carsaver.elasticsearch.model.UserDoc;
import com.carsaver.magellan.api.ImageUrlService;
import com.carsaver.magellan.client.DealerClient;
import com.carsaver.magellan.client.DealerLinkClient;
import com.carsaver.magellan.client.EvoxImageClient;
import com.carsaver.magellan.client.InventoryClient;
import com.carsaver.magellan.client.WarrantyContractClient;
import com.carsaver.magellan.model.DealerLinkRequest;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.UserView;
import com.carsaver.magellan.model.VehicleView;
import com.carsaver.magellan.model.WarrantyContractView;
import com.carsaver.magellan.model.evox.Image;
import com.carsaver.magellan.model.evox.ImageRequest;
import com.carsaver.magellan.model.warranty.CreatedSource;
import com.carsaver.magellan.support.SourceCreator;
import com.carsaver.magellan.validation.ErrorResponse;
import com.carsaver.magellan.validation.FieldError;
import com.carsaver.partner.elasticsearch.criteria.DealerUserSearchCriteria;
import com.carsaver.partner.model.warranty.ApplicantRegistration;
import com.carsaver.partner.model.warranty.Buyer;
import com.carsaver.partner.model.warranty.ConfirmationPageModel;
import com.carsaver.partner.model.warranty.CreateCustomerForm;
import com.carsaver.partner.model.warranty.CreateCustomerVehicle;
import com.carsaver.partner.model.warranty.EnrollCustomerModel;
import com.carsaver.partner.model.warranty.EnrollCustomerModelRequest;
import com.carsaver.partner.model.warranty.Form;
import com.carsaver.partner.model.warranty.Premium;
import com.carsaver.partner.model.warranty.PremiumsModel;
import com.carsaver.partner.model.warranty.PremiumsRequestModel;
import com.carsaver.partner.model.warranty.PremiumsSearchModel;
import com.carsaver.partner.model.warranty.Surcharge;
import com.carsaver.partner.model.warranty.Vehicle;
import com.carsaver.partner.model.warranty.VehicleConditionCode;
import com.carsaver.partner.model.warranty.WarrantyContract;
import com.carsaver.partner.security.SecurityUtils;
import com.carsaver.partner.service.warranty.WarrantyService;
import com.carsaver.partner.service.warranty.WarrantyUserService;
import com.carsaver.partner.service.warranty.exception.LifetimeWarrantyAlreadyRegisteredException;
import com.carsaver.partner.service.warranty.exception.PremiumUpgradeAlreadyRegisteredException;
import com.carsaver.partner.service.warranty.exception.PremiumsRequestValidationException;
import com.carsaver.partner.service.warranty.exception.VehicleNotFoundException;
import com.carsaver.partner.web.api.BaseListController;
import com.carsaver.partner.web.api.OnlyOneDealerInContext;
import com.carsaver.partner.web.api.SecurityHelperService;
import com.carsaver.search.support.PageMetadata;
import com.carsaver.search.support.SearchResults;
import com.carsaver.warranty.model.ApplicantRegistrationException;
import com.carsaver.warranty.model.FindSurchargesException;
import com.carsaver.warranty.model.GetFormsException;
import com.carsaver.warranty.model.GetFormsRequest;
import com.carsaver.warranty.model.GetVehicleResponse;
import com.carsaver.warranty.model.PremiumsRequest;
import com.carsaver.warranty.model.PremiumsRequestException;
import com.carsaver.warranty.model.Registration;
import com.carsaver.warranty.model.VehicleRequest;
import com.carsaver.warranty.model.VehicleRequestException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MultiMatchQueryBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.SortDefault;
import org.springframework.hateoas.CollectionModel;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.validation.BindingResult;
import org.springframework.validation.Errors;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpSession;
import javax.validation.Valid;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static org.elasticsearch.index.query.QueryBuilders.boolQuery;
import static org.elasticsearch.index.query.QueryBuilders.multiMatchQuery;
import static org.elasticsearch.index.query.QueryBuilders.termQuery;
import static org.elasticsearch.index.query.QueryBuilders.termsQuery;

//TODO - test @PreAuthorize
@Slf4j
@RequestMapping("/api")
@RestController
public class WarrantyApiController implements BaseListController, OnlyOneDealerInContext {
    public static final String WARRANTY_CREATE_PERMISSION = "warranty:create";
    @Value("${tenant.express-id}")
    private String expressTenantId;

    @Value("${tenant.default-id}")
    private String defaultTenantId;

    @Autowired
    private WarrantyService warrantyService;

    @Autowired
    private WarrantyContractClient warrantyContractClient;

    @Autowired
    private DealerLinkClient dealerLinkClient;


    @Autowired
    private ImageUrlService imageUrlService;

    @Autowired
    private EvoxImageClient evoxImageClient;

    @Autowired
    private InventoryClient inventoryClient;

    @Autowired
    private WarrantyControllerHelper helper;

    @Value("${warranty-service.providerCode}")
    private String providerCode;

    @Autowired
    private SourceCreator sourceCreator;

    @Autowired
    @Qualifier("gibson")
    private ElasticClient elasticClient;

    @Autowired
    private HttpSession session;

    @Autowired
    private DealerClient dealerClient;

    @Autowired
    private SecurityHelperService securityHelperService;

    @Autowired
    private WarrantyUserService warrantyUserService;

    @GetMapping(value="/warranty/vehicles", params = {"vin"})
    public CreateCustomerVehicle getVehicleByVin(@RequestParam String vin){
        return Optional.ofNullable(inventoryClient.getVehiclesByVin(vin))
                       .map(CollectionModel::getContent)
                       .map(content -> {
                           if (content.isEmpty()) {
                               return null;
                           }

                           return content.iterator().next();
                       })
                       .map(vehicleView -> CreateCustomerVehicle.from(vehicleView, imageUrlService.getDefaultImage(vehicleView)))
                       .orElse(null);
    }

    @GetMapping(value = "/warranty/verify-stock-number", params = {"stockNumber","dealerIds"})
    public ResponseEntity<CreateCustomerVehicle> verifyStockNumber(@RequestParam("dealerIds") List<String> dealerIdsParam,
                                                                   @RequestParam String stockNumber) {
        validateOnlyOneDealerInRequest(dealerIdsParam);
        final List<String> dealerIds = getDealerIdsAndCheckPermissions(session, WARRANTY_CREATE_PERMISSION, dealerIdsParam);

        Optional<VehicleView> vehicleOpt = inventoryClient.findVehicleByStockNumberAndDealerId(stockNumber, dealerIds.get(0));
        if (vehicleOpt.isEmpty()) {
            return ResponseEntity.notFound().build();
        }

        String imageUrl = null;
        VehicleView vehicle = vehicleOpt.get();
        if (vehicle.getImageUrl() != null && vehicle.getImageUrl().length > 0) {
            imageUrl = vehicle.getImageUrl()[0];
        } else {
            ImageRequest imageRequest = ImageRequest.builder()
                                                    .exteriorColorCode(vehicle.getExteriorColorCode())
                                                    .build();
            List<Image> images = evoxImageClient.getImageUrlByStyleId(vehicle.getStyleId(), imageRequest);
            if (CollectionUtils.isNotEmpty(images)) {
                imageUrl = images.get(0).getUrl();
            }
        }

        CreateCustomerVehicle createCustomerVehicle = CreateCustomerVehicle.builder()
               .imageUrl(imageUrl)
               .make(vehicle.getMake())
               .year(vehicle.getYear())
               .model(vehicle.getModel())
               .trim(vehicle.getTrim())
               .vin(vehicle.getVin())
               .stockNumber(vehicle.getStockNumber())
               .build();

        return ResponseEntity.ok(createCustomerVehicle);
    }

    @PostMapping("/warranty/create-customer")
    public UserView createCustomer(@RequestBody CreateCustomerForm form) {
        return warrantyUserService.createWarrantyCustomer(form);
    }

    @PostMapping(value = "/dealer/warranty/customer/search")
    public SearchResults<UserView> findUsersWithDealerLink(
        @RequestBody DealerUserSearchCriteria searchForm,
        @SortDefault("firstName") Pageable pageable
    ) {
        final List<String> dealerIds = searchForm.getDealerIds() == null || searchForm.getDealerIds().isEmpty()
                ? getDealerIds(session)
                : searchForm.getDealerIds();
        validateOnlyOneDealerInRequest(dealerIds);
        String dealerId = dealerIds.get(0);

        String keyword = searchForm.getName();

        BoolQueryBuilder defaultTenant = new BoolQueryBuilder();
        defaultTenant.filter(termsQuery("tenant.id", defaultTenantId));
        defaultTenant.filter(termsQuery("dealerAcls", dealerId ));

        BoolQueryBuilder expressTenant = new BoolQueryBuilder();
        expressTenant.filter(termsQuery("tenant.id", expressTenantId));

        BoolQueryBuilder queryTenant = boolQuery();
        queryTenant.filter(termsQuery("dealerLinks.dealerId", dealerId ));
        queryTenant.should(defaultTenant);
        queryTenant.should(expressTenant);
        queryTenant.minimumShouldMatch(1);
        queryTenant.must(termQuery("enabled", true));

        BoolQueryBuilder query = boolQuery();
        query.must(queryTenant);
        if (keyword != null && !keyword.isEmpty() ){
            query.should(multiMatchQuery(keyword, "firstName", "lastName").type(MultiMatchQueryBuilder.Type.CROSS_FIELDS));
            query.should(termQuery("email",keyword));
            query.should(termQuery("phoneNumber",keyword));
            query.minimumShouldMatch(1);
        }
        query.must(termQuery("enabled", true));

        SearchSourceBuilder searchBldr = new SearchSourceBuilder();
        searchBldr.query(query);
        searchBldr.size(pageable.getPageSize());
        searchBldr.from((pageable.getPageNumber()-1)*pageable.getPageSize());
        searchBldr.sort("_score");

        SearchRequest searchRequest = new SearchRequest("users");
        searchRequest.source(searchBldr);

        ElasticResponse<UserDoc> searchResponse = this.elasticClient.search(searchRequest, UserDoc.class);

        List<UserView> users = searchResponse.getItems()
            .stream()
            .map(userDoc -> {
                UserView user = new UserView();

                BeanUtils.copyProperties(userDoc, user);

                user.setAddress(userDoc.getAddress().getStreet());
                user.setCity(userDoc.getAddress().getCity());
                user.setStateCode(userDoc.getAddress().getStateCode());
                user.setZipCode(userDoc.getAddress().getZipCode());

                return user;
            })
            .collect(Collectors.toList());

        Page<UserView> page = new PageImpl<>(users, pageable, users.size());

        return SearchResults.<UserView>builder()
            .content(page.getContent())
            .pageMetadata(new PageMetadata(page.getSize(), page.getNumber(), Math.toIntExact(searchResponse.getSearchResponse().getHits().getTotalHits().value)))
            .build();
    }

    private List<String> getDealerIdsAndCheckPermissions(HttpSession session, String permission, List<String> dealerIdsParam) {
        final List<String> dealerIds = dealerIdsParam == null || dealerIdsParam.isEmpty()
                ? getDealerIds(session)
                : dealerIdsParam;
        securityHelperService.checkPermission(SecurityContextHolder.getContext().getAuthentication(), dealerIds, permission);
        return dealerIds;
    }


    //LIFETIME-WARRANTY: STEP 1c - CUSTOMER selection ACTION
//    @PreAuthorize("hasPermission(#dealer, 'warranty:create')")
    @GetMapping(value = "/dealer/warranty/{user}/select-customer", params = {"dealerIds"})
    public Buyer selectCustomer(
        @RequestParam("dealerIds") List<String> dealerIdsParam,
        @PathVariable("user") UserView customer) {
        List<String> dealerIds = getDealerIdsAndCheckPermissions(session, WARRANTY_CREATE_PERMISSION, dealerIdsParam);
        validateOnlyOneDealerInRequest(dealerIds);

        // refreshing all warranty statuses for customer prior to searching for premiums
        List<WarrantyContractView> warrantyContracts = new ArrayList<>(warrantyContractClient.findByUserId(customer.getId()).getContent());
        if(!warrantyContracts.isEmpty()) {
            warrantyContracts.forEach(wc -> {
                try {
                    warrantyService.refreshRemoteRegistrationAsync(wc.getId());
                } catch(Exception ex) {
                    log.error("Unable to refresh warranties for user {}, exception was {}", customer.getId(), ex.getMessage());
                }
            });
        }

        return Buyer.from(customer);
    }

//    @PreAuthorize("hasPermission(#dealerIds, 'warranty:create')")
    @GetMapping("/dealer/warranty/{user}/search-premiums")
    public PremiumsSearchModel premiumSearchForm(
        @RequestParam("dealerIds") List<String> dealerIdsParam,
        @PathVariable UserView user,
        @RequestParam(value = "vin", required = false) String vin) {
        List<String> dealerIds = getDealerIdsAndCheckPermissions(session, WARRANTY_CREATE_PERMISSION, dealerIdsParam);
        validateOnlyOneDealerInRequest(dealerIds);
        DealerView dealer = dealerClient.findById(dealerIds.get(0));

        PremiumsSearchModel premiumsSearchModel = new PremiumsSearchModel();

        helper.getEffectiveDealerEnrollmentDate(dealer)
              .ifPresent(premiumsSearchModel::setDealerEnrollmentEffectiveDate);

        premiumsSearchModel.setProspectDealer(DealerStatus.PROSPECT == dealer.getDealerStatus());
        boolean walmartEmployee = user.tagExists(WarrantyControllerHelper.USER_TAGS_WALMART_ASSOCIATE);
        PremiumsRequest premiumsRequest = helper.getPremiumsRequest(dealer, vin, walmartEmployee);
        premiumsSearchModel.setPremiumsRequest(PremiumsRequestModel.from(premiumsRequest));

        return premiumsSearchModel;
    }

    //LIFETIME-WARRANTY: step 2b2 - PREMIUMS search ACTION
//    @PreAuthorize("hasPermission(#dealer, 'warranty:create')")
    @PostMapping("/dealer/warranty/{user}/search-premiums-validation")
    public void searchPremiumsValidation(
        @PathVariable UserView user,
        @RequestBody PremiumsRequestModel premiumsRequestModel) throws PremiumsRequestValidationException {
        final List<String> dealerIds = getDealerIdsAndCheckPermissions(session, WARRANTY_CREATE_PERMISSION, premiumsRequestModel.getDealerIds());
        validateOnlyOneDealerInRequest(dealerIds);
        final DealerView dealer = dealerClient.findById(dealerIds.get(0));

        BindingResult result = helper.validatePremiumsRequest(dealer, premiumsRequestModel, user);

        if(result.hasErrors()) {
            //picked up by MethodArgumentNotValidException controller advice
            throw new PremiumsRequestValidationException(result);
        }
    }

    //LIFETIME-WARRANTY: step 2b2 - PREMIUMS search ACTION
//    @PreAuthorize("hasPermission(#dealer, 'warranty:create')")
    @PostMapping("/dealer/warranty/{user}/search-premiums")
    public PremiumsModel searchPremiums(
        @PathVariable UserView user,
        @Valid @RequestBody PremiumsRequestModel premiumsRequestModel) throws PremiumsRequestValidationException {
        final List<String> dealerIds = getDealerIdsAndCheckPermissions(session, WARRANTY_CREATE_PERMISSION, premiumsRequestModel.getDealerIds());
        validateOnlyOneDealerInRequest(dealerIds);
        final DealerView dealer = dealerClient.findById(dealerIds.get(0));
        final PremiumsModel premiumsModel = new PremiumsModel();

        BindingResult result = helper.validatePremiumsRequest(dealer, premiumsRequestModel, user);
        if(result.hasErrors()) {
            throw new PremiumsRequestValidationException(result);
        }

        PremiumsRequest premiumsRequest = helper.toPremiumsRequest(premiumsRequestModel, dealer, user);

        try {
            GetVehicleResponse getVehicleResponse = warrantyService.getVehicle(new VehicleRequest(premiumsRequest.getVin(), premiumsRequest.getOdometer()));

            if(getVehicleResponse == null) {
                throw new VehicleNotFoundException("No vehicle found meeting the search criteria");
            }

            premiumsModel.setVehicle(Vehicle.from(getVehicleResponse));

            List<com.carsaver.warranty.model.Premium> nwanPremiums = warrantyService.getPremiums(premiumsRequest);
            List<Premium> premiumsList = IntStream
                .range(0, nwanPremiums.size())
                .mapToObj(i -> Premium.from(nwanPremiums.get(i), i))
                .collect(Collectors.toList());

            premiumsModel.setPremiums(premiumsList.stream().filter(Premium::isGiveAway).collect(Collectors.toList()));

            boolean isCarSaverEmployeeTransaction = SecurityUtils.isAdminUser() && Boolean.TRUE.equals(premiumsRequest.getCarSaverTransaction());
            if(isCarSaverEmployeeTransaction || dealer.getEffectivePreferences().getOfferingOptionalVsc()) {
                premiumsModel.setPremiumQuickQuotes(premiumsList.stream()
                    .filter(premium -> !premium.isGiveAway()).collect(Collectors.toList()));
            }

        } catch(VehicleRequestException | PremiumsRequestException e) {
            premiumsModel.setErrorMessage(e.getMessage());

            return premiumsModel;
        }

        checkShouldSkipToConfirmation(dealer, user, premiumsRequest.getVin())
            .ifPresent(premiumsModel::setExistingWarrantyContract);

        return premiumsModel;
    }

    public void checkAllowedToRegisterApplicant(DealerView dealer, UserView user, ApplicantRegistration applicantRegistration) {
        String vin = applicantRegistration.getVehicle().getVin();
        if(applicantRegistration.getSelectedPremium().isGiveAway()) {
            checkShouldSkipToConfirmation(dealer, user, vin)
                .ifPresent(warrantyContract -> {
                    throw new LifetimeWarrantyAlreadyRegisteredException("already has lifetime warranty");
                });
        } else {
            List<WarrantyContractView> existingContracts = new ArrayList<>(
                warrantyContractClient.findByUserIdAndVinAndDealerId(
                    user.getId(), vin, dealer.getId()).getContent()
            );

            log.info("customerId = {}, vin = {}, found {} existingContracts", user.getId(), vin, existingContracts.size());
            long numberOfValidContracts = existingContracts.stream()
                .filter(wc -> !wc.getRemoteRegistration().getRegistrationStatus().equalsIgnoreCase("VOIDED"))
                .count();
            if(numberOfValidContracts > 1) {
                throw new PremiumUpgradeAlreadyRegisteredException("already has > 1 warranty");
            }
        }
    }

    public Optional<WarrantyContract> checkShouldSkipToConfirmation(DealerView dealer, UserView user, String vin) {

        WarrantyContract existingWarrantyContract = null;
        List<WarrantyContractView> existingContracts = new ArrayList<>(
            warrantyContractClient.findByUserIdAndVinAndDealerId(
                user.getId(), vin, dealer.getId()).getContent()
        );

        log.info("customerId = {}, vin = {}, found {} existingContracts", user.getId(), vin, existingContracts.size());
        long numberOfValidContracts = existingContracts.stream()
               .filter(wc -> !wc.getRemoteRegistration().getRegistrationStatus().equalsIgnoreCase("VOIDED"))
               .filter(wc -> wc.getSelectedPremium().isGiveAway())
               .count();
        if (numberOfValidContracts > 0) {
            WarrantyContractView lifetimeWarrantyContract = existingContracts.get(0);
            existingWarrantyContract = WarrantyContract.from(lifetimeWarrantyContract);
        }

        return Optional.ofNullable(existingWarrantyContract);
    }

    public static List<FieldError> toFieldErrors(Errors result, Locale locale, MessageSource messageSource) {
        List<FieldError> fieldErrors = new ArrayList<>();

        result.getFieldErrors().forEach(fieldError -> fieldErrors.add(FieldError.builder()
          .bindingFailure(fieldError.isBindingFailure())
          .field(fieldError.getField())
          .objectName(fieldError.getObjectName())
          .rejectedValue(fieldError.getRejectedValue())
          .message(messageSource.getMessage(fieldError, locale))
          .build()));

        return fieldErrors;
    }

//    @PreAuthorize("hasPermission(#dealer, 'warranty:create')")
    @GetMapping(value = "/dealer/warranty/{user}/confirmations/{vin}")
    public ConfirmationPageModel get(
        @RequestParam("dealerIds") List<String> dealerIdsParam,
        @PathVariable UserView user,
        @PathVariable String vin,
        @ModelAttribute PremiumsRequestModel premiumsRequestModel
    ) {
        final List<String> dealerIds = getDealerIdsAndCheckPermissions(session, WARRANTY_CREATE_PERMISSION, dealerIdsParam);
        validateOnlyOneDealerInRequest(dealerIds);
        final DealerView dealer = dealerClient.findById(dealerIds.get(0));

        ConfirmationPageModel confirmationPageModel = new ConfirmationPageModel();
        confirmationPageModel.setBuyer(Buyer.from(user));

        List<WarrantyContractView> allWarrantyContractsForUserAndVin = new ArrayList<>(
            warrantyContractClient.findByUserIdAndVinAndDealerId(user.getId(), vin, dealer.getId()).getContent());

        allWarrantyContractsForUserAndVin = allWarrantyContractsForUserAndVin.stream()
               .filter(wc -> !wc.getRemoteRegistration().getRegistrationStatus().equalsIgnoreCase("VOIDED"))
               .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(allWarrantyContractsForUserAndVin)) {
            return confirmationPageModel;
        }

        confirmationPageModel.applyExistingWarrantyContracts(
            allWarrantyContractsForUserAndVin.stream().map(WarrantyContract::from).collect(Collectors.toList()));

        if(premiumsRequestModel != null && premiumsRequestModel.getOdometer() != null) {
            confirmationPageModel.setCarSaverTransaction(premiumsRequestModel.isCarSaverTransaction());
        }

        WarrantyContractView warrantyContract = allWarrantyContractsForUserAndVin.get(0);
        PremiumsRequest premiumsRequest = buildUpgradePremiumsRequestForConfirmationsPage(dealer, user, warrantyContract, premiumsRequestModel);
        boolean hasSingleContractAndIsLifeTime = allWarrantyContractsForUserAndVin.size() == 1 && warrantyContract.isLifetimeWarranty();

        /*
            Only show upgrade premiums if there is one total registration AND it is a "Lifetime Warranty"
            AND
            when the dealer has opted IN for VSC OR if this is a carSaverTransaction=true
         */
        boolean showUpgradePremiums = premiumsRequest != null
            && hasSingleContractAndIsLifeTime
            && (dealer.getEffectivePreferences().getOfferingOptionalVsc() || Boolean.TRUE.equals(premiumsRequest.getCarSaverTransaction()));

        if(showUpgradePremiums) {
            populateUpgradePremiumsForConfirmationsPage(premiumsRequest, confirmationPageModel);
        }

        confirmationPageModel.setShowUpgradePremiums(showUpgradePremiums);

        return confirmationPageModel;
    }

    @PostMapping(value = "/dealer/warranty/{user}/load-enroll-customer-model")
    public EnrollCustomerModel loadEnrollCustomerModel(
        @PathVariable String user,
        @RequestBody EnrollCustomerModelRequest enrollCustomerModelRequest
        ) throws FindSurchargesException, GetFormsException {
        final List<String> dealerIds = enrollCustomerModelRequest.getDealerIds() == null || enrollCustomerModelRequest.getDealerIds().isEmpty()
                ? getDealerIds(session)
                : enrollCustomerModelRequest.getDealerIds();
        validateOnlyOneDealerInRequest(dealerIds);

        List<Form> forms = warrantyService.getForms(new GetFormsRequest(enrollCustomerModelRequest.getScheduleId())).stream()
            .map(Form::from)
            .collect(Collectors.toList());

        List<Surcharge> surcharges = warrantyService.getSurcharges(enrollCustomerModelRequest.toNwanFindChargesRequest()).stream()
            .map(Surcharge::from)
            .collect(Collectors.toList());

        List<VehicleConditionCode> vehicleConditionCodes = Stream.of(com.carsaver.warranty.model.VehicleConditionCode.values())
            .map(VehicleConditionCode::from)
            .collect(Collectors.toList());

        EnrollCustomerModel enrollCustomerModel = new EnrollCustomerModel();
        enrollCustomerModel.setForms(forms);
        enrollCustomerModel.setSurchargesList(surcharges);
        enrollCustomerModel.setVehicleConditionCodes(vehicleConditionCodes);

        return enrollCustomerModel;
    }

    //validates the `applicantRegistration` object
//    @PreAuthorize("hasPermission(#dealer, 'warranty:create')")
    @PostMapping("/dealer/warranty/{userId}/validate-enroll-customer-form")
    public ResponseEntity<?> validateEnrollCustomerForm(
        @PathVariable String userId,
        @Valid @RequestBody ApplicantRegistration applicantRegistration
    ) {
        final List<String> dealerIds = getDealerIdsAndCheckPermissions(session, WARRANTY_CREATE_PERMISSION, applicantRegistration.getDealerIds());
        validateOnlyOneDealerInRequest(dealerIds);
        return ResponseEntity.ok().build();
    }

    //    @PreAuthorize("hasPermission(#dealer, 'warranty:create')")
    @PostMapping("/dealer/warranty/{user}/register-applicant")
    public WarrantyContract registerApplicant(
        @PathVariable UserView user,
        @Valid @RequestBody ApplicantRegistration applicantRegistration
    ) throws ApplicantRegistrationException {
        final List<String> dealerIds = getDealerIdsAndCheckPermissions(session, WARRANTY_CREATE_PERMISSION, applicantRegistration.getDealerIds());
        validateOnlyOneDealerInRequest(dealerIds);
        final DealerView dealer = dealerClient.findById(dealerIds.get(0));

        //if selected premium is a "giveaway" aka Lifetime warranty,
        checkAllowedToRegisterApplicant(dealer, user, applicantRegistration);

        createDealerLink(dealer, user.getId());

        WarrantyContractView contractView = buildWarrantyContractViewFrom(applicantRegistration, dealer, user.getId());
        WarrantyContractView savedWarrantyContract = warrantyService.registerApplicant(contractView);
        log.info("registrationId = {}", savedWarrantyContract.getRegistrationId());

        return WarrantyContract.from(savedWarrantyContract);
    }

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(VehicleNotFoundException.class)
    public ErrorResponse handleVehicleNotFoundException(VehicleNotFoundException vehicleNotFoundException) {
        log.error("premiums search exception", vehicleNotFoundException);

        return ErrorResponse.builder()
            .timestamp(System.currentTimeMillis())
            .status(HttpStatus.BAD_REQUEST.value())
            .error(vehicleNotFoundException.getMessage())
            .exception(vehicleNotFoundException.getClass().getName())
            .type("PREMIUMS_EXCEPTION")
            .build();
    }

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(ApplicantRegistrationException.class)
    public ErrorResponse handleApplicantRegistrationException(ApplicantRegistrationException applicantRegistrationException) {
        log.error("applicant Registration Exception", applicantRegistrationException);

        return ErrorResponse.builder()
            .timestamp(System.currentTimeMillis())
            .status(HttpStatus.BAD_REQUEST.value())
            .error(applicantRegistrationException.getMessage())
            .exception(applicantRegistrationException.getClass().getName())
            .type("ENROLLMENT_EXCEPTION")
            .build();
    }

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(LifetimeWarrantyAlreadyRegisteredException.class)
    public ErrorResponse handleLifetimeWarrantyAlreadyRegisteredException(LifetimeWarrantyAlreadyRegisteredException lifetimeWarrantyAlreadyRegisteredException) {
        log.error("applicant registration exception", lifetimeWarrantyAlreadyRegisteredException);

        return ErrorResponse.builder()
            .timestamp(System.currentTimeMillis())
            .status(HttpStatus.BAD_REQUEST.value())
            .error(lifetimeWarrantyAlreadyRegisteredException.getMessage())
            .exception(lifetimeWarrantyAlreadyRegisteredException.getClass().getName())
            .type("LIFETIME_WARRANTY_ALREADY_REGISTERED")
            .build();
    }

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(PremiumUpgradeAlreadyRegisteredException.class)
    public ErrorResponse handlePremiumUpgradeAlreadyRegisteredException(PremiumUpgradeAlreadyRegisteredException premiumUpgradeAlreadyRegisteredException) {
        log.error("applicant registration exception", premiumUpgradeAlreadyRegisteredException);

        return ErrorResponse.builder()
            .timestamp(System.currentTimeMillis())
            .status(HttpStatus.BAD_REQUEST.value())
            .error(premiumUpgradeAlreadyRegisteredException.getMessage())
            .exception(premiumUpgradeAlreadyRegisteredException.getClass().getName())
            .type("PREMIUM_UPGRADE_ALREADY_REGISTERED")
            .build();
    }

    private PremiumsRequest buildUpgradePremiumsRequestForConfirmationsPage(
        DealerView dealer,
        UserView user,
        WarrantyContractView warrantyContract,
        PremiumsRequestModel premiumsRequestModel) {

        Registration registration = warrantyContract.getRemoteRegistration();
        com.carsaver.warranty.model.Vehicle vehicle = warrantyContract.getVehicle();
        com.carsaver.warranty.model.ApplicantRegistration applicantRegistration = warrantyContract.getApplicantRegistration();
        /*
        PremiumsSearch page may have auto-redirected to confirmations page if the system found a Lifetime warranty for the customer/vin combo
        entered. If that's the case, use the premiumsRequest populated in the model via a Flash Attribute
         */
        PremiumsRequest premiumsRequest = Optional.ofNullable(premiumsRequestModel)
              .filter(prm -> premiumsRequestModel.getOdometer() != null)
              .map(prm -> helper.toPremiumsRequest(premiumsRequestModel, dealer, user))
              .orElse(null);

        if(premiumsRequest != null) {
            applicantRegistration.setCarSaverTransaction(premiumsRequest.getCarSaverTransaction());
        } else {
            premiumsRequest = new PremiumsRequest(dealer.getDealerId());
            premiumsRequest.setOdometer(vehicle.getOdometer());
            premiumsRequest.setVin(vehicle.getVin());

            Date purchaseDate = null;
            if(registration != null) {//favor registration as the source of truth for this information
                String purchaseDateStr = registration.getAgreementPurchaseDate();
                try {//NOTE - if this fails, will be set in onCreate() in meridian
                    DateTimeFormatter DTF = DateTimeFormatter.ofPattern(WarrantyContractView.REGISTRATION_DATE_FMT);
                    LocalDateTime ldt = LocalDateTime.parse(purchaseDateStr, DTF);
                    purchaseDate = Date.from(ldt.atZone(ZoneId.systemDefault()).toInstant());
                } catch(Exception e) {
                    log.error("error setting registration date=" + purchaseDateStr + " using format= " + WarrantyContractView.REGISTRATION_DATE_FMT , e);
                }
                //fall back to applicantRegistration data
            } else if(applicantRegistration != null && applicantRegistration.getAgreement() != null) {
                purchaseDate = warrantyContract.getApplicantRegistration().getAgreement().getPurchaseDate();
            } else {
                log.warn("could not find vehicle purchase date, cannot request premiums");
                return null;
            }
            premiumsRequest.setPurchaseDate(purchaseDate);

            /*  5/22/2018
                no applicantRegistration means it was likely created via a NWAN_POSTBACK
                Per Dan from NWAN:
                If a Lifetime Warranty comes through from a menu into your system and then, at a later time, you upgrade the customer with a VSC on the CarSaver side (not through the menu)
                then, yes, you would send us the IsCarSaverTransaction flag with a TRUE value so that the financials on the VSC are correct.
             */
            boolean isCarSaverTransaction;
            if(warrantyContract.getCreatedSource() == CreatedSource.NWAN_POSTBACK || applicantRegistration == null) {
                isCarSaverTransaction = true;
            } else { //otherwise use the value previously associated with the warranty(also sanity check that this user is allowed to send a carSaverTransaction=true)
                isCarSaverTransaction = warrantyContract.isCarSaverTransaction() && SecurityUtils.isAdminUser();
            }
            premiumsRequest.setCarSaverTransaction(isCarSaverTransaction);

            if(user != null && user.tagExists(WarrantyControllerHelper.USER_TAGS_WALMART_ASSOCIATE)) {
                premiumsRequest.setWalmartEmployee(true);
            }
        }

        return premiumsRequest;
    }

    private void populateUpgradePremiumsForConfirmationsPage(
        PremiumsRequest premiumsRequest,
        ConfirmationPageModel confirmationPageModel) {

        List<Premium> upgradePremiumsList = new ArrayList<>();
        try {

            List<com.carsaver.warranty.model.Premium> nwanPremiums = warrantyService.getPremiums(premiumsRequest);
            upgradePremiumsList = IntStream
                .range(0, nwanPremiums.size())
                .mapToObj(i -> Premium.from(nwanPremiums.get(i), i))
                .filter(premium -> !premium.isGiveAway())
                .collect(Collectors.toList());

        } catch(PremiumsRequestException e) {
            log.error("error getting upgrade premiums", e);

            confirmationPageModel.setErrorMessage("error getting upgrade premiums");
        }
            /*
                if premiums are empty add some troubleshooting information to the screen
             */
        if(org.springframework.util.CollectionUtils.isEmpty(upgradePremiumsList)) {
            String premiumsRequestParams = premiumsRequest.toString();
            try {
                premiumsRequestParams = String.format("?carSaverDealerId=%s&vin=%s&odometer=%s&purchaseDate=%s&IsCarSaverTransaction=%s",
                    premiumsRequest.getCarSaverDealerId(),premiumsRequest.getVin(), premiumsRequest.getOdometer(),
                    new SimpleDateFormat("yyyy-MM-dd").format(premiumsRequest.getPurchaseDate()),
                    premiumsRequest.getCarSaverTransaction()
                );

            } catch(Exception e) {
                log.error("error parsing premiumsRequest to params", e);
            }

            confirmationPageModel.setPremiumsRequestParams(premiumsRequestParams);
        }

        confirmationPageModel.setPremiums(upgradePremiumsList);
    }

    private void createDealerLink(DealerView dealer, String userId) {
        DealerLinkRequest dealerLinkRequest = DealerLinkRequest.builder()
            .dealerId(dealer.getId())
            .userId(userId)
            .source(sourceCreator.createSource())
            .build();
        dealerLinkClient.create(dealerLinkRequest);
    }

    private WarrantyContractView buildWarrantyContractViewFrom(ApplicantRegistration applicantRegistration, DealerView dealer, String userId) {
        WarrantyContractView lifetimeWarrantyContract = new WarrantyContractView();
        lifetimeWarrantyContract.setApplicantRegistration(applicantRegistration.toNwanApplicantRegistration(providerCode));
        lifetimeWarrantyContract.setDealerId(dealer.getId());
        String vin = applicantRegistration.getVehicle() != null ? applicantRegistration.getVehicle().getVin() : null;
        lifetimeWarrantyContract.setVin(vin);
        lifetimeWarrantyContract.setUserId(userId);
        lifetimeWarrantyContract.setSelectedPremium(
            Optional.ofNullable(applicantRegistration.getSelectedPremium()).map(Premium::toNwanPremium).orElse(null));

        return lifetimeWarrantyContract;
    }

}
