application:
  domain: http://localhost:${server.port}
server:
  port: 3001
  servlet:
    session:
      cookie:
        name: UISESSION
      timeout: 24H
spring:
  redis:
    host: localhost
    port: 6379
  security:
    oauth2:
      client:
        registration:
          carsaver:
            client-id: PORTAL
            client-secret: -Ku2Q_FCN2
  session:
    redis:
      namespace: spring:atlas:local:session
logging:
  level:
    com.carsaver.magellan.client: DEBUG
    # Trace logging for Elasticsearch client. Uses "tracer" package for some reason
    tracer: TRACE
    root: INFO
    org.springframework.web: DEBUG
    com.amazonaws.util.EC2MetadataUtils: error
    com.carsaver.partner.configuration: DEBUG # Set the package for your Feign clients
    feign:
      client:
        config:
          default: # Use 'default' for all clients or specify client name
            loggerLevel: DEBUG # Options: NONE, BASIC, HEADERS, FULL
warranty-service:
  nwan-client:
    rootUri: https://qvcarsaver.naenwan.com/eBusinessApi
    username: TCS
    password: zcxUUvLJPj7QdycsQ2hGfTMcEh9N6r
  providerCode: TCS
  e-remittance-portal:
    url: https://qvcarsaver.naenwan.com/Portals/eBusiness/eRemittancePortal.aspx?oauth_token=
    role: f5b2da92-1811-4b0a-9f82-5e0b507b5077
  supply-order-portal:
    url: https://qvcarsaver.naenwan.com/Portals/Organizations/OrderSupplies.aspx?oauth_token=
    role: 5d0b33ad-6a7b-4943-8331-15be54d61a7a
twilio:
  accountId: **********************************
  authToken: 8cf701badf19f0aed13451a3fc92afdb
  number: +***********
routeone:
  base-uri: https://testint.r1dev.com
  partner-id: F00CSV
  sso:
    password: csv$%*HBNF3ed+
  protection-products:
    feature-r-id: 2289A234-1E4D-4E80-84C6-F7853596BEB5
portal:
  authorizationUri: http://localhost:3000/oauth2/authorize
atlas:
  features: dealer-user-vehicle-quote
atc:
  rootUri: https://svc.autotitling.com/AGFService/v1.0/
  userToken: Q2FyU2F2ZXJBR0Y6b2dFbzM1Y0JPbXJ3a3RGRWtPa2xRS1VhS1FYVEdibHVObTU1Zm1rYkJndz0=
  requestGranularity: 0
program:
  nissan-buy-at-home-id: 5e922fe4-e1e9-468c-b100-5b8f7cffcef3
features-toggle:
  pre-approval-enable: true
  accessories-enable: true
  ariya-reservation-enable: true
  routeone-prefix-enable: true
  route-one-finance-and-insurance-enable: true
  buy-at-home-program-enable: true
  protection-products-enabled: true
  liberty-mutual-enable: true
nissan-client:
  apigee:
    baseUri: https://dev.api.na.nissancloud.com
    token-api: #used by payoff,incentives,etc., for oauth2 client_credentials grant types
      username: qFNzhiBI20ng4kmyLADhnhCbIuGebAtp
      password: aeVrUOUQkVZqQb4x
    dealer-inventory:
      rootUri: https://dev.api.na.nissancloud.com
  accessories:
    rootUri: https://dev.api.na.nissancloud.com
    service:
      client:
        rootUri: http://localhost:8080/accessories
features-subscription:
  rootUri: https://api-beta.carsaver.com/meridian/features-subscription
  liberty-mutual-feature-id: 023f5087-01bb-4755-961d-89b0ea8e15c5
  routeone-f-and-i-feature-id: 2289A234-1E4D-4E80-84C6-F7853596BEB5
  sell-at-home-feature-id: 11bcd5c0-2292-4089-9265-e95d1852bfd4
  nesna-f-and-i-feature-id: 76c3c23b-2b1e-4822-bd26-9ef735820641
  garage-alerts-feature-id: e3850360-96cd-4046-bd0c-0802ba9078e7
  sms_alerts-feature-id: f4ef0ad8-6cdc-44b0-87f4-c5f5d4c0386c
  in-app-alerts-feature-id: d0baa546-9c38-4951-a0ee-c3f7c89079a2
  email-alerts-feature-id: 09546078-30f4-4a21-b270-64a2ec93659a
  spanish-translation-feature-id: 3e9ea5f7-6b01-4419-aefc-3ccd97034ea4
dealer-service:
  api-uri: https://api-beta.carsaver.com/dealer
nissan:
  wiretap-enabled: true
deal-desking:
  inventory-service:
    api-uri: https://api-beta.carsaver.com/inventory
quote-service:
  endpoint-root: https://api-beta.carsaver.com/quote
user-vehicle-service:
  api-uri: https://api-beta.carsaver.com/user-vehicles
lead-service:
  api-uri: https://api-beta.carsaver.com/lead
insurance-service:
  insurance-host: https://api-beta.carsaver.com/insurance
  route-one-uri: /api/routeone/deal-jacket
  route-one-secret-key-name: beta/protection-products/api-keys
  insurance-callback-url: https://nissan-ecommerce.beta.carsaver.com/routeone/thank-you.html
  default-route-one-dealer-id: FH1FG
  route-one-user-id: BHWANG
  route-one-hmac_id: F00CSM
splits:
  key: pbda4t4qifvfrk3j94f8fhg7i1n9lbefq3e0
  defaults:
    atlasDisplayOwnedProspectForDealerUsers: on
    AtlasCustomerPageBMWNewColumns: on
    AtlasCustomerSortFieldsEnabled: on
    AtlasEnhancedNoGroupFilters: on
    AtlasRangeSliderFilters: on
    AtlasSecondPhaseFilters: on
    offerAdjustmentFeatureV2: on
    EnableNESNAF&I: on
    DomoEmbedAtlas: off
    SendToCrmEnhancement: off
    AtlasActionBarEnabled: off
    AtlasRequestScreenShare: off
    atlasCustomerLeadsDisplayAdfEnabled: off
    enableElasticNestedFields: off
    AtlasNewCustomerPageOnlineNow: off
    AtlasStickyFiltersEnabled: off
    EnableGarageAlerts: off
    EnableINAppGarageAlerts: off
    EnableEmailGarageAlerts: off
    EnableSMSGarageAlerts: off
    CustomerTagsEnhancementsToggle: off
    DrawerSettingsEnabled: off
    QrCodeSettingsEnabled: off
    customerInfoCardProgramUserSupportToggle: off
    programUserSupport: off
    AtlasWalmartInventoryEnabled: off
    AtlasEnableLeadFilter: off
    AtlasInAppNotificationEnabled: off
    EnableLanguageSelection: off
client:
  api-host: https://api-beta.carsaver.com

  auth:
    host: ${client.api-host}/uaa/oauth
    auth-username: WEB
    auth-password: 9pA_G7soi9
    token-username: <EMAIL>
    token-password: AahdCzESGsDMDA52

  oauth:
    configurations:
      carsaver:
        url: ${client.api-host}/uaa/oauth/token
        grant-type: PASSWORD
        auth-username: WEB
        auth-password: 9pA_G7soi9
        token-username: <EMAIL>
        token-password: AahdCzESGsDMDA52

  insurance:
    host: http://localhost:${server.port}
    namespace: /insurance
    nesna:
      upsertNesnaUrl: "${client.insurance.host}/upsert"
      listNensaProductsUrl: "https://api.example.com/list-products"
domo:
  client-id: 21e18b92-3d4c-42b4-8d43-4325a11cac63
  client-secret: 9bc9f629161c2992806945db5c1c3b7ebc663b98b87d1788b7456bf30257118b
  grant-type: client_credentials
  scopes: data,workflow,user,dashboard
  embed-id: l5rWr
  api-host: https://api.domo.com
dynamoDB:
  lenderDesk-table: financier-lender-desk-mapping-table-staging
  session-heartbeat-table: digital-retail-session-heartbeat-staging

