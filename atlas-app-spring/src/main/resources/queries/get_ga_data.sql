WITH event_data AS (
    SELECT
        event_date,
        event_name,
        event_timestamp,
        (SELECT param.value.string_value
         FROM UNNEST(event_params) AS param
         WHERE param.key = 'page_location' LIMIT 1) AS page_location,
        (SELECT param.value.string_value
         FROM UNNEST(event_params) AS param
         WHERE param.key = 'page_title' LIMIT 1) AS page_title,
        user_id AS carsaver_user_id,
        user_pseudo_id,
        (SELECT value.int_value FROM UNNEST(event_params) WHERE key = 'ga_session_id') AS ga_session_id,
        (SELECT value.int_value FROM UNNEST(event_params) WHERE key = 'ga_session_number') AS ga_session_number,
        device.category
    FROM `<TABLE_NAME>`
)
SELECT *
FROM event_data
WHERE page_title = 'Search Results - Digital Retail App'
  AND page_location LIKE @dealerId
  AND carsaver_user_id = @userId;
