application:
  domain: https://atlas.carsaver.com
  loginPage: /login
server:
  servlet:
    session:
      timeout: 4H
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024
  forward-headers-strategy: native
  shutdown: graceful
spring:
  redis:
    host: atlas-sessions.3f0onc.ng.0001.use1.cache.amazonaws.com
  session:
    store-type: redis
  application:
    name: atlas-app
  sendgrid:
    api-key: *********************************************************************
  jackson:
    serialization:
      WRITE_DATES_AS_TIMESTAMPS: false
    deserialization:
      READ_UNKNOWN_ENUM_VALUES_USING_DEFAULT_VALUE: true
      ACCEPT_SINGLE_VALUE_AS_ARRAY: true
  main:
    allow-bean-definition-overriding: true
  resources:
    chain:
      strategy:
        content:
          enabled: true
          paths: /**
  security:
    oauth2:
      client:
        registration:
          carsaver:
            authorization-grant-type: password
        provider:
          carsaver:
            token-uri: ${magellen-service-api-uri}/uaa/oauth/token
            authorization-uri: ${magellen-service-api-uri}/uaa/oauth/authorize
            jwk-set-uri: ${magellen-service-api-uri}/uaa/.well-known/jwks.json
  thymeleaf:
    mode: HTML
warranty-service:
  nwan-client:
    rootUri: https://carsaver.naenwan.com/eBusinessApi
    username: PCS
    password: H8W76fgegWdXVJHvnpWxfGFvkUq7yJ
  providerCode: PCS
  e-remittance-portal:
    url: https://carsaver.naenwan.com/Portals/eBusiness/eRemittancePortal.aspx?oauth_token=
    role: f5b2da92-1811-4b0a-9f82-5e0b507b5077
  supply-order-portal:
    url: https://carsaver.naenwan.com/Portals/Organizations/OrderSupplies.aspx?oauth_token=
    role: 5d0b33ad-6a7b-4943-8331-15be54d61a7a
twilio:
  accountId: **********************************
  authToken: 8cf701badf19f0aed13451a3fc92afdb
  number: +***********
#todo - consider moving into spring.security.oauth2.* namespace
portal:
  authorizationUri: https://portal.carsaver.com/oauth2/authorize
  atlasRedirectUri: ${application.domain}/login/oauth2/code/portal
  clientId: portal
carsaver:
  dealJacketServiceEnabled: true
  paymentService:
    enabled: true
  cloud:
    kinesis:
      api-log-stream: api-log-stream-beta
quotes:
  lease:
    defaultDownPaymentPercent: 10
  finance:
    defaultDownPaymentPercent: 20
offerlogix:
  client:
    timeout: 30
    url: https://www.leaseasp.com
  defaultBeaconScore: 750
features-toggle:
  pre-approval-enable: true
  accessories-enable: true
  exclude-test-users: false
  ariya-reservation-enable: false
  chat-enable: true
  routeone-prefix-enable: true
  liberty-mutual-enable: false
  buy-at-home-program-enable: false
nissan-client:
  apigee:
    baseUri: https://api.na.nissancloud.com
    token-api: #used by payoff,incentives,etc., for oauth2 client_credentials grant types
      rootUri: ${nissan-client.apigee.baseUri}/identity/v1
      username: 0ooMGFrIHNh1KwVtOnQA9IWXwtgvjSko
      password: oFFyHtEinOiY8uke
    dealer-inventory:
      rootUri: https://api.na.nissancloud.com
  accessories:
    rootUri: https://api.na.nissancloud.com
quote-service:
  endpoint-root: https://api-beta.carsaver.com/quote
activity-service:
  api-uri: ${magellen-service-api-uri}/activity
finance-deep-link-program-exclusions: >
  d3a45b49-4387-4221-9055-24db52bf2368,
  b257486d-9b9a-46f6-85fd-601c452f7983,
  37ec98e2-c9e3-4a62-8e96-c883c3b91bed,
  eaed72e0-77a8-11ee-b962-0242ac120002,
  1e01da17-8c6b-40f8-bc1b-44093e49866d
splits:
  key: pbda4t4qifvfrk3j94f8fhg7i1n9lbefq3e0
  defaults:
    preQualifiedCtaFeature: off
    dealEditingCash: off
    offerAdjustmentFeature: off
    tradeInAdjustmentMultipleDealersFeature: off
    sellAtHomeFeature: off
    atlasDisplayOwnedProspectForDealerUsers: off
    NewConfigManager: off
    LMSPreferenceFeature: off
    Announcekit-atlas: off
    enabled_overlay_wide_view: off
    AtlasCustomerSortFieldsEnabled: off
    AtlasEnhancedNoGroupFilters: off
    AtlasRangeSliderFilters: off
    AtlasSecondPhaseFilters: off
    AtlasCustomerPageBMWNewColumns: off
    enableAtlasTradePurchaseFields: off
    EnableNESNAF&I: off
    nesnaFAndIFeature: off
    offerAdjustmentFeatureV2: off
    AtlasLoginSanitizationEnable: off
    DomoEmbedAtlas: off
    AtlasDealerTrackPhase2Enabled: off
    SendToCrmEnhancement: off
    AtlasNewCustomerPage: off
    AtlasActionBarEnabled: off
    AdaptiveRetailPlayground: off
    AtlasRequestScreenShare: off
    ImportCarLogsByDealerV2: off
    atlasCustomerLeadsDisplayAdfEnabled: off
    enableElasticNestedFields: off
    AtlasNewCustomerPageOnlineNow: off
    AtlasNewCustomerPageInShowroom: off
    AtlasStickyFiltersEnabled: off
    EnableGarageAlerts: off
    EnableINAppGarageAlerts: off
    EnableEmailGarageAlerts: off
    EnableSMSGarageAlerts: off
    CustomerTagsEnhancementsToggle: off
    DrawerSettingsEnabled: off
    QrCodeSettingsEnabled: off
    customerInfoCardProgramUserSupportToggle: off
    programUserSupport: off
    AtlasWalmartInventoryEnabled: off
    AtlasEnableLeadFilter: off
    AtlasInAppNotificationEnabled: off
    EnableLanguageSelection: off
    FinanceNegativeBug: off
    EnableCarsaverFandI: off
    AtlasNewCustomerDetailsPageCurrentVehicles: off
    EnableARContracts: off
    AtlasNewCustomerPageCurrentInshowRoomV2: off
    EnableARFinanceApp: off
    EnableARActivity: off
    EnableARSearches: off
    enableOverlaySettings: off
    EnableCheckInSalesPerson: off
    EnableDealerRateSheet: off
    EnableSMSOnlineNow: off
    AtlasLeadTypeConfig: off

insurance-service:
  insurance-host: https://api-beta.carsaver.com/insurance
  route-one-uri: /api/routeone/deal-jacket
  route-one-secret-key-name: local/protection-products/api-keys
  insurance-callback-url: http://localhost-nissan:4000/routeone/thank-you.html
  default-route-one-dealer-id: FH1FG
  route-one-user-id: BHWANG
  route-one-hmac_id: F00CSM
service-api-uri: https://api-beta.carsaver.com
configuration-service:
  api-uri: ${magellen-service-api-uri}/configuration
dynamoDB:
  dealerTable: upgrade_prospect_etl_dealer_staging
  lenderDesk-table: financier-lender-desk-mapping-table-staging
  session-heartbeat-table: digital-retail-session-heartbeat-staging
  prospect-leads-current-table: prospect-leads-current-beta
  prequal-history-table: prequal-transaction-history-staging

digital-retail:
  otp-api-uri: ${service-api-uri}/digital-retail/logins/dealer-otps
  campaign-id: 89344995-3a83-4ddf-98d6-j382kd92k3lf

domo:
  client-id: 632825b8-fd94-4f0c-8200-c2e8d55444c9
  client-secret: d1bd1df8b7f992fb693f97928c8236b8e38cca562e1c9266e682c7fac80048e9
  grant-type: client_credentials
  scopes: data,audit,user,dashboard
  embed-id: l5rWr
  api-host: https://api.domo.com

finance-service:
  root-uri: https://api-beta.carsaver.com/finance

export:
  customer:
    page-size: 100
client:
  api-host: https://api-beta.carsaver.com

  auth:
    host: ${client.api-host}/uaa/oauth
    auth-username: WEB
    auth-password: 9pA_G7soi9
    token-username: <EMAIL>
    token-password: AahdCzESGsDMDA52

  oauth:
    configurations:
      carsaver:
        url: ${client.api-host}/uaa/oauth/token
        grant-type: PASSWORD
        auth-username: WEB
        auth-password: 9pA_G7soi9
        token-username: <EMAIL>
        token-password: AahdCzESGsDMDA52
program:
  walmart-id: f4f8d14f-b0d1-404a-90f4-3f40d4ba9940
features-subscription:
  carsaver-f-and-i-feature-id: bc9dd58b-01d6-4707-bfd3-f1788cb8706a
  lms-feature-id: c45bb173-dd94-42c1-b778-6736ac4e0e2c
  boost-features-feature-id: b41d832f-0d61-41fe-958c-ecf65b407c47
in-showroom:
  minutes-threshold: 10
  lead-hour-threshold: 24

app:
  secrets: services/atlas-app/beta

vehicle-searches:
  table-name: ga4-eecu.analytics_430636697.events_*
