{"page": "Client Theming", "sections": [{"title": "Top Navigation Bar", "description": "This determines how the top navigation bar is displayed on your website.", "components": [{"title": {"key": "websiteTheming/clientTheming/topNavigationBar/colors/title", "type": "LABEL", "value": "Colors"}, "documentation": {"key": "websiteTheming/clientTheming/topNavigationBar/colors/documentation", "type": "VIDEO", "url": null}, "inputs": [{"key": "navigationBarActive", "value": false, "type": "ENABLED_GROUP", "label": "Use Custom Colors", "group": [{"key": "navigationBarBackgroundColorVal", "type": "INPUT_COLOR", "placeholder": "#FFFFFF", "hint": "#FFFFFF", "label": "Background Color"}, {"key": "navigationBarTextColorVal", "type": "INPUT_COLOR", "placeholder": "#2E2E2E", "hint": "#2E2E2E", "label": "Text Color"}]}]}]}, {"title": "Accent Color", "description": "This determines the accent color used in the section header icons and other elements of your website. ", "components": [{"title": {"key": "websiteTheming/clientTheming/accentColor/title", "type": "LABEL", "value": "Colors"}, "description": {"key": "websiteTheming/clientTheming/accentColor/description", "type": "LABEL", "value": "This determines the accent color used in the section header icons and other elements of your website. "}, "documentation": {"key": "websiteTheming/clientTheming/accentColor/documentation", "type": "VIDEO", "url": null}, "inputs": [{"key": "accentValuesActive", "value": false, "type": "ENABLED_GROUP", "label": "Use Custom Colors", "group": [{"key": "accentValuesColorVal", "type": "INPUT_COLOR", "placeholder": "#2E2E2E", "hint": "#535353", "label": "Accent Color"}]}]}]}, {"title": "Deal Guide (CTAs Anchored To Bottom of My Deal Page)", "description": "This determines the button and text colors used in the CTAs of the Deal Guide section. The Deal Guide navigates users through your specified sales process.", "components": [{"title": {"key": "websiteTheming/clientTheming/dealGuide/cta/bottom/primary/title", "type": "LABEL", "value": "Primary CTA Button", "postText": "(Bottom Right)"}, "documentation": {"key": "websiteTheming/clientTheming/dealGuide/cta/bottom/primary/documentation", "type": "VIDEO", "url": null}, "inputs": [{"key": "firstButtonActive", "value": false, "type": "ENABLED_GROUP", "label": "Use Custom Colors", "group": [{"key": "firstButtonBackgroundColorVal", "type": "INPUT_COLOR", "placeholder": "#FFFFFF", "hint": "#FFFFFF", "label": "Background Color"}, {"key": "firstButtonHighlightRolloverBackgroundColorVal", "type": "INPUT_COLOR", "placeholder": "#FFFFFF", "hint": "#FFFFFF", "label": "Highlight/Rollover | Background Color (Desktop Only)"}, {"key": "firstButtonTextColorVal", "type": "INPUT_COLOR", "placeholder": "#2E2E2E", "hint": "#2E2E2E", "label": "Text Color"}, {"key": "firstButtonHighlightRolloverTextColorVal", "type": "INPUT_COLOR", "placeholder": "#2E2E2E", "hint": "#2E2E2E", "label": "Highlight/Rollover | Text Color (Desktop Only)"}, {"key": "firstButtonBorderColorVal", "type": "INPUT_COLOR", "placeholder": "#D3D3D3", "hint": "#D3D3D3", "label": "Border Color"}, {"key": "firstButtonHighlightRolloverBorderColorVal", "type": "INPUT_COLOR", "placeholder": "#D3D3D3", "hint": "#D3D3D3", "label": "Highlight/Rollover | Border Color (Desktop Only)"}]}]}, {"title": {"key": "websiteTheming/clientTheming/dealGuide/cta/bottom/secondary/title", "type": "LABEL", "value": "CTA Button #2", "postText": "(Bottom Middle)"}, "documentation": {"key": "websiteTheming/clientTheming/dealGuide/cta/bottom/secondary/documentation", "type": "VIDEO", "url": null}, "inputs": [{"key": "secondButtonActive", "value": false, "type": "ENABLED_GROUP", "label": "Use Custom Colors", "group": [{"key": "secondButtonBackgroundColorVal", "type": "INPUT_COLOR", "placeholder": "#FFFFFF", "hint": "#FFFFFF", "label": "Background Color"}, {"key": "secondButtonHighlightRolloverBackgroundColorVal", "type": "INPUT_COLOR", "placeholder": "#FFFFFF", "hint": "#FFFFFF", "label": "Highlight/Rollover | Background Color (Desktop Only)"}, {"key": "secondButtonTextColorVal", "type": "INPUT_COLOR", "placeholder": "#2E2E2E", "hint": "#2E2E2E", "label": "Text Color"}, {"key": "secondButtonHighlightRolloverTextColorVal", "type": "INPUT_COLOR", "placeholder": "#2E2E2E", "hint": "#2E2E2E", "label": "Highlight/Rollover | Text Color (Desktop Only)"}, {"key": "secondButtonBorderColorVal", "type": "INPUT_COLOR", "placeholder": "#D3D3D3", "hint": "#D3D3D3", "label": "Border Color"}, {"key": "secondButtonHighlightRolloverBorderColorVal", "type": "INPUT_COLOR", "placeholder": "#D3D3D3", "hint": "#D3D3D3", "label": "Highlight/Rollover | Border Color (Desktop Only)"}]}]}, {"title": {"key": "websiteTheming/clientTheming/dealGuide/cta/bottom/tertiary/title", "type": "LABEL", "value": "CTA Button #3", "postText": "(Bottom Left)"}, "documentation": {"key": "websiteTheming/clientTheming/dealGuide/cta/bottom/tertiary/documentation", "type": "VIDEO", "url": null}, "inputs": [{"key": "thirdButtonActive", "value": false, "type": "ENABLED_GROUP", "label": "Use Custom Colors", "group": [{"key": "thirdButtonBackgroundColorVal", "type": "INPUT_COLOR", "placeholder": "#FFFFFF", "hint": "#FFFFFF", "label": "Background Color"}, {"key": "thirdButtonHighlightRolloverBackgroundColorVal", "type": "INPUT_COLOR", "placeholder": "#FFFFFF", "hint": "#FFFFFF", "label": "Highlight/Rollover | Background Color (Desktop Only)"}, {"key": "thirdButtonTextColorVal", "type": "INPUT_COLOR", "placeholder": "#2E2E2E", "hint": "#2E2E2E", "label": "Text Color"}, {"key": "thirdButtonHighlightRolloverTextColorVal", "type": "INPUT_COLOR", "placeholder": "#2E2E2E", "hint": "#2E2E2E", "label": "Highlight/Rollover | Text Color (Desktop Only)"}, {"key": "thirdButtonBorderColorVal", "type": "INPUT_COLOR", "placeholder": "#D3D3D3", "hint": "#D3D3D3", "label": "Border Color"}, {"key": "thirdButtonHighlightRolloverBorderColorVal", "type": "INPUT_COLOR", "placeholder": "#D3D3D3", "hint": "#D3D3D3", "label": "Highlight/Rollover | Border Color (Desktop Only)"}]}]}]}]}