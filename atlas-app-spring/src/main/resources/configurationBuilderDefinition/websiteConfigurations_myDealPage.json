{"page": "My Deal Page", "sections": [{"title": "Payment Options", "description": "This determines how payment options are displayed to users on your website.", "components": [{"title": {"key": "paymentOptionsTitle", "type": "LABEL", "value": "Display"}, "description": {"key": "paymentOptionsDescription", "type": "LABEL", "value": "This determines the section title shown for the payment options section of the My Deal Page."}, "inputs": [{"key": "paymentOptionsSectionTitle", "type": "INPUT", "hint": "This is displayed in the left menu, the collapsed version of the page section and in the primary CTA of the deal guide.", "description": "This is displayed in the left menu, the collapsed version of the page section and in the primary CTA of the deal guide.", "label": "Section Title"}, {"key": "paymentOptionsDisplayTimeToComplete", "type": "ENABLED_GROUP", "label": "Display Time To Complete", "group": [{"key": "paymentOptionsTimeToComplete", "type": "TIME_DURATION", "placeholder": "1 min", "label": "Time To Complete"}]}]}, {"title": {"key": "paymentOptionsPaymentMethodsDisplayedTitle", "type": "LABEL", "value": "Payments Methods Displayed"}, "description": {"key": "paymentOptionsPaymentMethodsDisplayedDescription", "type": "LABEL", "value": "Select the payment methods you would like displayed on your website."}, "inputs": [{"key": "paymentOptionsPaymentMethodsDisplayed", "type": "CHECKBOX", "allowedValues": [{"value": "FINANCE", "text": "Finance"}, {"value": "LEASE", "text": "Lease"}, {"value": "CASH", "text": "Cash"}]}]}], "order": "1"}, {"title": "Rebates & Incentives", "description": "This determines how rebates & incentives are displayed to users on your website.", "components": [{"title": {"key": "rebatesAndIncentivesTitle", "type": "LABEL", "value": "Display"}, "description": {"key": "rebatesAndIncentivesDescription", "type": "LABEL", "value": "This determines the section title, header title and header sub-title shown on the rebates & incentives section of the My Deal Page."}, "inputs": [{"key": "rebatesAndIncentivesDisplaySection", "type": "ENABLED_GROUP", "label": "Display Section", "value": true, "group": [{"key": "rebatesAndIncentivesSectionTitle", "type": "INPUT", "hint": "This is displayed in the left menu, the collapsed version of the page section and in the primary CTA of the deal guide.", "description": "This is displayed in the left menu, the collapsed version of the page section and in the primary CTA of the deal guide.", "label": "Section Title"}, {"key": "rebatesAndIncentivesHeaderTitle", "type": "INPUT", "hint": "This is displayed under the icon/header image.", "description": "This is displayed under the icon/header image.", "label": "Header Title"}, {"key": "rebatesAndIncentivesHeaderSubTitle", "type": "INPUT", "hint": "This is displayed under the header title.", "description": "This is displayed under the header title.", "label": "Header Sub-Title"}, {"key": "rebatesAndIncentivesDisplayTimeToComplete", "type": "ENABLED_GROUP", "label": "Display Time To Complete", "group": [{"key": "rebatesAndIncentivesTimeToComplete", "type": "TIME_DURATION", "placeholder": "2 min", "label": "Time To Complete"}]}]}]}, {"title": {"key": "rebatesAndIncentivesRebateCategoriesTitle", "type": "LABEL", "value": "Rebate Categories"}, "description": {"key": "rebatesAndIncentivesRebateCategoriesDescription", "type": "LABEL", "value": "This determines the rebate categories displayed in the rebates & incentives section of the My Deal Page."}, "inputs": [{"key": "rebatesAndIncentivesRebateCategories", "type": "CHECKBOX", "allowedValues": [{"value": "CONDITIONAL", "text": "Conditional"}, {"value": "ADDITIONAL", "text": "Additional"}]}]}], "order": "2"}, {"title": "Trade-In", "description": "This determines how the trade-in section of the My Deal Page is displayed to users on your website.", "components": [{"title": {"key": "tradeInTitle", "type": "LABEL", "value": "Display"}, "description": {"key": "tradeInDescription", "type": "LABEL", "value": "This determines the section title, header title and header sub-title shown on the trade-in section of the My Deal Page."}, "inputs": [{"key": "tradeInDisplaySection", "type": "ENABLED_GROUP", "label": "Display Section", "value": true, "group": [{"key": "tradeInSectionTitle", "type": "INPUT", "hint": "This is displayed in the left menu, the collapsed version of the page section and in the primary CTA of the deal guide.", "description": "This is displayed in the left menu, the collapsed version of the page section and in the primary CTA of the deal guide.", "label": "Section Title"}, {"key": "tradeInHeaderTitle", "type": "INPUT", "hint": "This is displayed under the icon/header image.", "description": "This is displayed under the icon/header image.", "label": "Header Title"}, {"key": "tradeInHeaderSubTitle", "type": "INPUT", "hint": "This is displayed under the header title.", "description": "This is displayed under the header title.", "label": "Header Sub-Title"}, {"key": "tradeInDisplayTimeToComplete", "type": "ENABLED_GROUP", "label": "Display Time To Complete", "group": [{"key": "tradeInTimeToComplete", "type": "TIME_DURATION", "placeholder": "3 min", "label": "Time To Complete"}]}]}]}], "order": "3"}, {"title": "Accessories", "description": "This determines how the accessories section on the My Deal Page is displayed to users on your website.", "components": [{"title": {"key": "accessoriesTitle", "type": "LABEL", "value": "Display"}, "description": {"key": "accessoriesDescription", "type": "LABEL", "value": "This determines the section title, header title and header sub-title shown on the accessories section of the My Deal Page."}, "inputs": [{"key": "accessoriesDisplaySection", "type": "ENABLED_GROUP", "label": "Display Section", "value": true, "group": [{"key": "accessoriesSectionTitle", "type": "INPUT", "hint": "This is displayed in the left menu, the collapsed version of the page section and in the primary CTA of the deal guide.", "description": "This is displayed in the left menu, the collapsed version of the page section and in the primary CTA of the deal guide.", "label": "Section Title"}, {"key": "accessoriesHeaderTitle", "type": "INPUT", "hint": "This is displayed under the icon/header image.", "description": "This is displayed under the icon/header image.", "label": "Header Title"}, {"key": "accessoriesHeaderSubTitle", "type": "INPUT", "hint": "This is displayed under the header title.", "description": "This is displayed under the header title.", "label": "Header Sub-Title"}, {"key": "accessoriesDisplayTimeToComplete", "type": "ENABLED_GROUP", "label": "Display Time To Complete", "group": [{"key": "accessoriesTimeToComplete", "type": "TIME_DURATION", "placeholder": "2 min", "label": "Time To Complete"}]}]}]}], "order": "4"}, {"title": "F&I Products", "description": "This determines the section title, header title and header sub-title shown on the F&I products section of the My Deal Page.", "components": [{"title": {"key": "f&IProductsTitle", "type": "LABEL", "value": "Display"}, "description": {"key": "f&IProductsDescription", "type": "LABEL", "value": "This determines the section title, header title and header sub-title shown on the F&I products section of the My Deal Page."}, "inputs": [{"key": "protectionProductsDisplaySection", "type": "ENABLED_GROUP", "label": "Display Section", "value": true, "group": [{"key": "protectionProductsSectionTitle", "type": "INPUT", "hint": "This is displayed in the left menu, the collapsed version of the page section and in the primary CTA of the deal guide.", "description": "This is displayed in the left menu, the collapsed version of the page section and in the primary CTA of the deal guide.", "label": "Section Title"}, {"key": "protectionProductsHeaderTitle", "type": "INPUT", "hint": "This is displayed under the icon/header image.", "description": "This is displayed under the icon/header image.", "label": "Header Title"}, {"key": "protectionProductsHeaderSubTitle", "type": "INPUT", "hint": "This is displayed under the header title.", "description": "This is displayed under the header title.", "label": "Header Sub-Title"}, {"key": "protectionProductsDisplayTimeToComplete", "type": "ENABLED_GROUP", "label": "Display Time To Complete", "group": [{"key": "protectionProductsTimeToComplete", "type": "TIME_DURATION", "placeholder": "2 min", "label": "Time To Complete"}]}]}]}], "order": "5"}, {"title": "Financing", "description": "This determines how the financing options section on the My Deal Page are displayed to users on your website.", "components": [{"title": {"key": "financingTitle", "type": "LABEL", "value": "Display"}, "description": {"key": "financingDescription", "type": "LABEL", "value": "This determines the section title, header title and header sub-title shown on the financing section of the My Deal Page."}, "inputs": [{"key": "financingDisplaySection", "type": "ENABLED_GROUP", "label": "Display Section", "group": [{"key": "financingSectionTitle", "type": "INPUT", "hint": "Financing", "label": "Section Title", "description": "This is displayed in the left menu, the collapsed version of the page section and in the primary CTA of the deal guide."}, {"key": "financingHeaderTitle", "type": "INPUT", "hint": "Finance Options", "label": "Header Title", "description": "This is displayed under the icon/header image."}, {"key": "financingHeaderSubTitle", "type": "INPUT", "hint": "Save up to 30 minutes at the dealership by getting pre-approved online", "label": "Header Sub-Title", "description": "This is displayed under the header title."}, {"key": "financingDisplayTimeToComplete", "type": "ENABLED_GROUP", "label": "Display Time To Complete", "group": [{"key": "financingTimeToComplete", "type": "TIME_DURATION", "placeholder": "2 min", "label": "Time To Complete"}]}]}]}], "order": "6"}, {"title": "Contracting & Delivery", "description": "This determines how the contracting & delivery options section on the My Deal Page are displayed to users on your website.", "components": [{"title": {"key": "contractingAndDeliveryTitle", "type": "LABEL", "value": "Display"}, "description": {"key": "contractingAndDeliveryDescription", "type": "LABEL", "value": "This determines the section title, header title and header sub-title shown on the contracting & delivery section of the My Deal Page."}, "inputs": [{"key": "contractingAndDeliveryDisplaySection", "type": "ENABLED_GROUP", "label": "Display Section", "group": [{"key": "contractingAndDeliverySectionTitle", "type": "INPUT", "hint": "This is displayed in the left menu, the collapsed version of the page section and in the primary CTA of the deal guide.", "description": "This is displayed in the left menu, the collapsed version of the page section and in the primary CTA of the deal guide.", "label": "Section Title"}, {"key": "contractingAndDeliveryHeaderTitle", "type": "INPUT", "hint": "This is displayed under the icon/header image.", "description": "This is displayed under the icon/header image.", "label": "Header Title"}, {"key": "contractingAndDeliveryHeaderSubTitle", "type": "INPUT", "hint": "This is displayed under the header title.", "description": "This is displayed under the header title.", "label": "Header Sub-Title"}, {"key": "contractingAndDeliveryDisplayTimeToComplete", "type": "ENABLED_GROUP", "label": "Display Time To Complete", "group": [{"key": "contractingAndDeliveryTimeToComplete", "type": "TIME_DURATION", "placeholder": "2 min", "label": "Time To Complete"}]}]}]}], "order": "7"}, {"title": "Upload Documents", "description": "This determines how the upload documents section on the My Deal Page are displayed to users on your website.", "components": [{"title": {"key": "uploadDocumentsTitle", "type": "LABEL", "value": "Display"}, "description": {"key": "uploadDocumentsDescription", "type": "LABEL", "value": "This determines the section title, header title and header sub-title shown on the upload documents section of the My Deal Page."}, "inputs": [{"key": "uploadDocumentsDisplaySection", "type": "ENABLED_GROUP", "label": "Display Section", "group": [{"key": "uploadDocumentsSectionTitle", "type": "INPUT", "hint": "This is displayed in the left menu, the collapsed version of the page section and in the primary CTA of the deal guide.", "description": "This is displayed in the left menu, the collapsed version of the page section and in the primary CTA of the deal guide.", "label": "Section Title"}, {"key": "uploadDocumentsHeaderTitle", "type": "INPUT", "hint": "This is displayed under the icon/header image.", "description": "This is displayed under the icon/header image.", "label": "Header Title"}, {"key": "uploadDocumentsHeaderSubTitle", "type": "INPUT", "hint": "This is displayed under the header title.", "description": "This is displayed under the header title.", "label": "Header Sub-Title"}, {"key": "uploadDocumentsDisplayTimeToComplete", "type": "ENABLED_GROUP", "label": "Display Time To Complete", "group": [{"key": "uploadDocumentsTimeToComplete", "type": "TIME_DURATION", "placeholder": "3 min", "label": "Time To Complete"}]}]}]}], "order": "8"}, {"title": "Appointment", "description": "This determines how the appointments section on the My Deal Page are displayed to users on your website.", "components": [{"title": {"key": "<PERSON><PERSON><PERSON>le", "type": "LABEL", "value": "Display"}, "description": {"key": "appointmentDescription", "type": "LABEL", "value": "This determines the section title, header title and header sub-title shown on the appointment section of the My Deal Page."}, "inputs": [{"key": "appointmentDisplaySection", "type": "ENABLED_GROUP", "label": "Display Section", "value": true, "group": [{"key": "appointmentSectionTitle", "type": "INPUT", "hint": "This is displayed in the left menu, the collapsed version of the page section and in the primary CTA of the deal guide.", "description": "This is displayed in the left menu, the collapsed version of the page section and in the primary CTA of the deal guide.", "label": "Section Title"}, {"key": "appointmentHeaderTitle", "type": "INPUT", "hint": "This is displayed under the icon/header image.", "description": "This is displayed under the icon/header image.", "label": "Header Title"}, {"key": "appointmentHeaderSubTitle", "type": "INPUT", "hint": "This is displayed under the header title.", "description": "This is displayed under the header title.", "label": "Header Sub-Title"}, {"key": "appointmentDisplayTimeToComplete", "type": "ENABLED_GROUP", "label": "Display Time To Complete", "group": [{"key": "appointmentTimeToComplete", "type": "TIME_DURATION", "placeholder": "3 min", "label": "Time To Complete"}]}]}]}], "order": "9"}, {"title": "Insurance Quotes", "description": "This determines how insurance quotes are displayed to users on your website.", "components": [{"title": {"key": "insuranceQuotesTitle", "type": "LABEL", "value": "Display"}, "description": {"key": "insuranceQuotesDescription", "type": "LABEL", "value": "This determines the section title, header title and header sub-title shown on the insurance quotes section of the My Deal Page."}, "inputs": [{"key": "insuranceQuotesDisplaySection", "type": "ENABLED_GROUP", "default": "true", "label": "Display Section", "group": [{"key": "insuranceQuotesSectionTitle", "type": "INPUT", "default": null, "hint": "This is displayed in the left menu, the collapsed version of the page section and in the primary CTA of the deal guide.", "description": "This is displayed in the left menu, the collapsed version of the page section and in the primary CTA of the deal guide.", "label": "Section Title"}, {"key": "insuranceQuotesHeaderTitle", "type": "INPUT", "default": null, "hint": "This is displayed under the icon/header image.", "label": "Header Title", "description": "This is displayed under the icon/header image."}, {"key": "insuranceQuotesHeaderSubTitle", "type": "INPUT", "default": null, "hint": "This is displayed under the header title.", "description": "This is displayed under the header title.", "label": "Header Sub-Title"}, {"key": "insuranceQuotesDisplayTimeToComplete", "type": "ENABLED_GROUP", "default": "true", "label": "Display Time To Complete", "group": [{"key": "insuranceQuotesTimeToComplete", "type": "TIME_DURATION", "default": "3 min", "label": "Time To Complete"}]}]}]}], "order": "10"}, {"title": "Deal Guide (CTAs Anchored To Bottom of My Deal Page)", "description": "This determines the behavior of the Deal Guide section on the My Deal Page. The Deal Guide navigates users through your specified sales process.", "components": [{"title": {"key": "dealGuidePrimaryTitle", "type": "LABEL", "value": "Primary CTA Button"}, "description": {"key": "dealGuidePrimaryDescription", "type": "LABEL", "value": "The text in this CTA will change based on where the user is in the sales process. The text and link destination for this CTA button are determined by the step names shown on your My Deal Page. You may edit the text shown in this CTA by changing the display name in one or more sections on the My Deal Page."}, "type": "WELL"}, {"title": {"key": "dealGuideSecondaryTitle", "type": "LABEL", "value": "CTA Button #2", "postText": "(Bottom Middle)"}, "description": {"key": "dealGuideSecondaryDescription", "type": "LABEL", "value": "This determines the display name and link destination for CTA Button #2."}, "inputs": [{"key": "dealGuideSecondaryCtaButtonActive", "type": "ENABLED_GROUP", "default": "true", "label": "<PERSON><PERSON><PERSON>", "singleRow": true, "group": [{"key": "dealGuideSecondaryCtaButtonText", "type": "INPUT", "default": null, "hint": "ex. Test Drive", "label": "Button Text"}, {"key": "dealGuideSecondaryCtaButtonLinkDestination", "type": "DROPDOWN", "default": null, "label": "Link Destination", "function": "getLinkDestinations()", "allowedValues": [{"value": "contact-dealer", "text": "Contact Dealer"}, {"value": "schedule-appointment", "text": "Schedule Appointment"}, {"value": "get-pre-qualified", "text": "Get Pre-Qualified"}, {"value": "add-trade-in", "text": "Add Trade-In"}]}]}]}, {"title": {"key": "dealGuideTertiaryTitle", "type": "LABEL", "value": "CTA Button #3", "postText": "(Bottom Left)"}, "description": {"key": "dealGuideTertiaryDescription", "type": "LABEL", "value": "This determines the display name and link destination for CTA Button #3."}, "inputs": [{"key": "dealGuideTertiaryCtaButtonActive", "type": "ENABLED_GROUP", "default": "true", "label": "<PERSON><PERSON><PERSON>", "singleRow": true, "group": [{"key": "dealGuideTertiaryCtaButtonText", "type": "INPUT", "default": null, "hint": "ex. Contact Dealer", "label": "Button Text"}, {"key": "dealGuideTertiaryCtaButtonLinkDestination", "type": "DROPDOWN", "default": null, "label": "Link Destination", "function": "getLinkDestinations()", "allowedValues": [{"value": "contact-dealer", "text": "Contact Dealer"}, {"value": "schedule-appointment", "text": "Schedule Appointment"}, {"value": "get-pre-qualified", "text": "Get Pre-Qualified"}, {"value": "add-trade-in", "text": "Add Trade-In"}]}]}]}], "order": "11"}]}