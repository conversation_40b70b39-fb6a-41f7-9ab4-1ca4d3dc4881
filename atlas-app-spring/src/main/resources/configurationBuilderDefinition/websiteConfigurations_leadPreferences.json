{"page": "Lead Preferences", "sections": [{"title": "Lead Type Settings", "description": "This determines which lead types are enabled or disabled.", "components": [{"title": {"key": "leadTypesTitle", "type": "LABEL", "value": "Lead Types"}, "description": {"key": "leadTypesDescription", "type": "LABEL", "value": "Select the Lead Types you want to enable in your CRM."}, "documentation": {"key": "leadPreferences/leadTypes/documentation", "type": "VIDEO", "url": null}, "inputs": [{"key": "leadTypes", "type": "CHECKBOX", "allowedValues": [{"value": "savedToGarage", "description": "Has vehicles saved in their Garage", "text": "Garaged"}, {"value": "contactDealer", "description": "Submits the Contact Dealer form", "text": "Contact", "disabled": true}, {"value": "financeApp", "description": "Submits a credit application", "text": "Credit App"}, {"value": "orderRequest", "description": "Accepts a Finance Offer", "text": "Contract"}, {"value": "appointment", "description": "Schedules a Test Drive", "text": "Appointment", "disabled": true}, {"value": "sessionEnd", "description": "Shopper session ends", "text": "Session End"}, {"value": "sendToCrm", "description": "Dealer sends lead from Atlas", "text": "Send to CRM", "disabled": true}, {"value": "sellOffer", "description": "Submits a Sell Your Car request", "text": "Sell Your Car Offer", "disabled": true}, {"value": "sellContact", "description": "Submits Contact Dealer form via Sell Your Car", "text": "Sell Your Car Contact"}, {"value": "sellInspection", "description": "Submits Appointment Request via Sell Your Car", "text": "Sell Your Car Inspection"}, {"value": "tradeOffer", "description": "Submits SAT request", "text": "Trade Offer"}, {"value": "tradeContact", "description": "Submits Contact Dealer form via SAT", "text": "Trade Contact Dealer"}, {"value": "tradeInspection", "description": "Submits Appointment Request via SAT", "text": "Trade Inspection"}, {"value": "inStoreCheckin", "description": "Checked into a Showroom", "text": "In-Store Check-In"}, {"value": "inStoreLookUp", "description": "FastPass was scanned in Showroom", "text": "In-Store Lookup"}, {"value": "preQual", "description": "Submits a Pre-Qualification form", "text": "Pre-Qual"}, {"value": "standalonePreQual", "description": "Submits a Pre-Qualification form via Standalone", "text": "Standalone Pre-Qual"}]}]}]}]}