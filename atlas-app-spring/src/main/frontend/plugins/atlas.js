import ToastPlugin from "@/plugins/Toast";
import acl from "@/mixins/acl";
import FeatureFlipping from "vue-feature-flipping";
import api from "Util/api";
const VueAxe = import("vue-axe");

class AtlasPlugin {
    install(Vue) {
        Vue.mixin(acl);
        Vue.use(ToastPlugin);
        Vue.use(FeatureFlipping);

        //setTimeout(async () => setEnabledFeatures(await this.getFeaturesFromBackend()), 1);

        const environment = _.get(window, "_APP_CONFIG.env[0]", "local") || "local";

        // Excludes PROD environment
        if (environment !== "prod") {
            VueAxe.then((axe) => {
                Vue.use(axe.default, {
                    allowConsoleClears: false,
                    auto: true,
                });
            });
        }

        Vue.filter("abs", (value) => {
            return Math.abs(value);
        });
    }

    async getFeaturesFromBackend() {
        return api.get("/features").then((response) => {
            return response.data;
        });
    }
}

export default new AtlasPlugin();
