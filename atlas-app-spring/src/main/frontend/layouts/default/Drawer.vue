<template>
    <v-navigation-drawer v-model="open" :mini-variant.sync="mini" mini-variant-width="80" width="260" app>
        <version-toggle />
        <template #prepend>
            <v-list-item :to="`/userSelfService?dealerIds=${dealerId}`" two-line>
                <v-list-item-avatar>
                    <img :src="imageUrl" alt="user avatar" />
                </v-list-item-avatar>

                <v-list-item-content>
                    <v-list-item-title>{{ name }}</v-list-item-title>
                    <v-list-item-subtitle>Logged In</v-list-item-subtitle>
                </v-list-item-content>
            </v-list-item>
        </template>

        <v-list dense nav expand class="nav-list">
            <v-list-item to="/">
                <v-list-item-action>
                    <v-icon>mdi-home</v-icon>
                </v-list-item-action>
                <v-list-item-content>
                    <v-list-item-title>Home</v-list-item-title>
                </v-list-item-content>
            </v-list-item>

            <!-- v-if dealer in context -->
            <v-list-item v-if="hasDealerAccess" :to="{ path: '/dealer/details', query: { dealerIds: dealerId } }">
                <v-list-item-action>
                    <v-icon>mdi-monitor-dashboard</v-icon>
                </v-list-item-action>
                <v-list-item-content>
                    <v-list-item-title>Dealer Details</v-list-item-title>
                </v-list-item-content>
            </v-list-item>
            <v-list-item v-if="hasCustomerAccess" :href="`/customers?dealerIds=${dealerId}`">
                <v-list-item-action>
                    <v-icon>mdi-account-box-outline</v-icon>
                </v-list-item-action>
                <v-list-item-content>
                    <v-list-item-title>Customers</v-list-item-title>
                </v-list-item-content>
            </v-list-item>
            <v-list-item v-if="hasCustomerAccess" :to="{ path: '/leads', query: { dealerIds: dealerId } }">
                <v-list-item-action>
                    <v-icon>mdi-email-search-outline</v-icon>
                </v-list-item-action>
                <v-list-item-content>
                    <v-list-item-title>Leads</v-list-item-title>
                </v-list-item-content>
            </v-list-item>
            <v-list-item :to="{ path: '/vehicles', query: { dealerIds: dealerId } }">
                <v-list-item-action>
                    <v-icon>mdi-car-multiple</v-icon>
                </v-list-item-action>
                <v-list-item-content>
                    <v-list-item-title>Vehicles</v-list-item-title>
                </v-list-item-content>
            </v-list-item>
            <v-list-item v-if="hasPricingAccess" :to="{ path: '/pricing/new', query: { dealerIds: dealerId } }">
                <v-list-item-action>
                    <v-icon>mdi-currency-usd</v-icon>
                </v-list-item-action>
                <v-list-item-content>
                    <v-list-item-title>Pricing</v-list-item-title>
                </v-list-item-content>
            </v-list-item>

            <v-list-item :to="{ path: '/sell-at-home', query: { dealerIds: dealerId } }">
                <v-list-item-action>
                    <v-icon>mdi-tag-outline</v-icon>
                </v-list-item-action>
                <v-list-item-content>
                    <v-list-item-title>Sell Your Car</v-list-item-title>
                </v-list-item-content>
            </v-list-item>

            <v-list-item
                v-if="isProtectionProductsEnabled"
                :to="{ path: '/protection-products', query: { dealerIds: dealerId } }"
            >
                <v-list-item-action>
                    <v-icon>mdi-shield-plus-outline</v-icon>
                </v-list-item-action>
                <v-list-item-content>
                    <v-list-item-title>Protection Products</v-list-item-title>
                </v-list-item-content>
            </v-list-item>

            <v-list-item v-if="hasDealerAccess" :to="{ path: '/sales', query: { dealerIds: dealerId } }">
                <v-list-item-action>
                    <v-icon>mdi-cash-multiple</v-icon>
                </v-list-item-action>
                <v-list-item-content>
                    <v-list-item-title>Sales</v-list-item-title>
                </v-list-item-content>
            </v-list-item>
            <!-- <v-list-item v-if="enableReporting" :to="`/reporting?dealerIds=${dealerId}`">
                <v-list-item-action>
                    <v-icon>mdi-chart-bar</v-icon>
                </v-list-item-action>
                <v-list-item-content>
                    <v-list-item-title>Reporting</v-list-item-title>
                </v-list-item-content>
            </v-list-item> -->
            <v-list-item
                v-if="enableReporting"
                :to="{
                    path: domoLinksDashboardMapping.BOOST_ENROLLMENT.PATH,
                    query: { dealerIds: dealerId, dashboardId: domoLinksDashboardMapping.BOOST_ENROLLMENT.ID },
                }"
            >
                <v-list-item-action>
                    <v-icon>mdi-chart-bar</v-icon>
                </v-list-item-action>
                <v-list-item-content>
                    <v-list-item-title>Reporting</v-list-item-title>
                </v-list-item-content>
            </v-list-item>
            <v-list-group
                v-if="hasWarrantyAccess"
                :group="`/dealer/${dealerId}/warranty`"
                prepend-icon="mdi-shield-check"
                no-action
            >
                <template #activator>
                    <v-list-item-content>
                        <v-list-item-title>CarSaver Protect</v-list-item-title>
                    </v-list-item-content>
                </template>
                <v-list-item :to="{ path: '/dealer/warranty/customer-search', query: { dealerIds: dealerId } }">
                    <v-list-item-content>
                        <v-list-item-title>eRating</v-list-item-title>
                    </v-list-item-content>
                </v-list-item>
                <v-list-item :to="{ path: '/dealer/warranty/remittance-url', query: { dealerIds: dealerId } }">
                    <v-list-item-content>
                        <v-list-item-title>eRemittance</v-list-item-title>
                    </v-list-item-content>
                </v-list-item>
                <v-list-item :to="{ path: '/dealer/warranty/resources', query: { dealerIds: dealerId } }">
                    <v-list-item-content>
                        <v-list-item-title>Resources</v-list-item-title>
                    </v-list-item-content>
                </v-list-item>
            </v-list-group>
            <v-list-item v-if="hasDealerAccess" :to="`/training?dealerIds=${dealerId}`">
                <v-list-item-action>
                    <v-icon>mdi-video</v-icon>
                </v-list-item-action>
                <v-list-item-content>
                    <v-list-item-title>Training</v-list-item-title>
                </v-list-item-content>
            </v-list-item>
            <v-list-item :href="`https://econnect.carsaver.com/`" :target="`_blank`">
                <v-list-item-action>
                    <v-icon>mdi-alpha-e-circle-outline</v-icon>
                </v-list-item-action>
                <v-list-item-content>
                    <v-list-item-title>eConnect</v-list-item-title>
                </v-list-item-content>
            </v-list-item>
            <v-list-item :href="`https://finqpro.io`" :target="`_blank`">
                <v-list-item-action>
                    <v-icon>mdi-plus-thick</v-icon>
                </v-list-item-action>
                <v-list-item-content>
                    <v-list-item-title>Prospect+</v-list-item-title>
                </v-list-item-content>
            </v-list-item>
            <!-- END v-if dealer in context -->

            <v-list-item
                v-if="isNissanDealer"
                :to="{ path: '/dealer/supported-accessories', query: { dealerIds: dealerId } }"
            >
                <v-list-item-action>
                    <v-icon>mdi-format-list-checks</v-icon>
                </v-list-item-action>
                <v-list-item-content>
                    <v-list-item-title>Supported Accessories</v-list-item-title>
                </v-list-item-content>
            </v-list-item>

            <!--   comment out for release, will be uncommented after the release, no deleting code please
            -->
            <v-list-item
                v-if="isDisplayCTACustomizationLink"
                :to="{ path: '/widget-customization', query: { dealerIds: dealerId } }"
            >
                <v-list-item-action>
                    <v-icon>mdi-monitor-dashboard</v-icon>
                </v-list-item-action>
                <v-list-item-content>
                    <v-list-item-title>Website CTA</v-list-item-title>
                </v-list-item-content>
            </v-list-item>

            <v-list-item v-if="isDisplayReturnPolicy" :to="{ path: '/return-policy', query: { dealerIds: dealerId } }">
                <v-list-item-action>
                    <v-icon>mdi-shield-check-outline</v-icon>
                </v-list-item-action>
                <v-list-item-content>
                    <v-list-item-title>Return Policy</v-list-item-title>
                </v-list-item-content>
            </v-list-item>

            <v-list-item
                v-if="isDisplayTradeInAdjustment"
                :to="{ path: '/trade-in-adjustment', query: { dealerIds: dealerId } }"
            >
                <v-list-item-action>
                    <v-icon>mdi-sync</v-icon>
                </v-list-item-action>
                <v-list-item-content>
                    <v-list-item-title>Offer Adjustments</v-list-item-title>
                </v-list-item-content>
            </v-list-item>

            <v-list-group
                v-if="isDomoIntegrationEnabled && showEnterpriseReporting"
                :group="`/domo`"
                prepend-icon="mdi-chart-bar"
                class="list-group"
                no-action
            >
                <template #activator>
                    <v-list-item-content>
                        <v-list-item-title>Enterprise Reporting</v-list-item-title>
                    </v-list-item-content>
                </template>
                <v-list-item
                    :to="{
                        path: domoLinksDashboardMapping.OVERVIEW.PATH,
                        query: { dealerIds: dealerId, dashboardId: domoLinksDashboardMapping.OVERVIEW.ID },
                    }"
                >
                    <v-list-item-content>
                        <v-list-item-title>Overview</v-list-item-title>
                    </v-list-item-content>
                </v-list-item>
                <v-list-item
                    :to="{
                        path: domoLinksDashboardMapping.GARAGE.PATH,
                        query: { dealerIds: dealerId, dashboardId: domoLinksDashboardMapping.GARAGE.ID },
                    }"
                >
                    <v-list-item-content>
                        <v-list-item-title>Buyer's Garage </v-list-item-title>
                    </v-list-item-content>
                </v-list-item>
                <v-list-item
                    :to="{
                        path: domoLinksDashboardMapping.STAGES.PATH,
                        query: { dealerIds: dealerId, dashboardId: domoLinksDashboardMapping.STAGES.ID },
                    }"
                >
                    <v-list-item-content>
                        <v-list-item-title>Stages</v-list-item-title>
                    </v-list-item-content>
                </v-list-item>
                <v-list-item
                    :to="{
                        path: domoLinksDashboardMapping.PLATFORM_UTILIZATION.PATH,
                        query: { dealerIds: dealerId, dashboardId: domoLinksDashboardMapping.PLATFORM_UTILIZATION.ID },
                    }"
                >
                    <v-list-item-content>
                        <v-list-item-title>Platform Utilization</v-list-item-title>
                    </v-list-item-content>
                </v-list-item>
                <v-list-item
                    :to="{
                        path: domoLinksDashboardMapping.GEOGRAPHIC_COMPARISON.PATH,
                        query: { dealerIds: dealerId, dashboardId: domoLinksDashboardMapping.GEOGRAPHIC_COMPARISON.ID },
                    }"
                >
                    <v-list-item-content>
                        <v-list-item-title>Geographic Comparison</v-list-item-title>
                    </v-list-item-content>
                </v-list-item>
                <v-list-item
                    :to="{
                        path: domoLinksDashboardMapping.YOY.PATH,
                        query: { dealerIds: dealerId, dashboardId: domoLinksDashboardMapping.YOY.ID },
                    }"
                >
                    <v-list-item-content>
                        <v-list-item-title>YoY</v-list-item-title>
                    </v-list-item-content>
                </v-list-item>
                <v-list-item
                    :to="{
                        path: domoLinksDashboardMapping.INVENTORY.PATH,
                        query: { dealerIds: dealerId, dashboardId: domoLinksDashboardMapping.INVENTORY.ID },
                    }"
                >
                    <v-list-item-content>
                        <v-list-item-title>Inventory</v-list-item-title>
                    </v-list-item-content>
                </v-list-item>
            </v-list-group>
            <v-list-group
                v-if="isDrawerSettingsEnabled && !newConfig"
                :group="`/settings`"
                prepend-icon="mdi-calculator-variant-outline"
                class="list-group"
                no-action
            >
                <template #activator>
                    <v-list-item-content>
                        <v-list-item-title>Settings</v-list-item-title>
                    </v-list-item-content>
                </template>
                <v-list-item :to="{ path: '/settings/new-car-finance-settings', query: { dealerIds: dealerId } }">
                    <v-list-item-content>
                        <v-list-item-title>New Car Calc.</v-list-item-title>
                    </v-list-item-content>
                </v-list-item>
                <v-list-item :to="{ path: '/settings/used-car-finance-settings', query: { dealerIds: dealerId } }">
                    <v-list-item-content>
                        <v-list-item-title>Used Car Calc</v-list-item-title>
                    </v-list-item-content>
                </v-list-item>
                <v-list-item
                    v-if="isQrCodeSettingsEnabled"
                    :to="{ path: '/settings/smart-links-qr-codes', query: { dealerIds: dealerId } }"
                >
                    <v-list-item-content>
                        <v-list-item-title>Smart Links/QR Codes</v-list-item-title>
                    </v-list-item-content>
                </v-list-item>
                <v-list-item
                    v-if="enableCtaButtons"
                    :to="{ path: '/settings/cta-buttons', query: { dealerIds: dealerId } }"
                >
                    <v-list-item-content>
                        <v-list-item-title>CTA Buttons</v-list-item-title>
                    </v-list-item-content>
                </v-list-item>
                <v-list-item
                    v-if="enableClientTheming"
                    :to="{ path: '/settings/client-theming', query: { dealerIds: dealerId } }"
                >
                    <v-list-item-content>
                        <v-list-item-title>Client Theming</v-list-item-title>
                    </v-list-item-content>
                </v-list-item>
                <v-list-item
                    v-if="overlaySettingsEnabled"
                    :to="{ path: '/settings/overlay-settings', query: { dealerIds: dealerId } }"
                >
                    <v-list-item-content>
                        <v-list-item-title>Integration Settings</v-list-item-title>
                    </v-list-item-content>
                </v-list-item>
                <v-list-item
                    v-if="enableMdpConfigs"
                    :to="{ path: '/settings/my-deal-page', query: { dealerIds: dealerId } }"
                >
                    <v-list-item-content>
                        <v-list-item-title>My Deal Page</v-list-item-title>
                    </v-list-item-content>
                </v-list-item>
                <v-list-item
                    v-if="isDealerRateSheetEnabled"
                    :to="{ path: '/settings/rate-sheet-configuration', query: { dealerIds: dealerId } }"
                >
                    <v-list-item-content>
                        <v-list-item-title>Rate Sheet Configuration</v-list-item-title>
                    </v-list-item-content>
                </v-list-item>
                <v-list-item
                    v-if="isLeadTypeConfigEnabled"
                    :to="{ path: '/settings/lead-preferences', query: { dealerIds: dealerId } }"
                >
                    <v-list-item-content>
                        <v-list-item-title>Lead Preferences</v-list-item-title>
                    </v-list-item-content>
                </v-list-item>
                <v-list-item
                    v-if="isGraphicsAndImagesEnabled"
                    :to="{ path: '/settings/graphics-and-images', query: { dealerIds: dealerId } }"
                >
                    <v-list-item-content>
                        <v-list-item-title>Graphics & Images</v-list-item-title>
                    </v-list-item-content>
                </v-list-item>
                <v-list-item
                    v-if="isBannerConfigEnabled"
                    :to="{ path: '/settings/banners', query: { dealerIds: dealerId } }"
                >
                    <v-list-item-content>
                        <v-list-item-title>Banners</v-list-item-title>
                    </v-list-item-content>
                </v-list-item>
            </v-list-group>

            <v-list-item v-if="isWalmartInventoryEnabled" :to="{ path: '/walmart', query: { dealerIds: dealerId } }">
                <v-list-item-action>
                    <v-icon>mdi-car-pickup</v-icon>
                </v-list-item-action>
                <v-list-item-content>
                    <v-list-item-title>Walmart Inventory</v-list-item-title>
                </v-list-item-content>
            </v-list-item>
        </v-list>
    </v-navigation-drawer>
</template>

<script>
import { get, sync, call } from "vuex-pathify";
import CryptoJS from "crypto-js";
import lodashGet from "lodash/get";
import VersionToggle from "./DrawerV2/versionToggle";
import { DOMO_LINKS_DASHBOARD_MAPPING } from "Util/domo";

export default {
    name: "DefaultDrawer",
    components: { VersionToggle },
    props: {
        newConfig: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            isLocalhost: this.testHostname("localhost"),
            isBeta: this.testHostname("beta"),
            domoLinksDashboardMapping: DOMO_LINKS_DASHBOARD_MAPPING,
        };
    },
    computed: {
        ...sync("drawer", ["open", "mini"]),
        ...get("loggedInUser", ["name", "email"]),
        featureFlags: get("loggedInUser/featureFlags"),
        isNesnaFAndIEnabledInSplit: get("loggedInUser/featureFlags@ENABLE_NESNA_F_AND_I"),
        nesnaFeatureSubscriptionEnabled: get("loggedInUser/nesnaFeatureSubscriptionEnabled"),
        isDrawerSettingsEnabled: get("loggedInUser/featureFlags@DRAWER_SETTINGS_ENABLED"),
        isQrCodeSettingsEnabled: get("loggedInUser/featureFlags@QR_CODE_SETTINGS_ENABLED"),
        isMdpConfigsEnabled: get("loggedInUser/featureFlags@ENABLE_MDP_CONFIGS"),
        isDealerEnrolledInNissanBuyAtHome: get("dealerStore/isDealerEnrolledInNissanBuyAtHome"),
        selectedDealerId: get("loggedInUser/selectedDealer@id"),
        programsLoader: get("dealerStore/selectedDealer@loader"),
        isDealerEnrolledInBoost: get("loggedInUser/isDealerEnrolledInBoost"),
        isAdminUser() {
            return this.$acl.hasAuthority("ROLE_ADMIN");
        },
        isDealerUser() {
            return this.$acl.hasAuthority("ROLE_DEALER");
        },
        isProgramUserEnrolledInNissanBuyAtHome: get("loggedInUser/isProgramUserEnrolledInNissanBuyAtHome"),
        showEnterpriseReporting() {
            return this.isProgramUserEnrolledInNissanBuyAtHome || this.isDealerEnrolledInNissanBuyAtHome;
        },
        isProgramReturnPolicyEnabled() {
            return lodashGet(this.featureFlags, "PROGRAM_RETURN_POLICY_FEATURE", false);
        },
        isOfferAdjustmentFeatureV2() {
            const isEnabled = lodashGet(this.featureFlags, "OFFER_ADJUSTMENT_FEATUREV2", false) || false;
            return isEnabled;
        },
        isDomoIntegrationEnabled() {
            const isEnabled = lodashGet(this.featureFlags, "DOMO_INTEGRATION", false) || false;
            return isEnabled;
        },
        isCtaConfigMenuEnabled() {
            const isEnabled = lodashGet(this.featureFlags, "CTA_CONFIG_MENU", false) || false;
            return isEnabled;
        },
        isProtectionProductsEnabled() {
            return this.nesnaFeatureSubscriptionEnabled && this.isNesnaFAndIEnabledInSplit;
        },
        isDealerRateSheetEnabled() {
            const isEnabled = lodashGet(this.featureFlags, "ENABLE_DEALER_RATE_SHEET", false) || false;
            return isEnabled;
        },
        enableReporting() {
            return this.hasDealerAccess && this.isDealerEnrolledInBoost;
        },
        enableCtaButtons() {
            return (this.isAdminUser || this.isDealerUser) && this.isCtaConfigMenuEnabled;
        },
        enableClientTheming() {
            return (this.isAdminUser || this.isDealerUser) && this.isClientThemingEnabled;
        },
        enableMdpConfigs() {
            return (this.isAdminUser || this.isDealerUser) && this.isMdpConfigsEnabled;
        },
        dealerId() {
            if (this.$route.params.dealerId) {
                return this.$route.params.dealerId;
            }

            if (this.$route.query.dealerIds) {
                return this.$route.query.dealerIds;
            }
            if (this.selectedDealerId) {
                return this.selectedDealerId;
            }
            return undefined;
        },
        imageUrl() {
            const hash = CryptoJS.MD5(this.email);
            return `https://www.gravatar.com/avatar/${hash}?d=mp`;
        },
        hasWarrantyAccess() {
            return (
                this.$acl.hasDealerPermission(this.dealerId, "warranty:create") &&
                this.$store.getters["loggedInUser/isEnrolledInWarrantyProgram"](this.dealerId)
            );
        },
        hasPricingAccess() {
            return (
                this.$acl.hasDealerPermission(this.dealerId, "inventory:new-pricing:edit") ||
                this.$acl.hasDealerPermission(this.dealerId, "inventory:used-pricing:edit")
            );
        },
        hasCustomerAccess() {
            return this.$acl.hasDealerPermission(this.dealerId, "customer:read");
        },
        hasDealerAccess() {
            return this.$acl.hasDealerPermission(this.dealerId, "dealer:read");
        },
        hasWebsiteCTAReadAccess() {
            const result = this.$acl.hasDealerPermission(this.dealerId, "website-cta:read");
            return result;
        },
        hasReadPolicyReadAccess() {
            const result = this.$acl.hasDealerPermission(this.dealerId, "return-policy:read");
            return result;
        },
        hasWebsiteCTAEditAccess() {
            const result = this.$acl.hasDealerPermission(this.dealerId, "website-cta:edit");
            return result;
        },
        isNissanDealer() {
            return this.$store.getters["loggedInUser/isNnaDealer"](this.dealerId);
        },
        isDisplayCTACustomizationLink() {
            const result = this.isNissanDealer && this.hasWebsiteCTAReadAccess;
            return result;
        },
        isDisplayReturnPolicy() {
            if (this.isProgramReturnPolicyEnabled) {
                return this.hasReadPolicyReadAccess;
            }
            return this.isNissanDealer && this.hasReadPolicyReadAccess;
        },
        isDisplayTradeInAdjustment() {
            const hasTIAReadAccess = this.$acl.hasDealerPermission(this.dealerId, "trade-in-adjustment:read");
            const result = (this.isOfferAdjustmentFeatureV2 || this.isNissanDealer) && hasTIAReadAccess;
            return result;
        },
        isWalmartInventoryEnabled() {
            const isEnabled = lodashGet(this.featureFlags, "ATLAS_WALMART_INVENTORY_ENABLED", false) || false;
            return isEnabled;
        },
        isClientThemingEnabled() {
            const isEnabled = lodashGet(this.featureFlags, "ENABLE_CLIENT_THEMING", false) || false;
            return isEnabled;
        },
        overlaySettingsFeatureFlagEnabled() {
            const isEnabled = lodashGet(this.featureFlags, "ENABLE_OVERLAY_SETTINGS", false) || false;
            return isEnabled;
        },
        overlaySettingsEnabled() {
            return (this.isAdminUser || this.isDealerUser) && this.overlaySettingsFeatureFlagEnabled;
        },
        isLeadTypeConfigEnabled() {
            const isEnabled = lodashGet(this.featureFlags, "ATLAS_LEAD_TYPE_CONFIG", false) || false;
            return isEnabled;
        },
        graphicsAndImagesFeatureFlagEnabled() {
            const isEnabled = lodashGet(this.featureFlags, "ENABLE_GRAPHICS_AND_IMAGES", false) || false;
            return isEnabled;
        },
        isGraphicsAndImagesEnabled() {
            return (this.isAdminUser || this.isDealerUser) && this.graphicsAndImagesFeatureFlagEnabled;
        },
        isBannerConfigEnabled() {
            const isEnabled = lodashGet(this.featureFlags, "ATLAS_BANNER_CONFIG", false) || false;
            return isEnabled;
        },
    },
    mounted() {
        this.init();
    },
    methods: {
        testHostname(name) {
            const hostName = window.location.hostname;
            const regEx = new RegExp(name);

            return regEx.test(hostName);
        },
        fetchDealerPrograms: call("dealerStore/fetchDealerPrograms"),
        init() {
            //only fetch if programs are not loading and not loaded
            if (this.programsLoader.isLoading || this.programsLoader.isComplete) {
                return;
            }
            if (this.dealerId) {
                this.fetchDealerPrograms(this.dealerId);
            }
        },
    },
};
</script>

<style scoped lang="scss">
@import "~vuetify/src/styles/settings/_variables";
.nav-list {
    @media #{map-get($display-breakpoints, 'md-and-down')} {
        margin-bottom: 80px;
    }
}
.list-group {
    margin: 4px 0 8px 0;
}
</style>
