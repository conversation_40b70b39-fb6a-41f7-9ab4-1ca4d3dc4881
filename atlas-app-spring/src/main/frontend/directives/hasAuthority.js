import Vue from "vue";

const compileAuthorize = (el, bindings, vnode) => {
    const authorities = bindings.value || [];

    // Run authorization
    if (!vnode.context.$store.getters["loggedInUser/hasAuthority"](authorities)) {
        commentNode(el, vnode);
    }
};

const compileHasAnyAuthorities = (el, bindings, vnode) => {
    const authorities = bindings.value || [];

    // Run authorization
    if (!vnode.context.$store.getters["loggedInUser/hasAnyAuthorities"](authorities)) {
        commentNode(el, vnode);
    }
};

Vue.directive("has-authority", {
    bind: (el, bindings, vnode) => {
        compileAuthorize(el, bindings, vnode);
    },
    update: (el, bindings, vnode) => {
        compileAuthorize(el, bindings, vnode);
    },

    componentUpdated: (el, bindings, vnode) => {
        compileAuthorize(el, bindings, vnode);
    },
});

Vue.directive("has-any-authorities", {
    bind: (el, bindings, vnode) => {
        compileHasAnyAuthorities(el, bindings, vnode);
    },
    update: (el, bindings, vnode) => {
        compileHasAnyAuthorities(el, bindings, vnode);
    },

    componentUpdated: (el, bindings, vnode) => {
        compileHasAnyAuthorities(el, bindings, vnode);
    },
});

/**
 * Create comment node
 *
 * @private
 * <AUTHOR>
 */
function commentNode(el, vnode) {
    const comment = document.createComment(" ");

    Object.defineProperty(comment, "setAttribute", {
        value: () => undefined,
    });

    vnode.text = " ";
    vnode.elm = comment;
    vnode.isComment = true;
    vnode.context = undefined;
    vnode.tag = undefined;
    vnode.data.directives = undefined;

    if (vnode.componentInstance) {
        vnode.componentInstance.$el = comment;
    }

    if (el.parentNode) {
        el.parentNode.replaceChild(comment, el);
    }
}
