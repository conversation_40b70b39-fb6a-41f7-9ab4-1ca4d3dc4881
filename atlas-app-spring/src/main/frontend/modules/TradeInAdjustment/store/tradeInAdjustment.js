import { make } from "vuex-pathify";
import loader from "@/util/loader";
import api from "Util/api";

const initialState = {
    tradeInValueData: {
        data: {},
        loader: loader.defaultState(),
    },
};

const actions = {
    ...make.actions(initialState),

    // saveAndPublish({ commit, state }, formData) {
    //     api.post(`/return-policy`, formData)
    //         .then((response) => {
    //             commit("SET_SAVE_AND_PUBLISH_DATA", response.data);
    //             commit("SET_SAVE_AND_PUBLISH_LOADER", loader.successful());
    //         })
    //         .catch((error) => {
    //             console.log(error);
    //             commit("SET_SAVE_AND_PUBLISH_LOADER", loader.error(error));
    //         })
    //         .finally(() => {
    //             this.loading = false;
    //         });
    // },
};

const mutations = {
    ...make.mutations(initialState),

    // SET_SAVE_AND_PUBLISH_DATA: (state, payload) => {
    //     state.returnPolicyData.data = payload;
    // },
    // SET_SAVE_AND_PUBLISH_LOADER: (state, payload) => {
    //     state.returnPolicyData.loader = payload;
    // },
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
};
