<template>
    <div>
        <v-card class="fill-height" style="max-width: 750px">
            <v-card-title
                ><div v-if="dataType === 'trade'">Global Trade-In Value Adjustment</div>
                <div v-else-if="dataType === 'sell'">Global Sell@Home Value Adjustment</div>
                <sup class="ml-1 font-weight-bold superScriptSizeTitle">1</sup>
                <v-tooltip top open-on-focus>
                    <template #activator="{ on, attrs }">
                        <v-icon color="grey" dark v-bind="attrs" medium class="close-rate-icon ml-2" v-on="on">
                            mdi-information-outline
                        </v-icon>
                    </template>
                    <p>
                        You can set a global trade adjustment rule to increase the trade value/offer over the 3rd party
                        trade valuation presented to the shopper. The global adjustment will apply to all trade vehicle
                        valuations unless a model specific adjustment you created exists for the vehicle.
                    </p>
                </v-tooltip>
            </v-card-title>
            <v-divider></v-divider>
            <v-container>
                <v-row>
                    <v-col cols="12">
                        <v-checkbox
                            v-model="dataSet.isGlobalPricingEnabled"
                            dense
                            :disabled="!hasOfferAdjustmentEditAccess"
                            class="mt-0"
                            label="Enable Global Pricing"
                            persistent-hint
                            hint="Enable if you want adjustment amounts to apply to ALL 'Trade-In' vehicles"
                        />
                    </v-col>
                </v-row>
                <v-row>
                    <v-col cols="12">
                        <v-select
                            v-model="dataSet.globalAdjustment.adjustmentType"
                            outlined
                            :disabled="!dataSet.isGlobalPricingEnabled || !hasOfferAdjustmentEditAccess"
                            dense
                            hide-details
                            :items="adjustmentTypeOptions"
                            label="Adjustment Type"
                        />
                    </v-col>
                    <v-col cols="12">
                        <v-text-field
                            v-model="dataSet.globalAdjustment.adjustmentAmount"
                            :disabled="!dataSet.isGlobalPricingEnabled || !hasOfferAdjustmentEditAccess"
                            label="Adjustment Amount"
                            outlined
                            type="number"
                            dense
                            :rules="[numberRule]"
                        />
                    </v-col>
                </v-row>
                <div class="py-3 d-flex justify-end">
                    <v-btn
                        :disabled="isFormInvalidOrOfferAdjustmentDisabled()"
                        class="primary mr-2"
                        @click="saveGlobal"
                    >
                        Save
                    </v-btn>
                    <v-btn
                        :disabled="!dataSet.isGlobalPricingEnabled || !hasOfferAdjustmentEditAccess"
                        @click="initialize"
                    >
                        Cancel
                    </v-btn>
                    <v-snackbar v-model="snackbar" elevation="50" color="grey" rounded="pill" :timeout="timeout">
                        <div class="title">
                            {{ globalConfirmationMessage }}
                        </div>
                        <template #actions>
                            <v-btn color="blue" variant="text" @click="snackbar = false"> Close </v-btn>
                        </template>
                    </v-snackbar>
                </div>
            </v-container>
        </v-card>

        <!-- Model-Specific Trade-in Value Adjustments Table -->
        <v-row class="mt-0">
            <v-col>
                <v-card>
                    <v-divider></v-divider>
                    <v-data-table
                        :headers="headers"
                        :items="dataSet.modelSpecificAdjustments"
                        sort-by="year"
                        class="elevation-1"
                    >
                        <template #top>
                            <v-toolbar flat>
                                <v-toolbar-title style="font-weight: 500">
                                    <span
                                        >Model-Specific {{ dataType === "trade" ? "Trade-In" : "Sell@Home" }} Value
                                        Adjustments</span
                                    >
                                    <sup class="mt-3 font-weight-bold superScriptSizeTitle">1</sup>
                                    <v-tooltip top open-on-focus>
                                        <template #activator="{ on, attrs }">
                                            <v-icon
                                                color="grey"
                                                dark
                                                v-bind="attrs"
                                                medium
                                                class="close-rate-icon ml-2"
                                                v-on="on"
                                            >
                                                mdi-information-outline
                                            </v-icon>
                                        </template>
                                        <p>
                                            You can set a global cash offer and/or trade adjustment rule to increase the
                                            trade value/cash offer over the 3rd party trade valuation presented to the
                                            shopper. The global adjustment will apply to all cash offers and/or trade
                                            vehicle valuations unless a model-specific adjustment you created exists for
                                            the vehicle.
                                        </p>
                                    </v-tooltip></v-toolbar-title
                                >
                                <v-divider class="mx-4" inset vertical></v-divider>
                                <v-spacer></v-spacer>

                                <v-dialog :key="dataType" v-model="dialog" max-width="600px">
                                    <template #activator="{ on, attrs }">
                                        <v-btn
                                            :disabled="!hasOfferAdjustmentEditAccess"
                                            color="primary"
                                            class="mb-2 mt-2"
                                            v-bind="attrs"
                                            v-on="on"
                                        >
                                            ADD ADJUSTMENT
                                        </v-btn>
                                    </template>
                                    <v-card>
                                        <v-card-title>
                                            <span class="text-h5">{{ formTitle }}</span>
                                        </v-card-title>

                                        <v-card-text>
                                            <v-container>
                                                <div class="d-flex">
                                                    <v-select
                                                        v-model="editedItem.make"
                                                        style="width: 265px"
                                                        class="pr-2"
                                                        item-value="text"
                                                        item-title="value"
                                                        outlined
                                                        :items="listOfMakes"
                                                        return-object
                                                        label="Makes"
                                                        @change="getModelsList(editedItem.make)"
                                                    ></v-select>
                                                    <v-select
                                                        v-model="editedItem.model"
                                                        style="width: 265px"
                                                        class="pl-2"
                                                        :items="listOfModels"
                                                        outlined
                                                        label="Models"
                                                    ></v-select>
                                                </div>

                                                <div class="d-inline-flex">
                                                    <v-select
                                                        v-if="editedItem.isYearRange === false"
                                                        v-model="editedItem.year"
                                                        style="width: 258px"
                                                        outlined
                                                        :items="listOfYears"
                                                        label="Year"
                                                    ></v-select>
                                                    <v-select
                                                        v-if="editedItem.isYearRange === true"
                                                        v-model="editedItem.startYear"
                                                        style="width: 258px"
                                                        :disabled="!editedItem.isYearRange"
                                                        outlined
                                                        :items="listOfStartYears"
                                                        label="Start Year"
                                                    ></v-select>
                                                    <v-select
                                                        v-if="editedItem.isYearRange === true"
                                                        v-model="editedItem.endYear"
                                                        style="width: 272px"
                                                        class="pl-4"
                                                        :disabled="!editedItem.isYearRange"
                                                        outlined
                                                        :items="listOfEndYears"
                                                        label="End Year"
                                                    ></v-select>
                                                </div>

                                                <div>
                                                    <v-checkbox
                                                        v-model="editedItem.isYearRange"
                                                        class="pb-4 checkBoxWidth"
                                                        label="Enable Year Rage"
                                                    >
                                                    </v-checkbox>
                                                </div>

                                                <div class="d-inline-flex">
                                                    <v-select
                                                        v-model="editedItem.adjustmentType"
                                                        item-value="text"
                                                        style="width: 266px"
                                                        class="pr-2"
                                                        :items="adjustmentTypeOptions"
                                                        outlined
                                                        label="Adjustment Type"
                                                    ></v-select>
                                                    <v-text-field
                                                        v-model="editedItem.adjustmentAmount"
                                                        style="width: 264px"
                                                        class="pl-2"
                                                        :rules="[EditOrAddNumberRule]"
                                                        type="number"
                                                        outlined
                                                        label="Adjustment Amount"
                                                    ></v-text-field>
                                                </div>
                                            </v-container>
                                        </v-card-text>

                                        <v-card-actions>
                                            <v-spacer></v-spacer>
                                            <v-btn color="blue darken-1" text @click.stop.prevent="close">
                                                Cancel
                                            </v-btn>
                                            <v-btn
                                                color="blue darken-1"
                                                :disabled="isValidToSubmit === false || isValidForm === false"
                                                text
                                                @click="saveSpecific"
                                            >
                                                Save
                                            </v-btn>
                                        </v-card-actions>
                                    </v-card>
                                </v-dialog>

                                <v-dialog v-model="dialogDelete" max-width="500px">
                                    <v-card>
                                        <v-card-title class="text-h5"
                                            >Are you sure you want to delete this item?</v-card-title
                                        >
                                        <v-card-actions>
                                            <v-spacer></v-spacer>
                                            <v-btn color="blue darken-1" text @click="closeDelete">Cancel</v-btn>
                                            <v-btn color="blue darken-1" text @click="deleteItemConfirm">OK</v-btn>
                                            <v-spacer></v-spacer>
                                        </v-card-actions>
                                    </v-card>
                                </v-dialog>
                            </v-toolbar>
                        </template>
                        <template #item.adjustmentType="props">
                            <div class="d-inline-flex">
                                <textarea
                                    v-model="props.item.adjustmentType"
                                    class="pr-1 mr-2 mt-5 textarea"
                                    style="max-width: 18px"
                                    readonly
                                ></textarea>
                                <v-text-field
                                    v-model="props.item.adjustmentAmount"
                                    class="pt-4"
                                    readonly
                                    style="height: 50px; width: 100px"
                                ></v-text-field>
                            </div>
                        </template>
                        <template #item.actions="{ item }">
                            <v-icon
                                :disabled="!hasOfferAdjustmentEditAccess"
                                small
                                class="mr-2"
                                @click="editItem(item)"
                            >
                                mdi-pencil
                            </v-icon>
                            <v-icon :disabled="!hasOfferAdjustmentEditAccess" small @click="deleteItem(item)">
                                mdi-delete
                            </v-icon>
                        </template>
                        <template #no-data>
                            <div>No model-specific trade-in price adjustments have been set up for this dealer</div>
                        </template>
                    </v-data-table>
                </v-card>
            </v-col>
        </v-row>

        <v-divider></v-divider>
        <v-card class="mt-3">
            <v-card-text class="d-flex grey--text font-weight-bold">
                <sup class="mt-3 superScriptSize">1</sup>
                By enabling and saving a Dealer Trade-In Adjustment, {{ dealerName }} assumes the responsibility of the
                Guaranteed Offer instead of CarSaver. The dealership will honor the customer’s Guaranteed Trade-in Offer
                based on final inspection of the vehicle.
            </v-card-text>
        </v-card>
    </div>
</template>
<script>
import { get } from "vuex-pathify";
import api from "Util/api";
import lodashMap from "lodash/map";
import lodashGet from "lodash/get";
import lodashFind from "lodash/find";
import lodashIsNil from "lodash/isNil";

// TODO: Hardcoded!
const NISSAN_BUY_AT_HOME_ID = "5e922fe4-e1e9-468c-b100-5b8f7cffcef3";
const ADJUSTMENT_PATH = "/trade_in_adjustment";
const ADJUSTMENT_PATH_ENTRY_POINTS = "/trade-in-adjustment";

export default {
    name: "TradeInValue",
    props: {
        dataType: {
            type: String,
            required: false,
            default: "trade",
        },
    },
    data() {
        return {
            tabs: null,
            dialog: false,
            dialogDelete: false,
            headers: [
                {
                    align: "start",
                    sortable: false,
                    value: "name",
                    width: "50px",
                },
                { text: "Year", value: "year", width: "200px" },
                { text: "Make", value: "make.text", width: "200px" },
                { text: "Model", value: "model", width: "200px" },
                { text: "Adjustment Type/Amount", value: "adjustmentType", width: "200px" },
                { text: "Actions", value: "actions", sortable: false, width: "200px" },
            ],
            listOfYears: [],
            listOfEndYears: [],
            listOfStartYears: [],
            listOfMakes: [],
            listOfModels: [],
            adjustmentTypeOptions: [
                { text: "$", value: "DOLLAR" },
                { text: "%", value: "PERCENT" },
            ],
            editedIndex: -1,
            deletedIndex: -1,
            editedItem: {
                isYearRange: false,
                name: "",
                year: "",
                startYear: null,
                endYear: null,
                make: {
                    text: "",
                    value: "",
                },
                model: "",
                adjustmentAmount: "",
                adjustmentType: "",
            },
            defaultItem: {
                isYearRange: false,
                name: "",
                year: "",
                startYear: null,
                endYear: null,
                make: {
                    text: "",
                    value: "",
                },
                model: "",
                adjustmentAmount: "",
                adjustmentType: "",
            },
            isValidForm: true,
            EditOrAddNumberRule: (v) => {
                let result;
                if (this.editedItem.adjustmentType === "$") {
                    if ((v > 0 && v > 5000) || this.containsOnlyNumbers(v) === false) {
                        result = false;
                        this.isValidForm = false;
                        return "Only up to $5000 accepted";
                    }
                }
                if (this.editedItem.adjustmentType === "%") {
                    if (v > 0 && v > 25) {
                        result = false;
                        this.isValidForm = false;
                        return "Only up to 25% accepted";
                    }
                }
                this.isValidForm = true;
                return true;
            },
            numberRule: (v) => {
                let result;
                if (this.dataSet.globalAdjustment.adjustmentType === "DOLLAR") {
                    if ((v > 0 && v > 5000) || this.containsOnlyNumbers(v) === false) {
                        result = false;
                        this.isValidForm = false;
                        return "Only up to $5000 accepted";
                    }
                }
                if (this.dataSet.globalAdjustment.adjustmentType === "PERCENT") {
                    if (v > 0 && v > 25) {
                        result = false;
                        this.isValidForm = false;
                        return "Only up to 25% accepted";
                    }
                }
                this.isValidForm = true;
                return true;
            },
            dataSet: {
                dealerId: null,
                programId: null,
                dataType: this.dataType,
                globalAdjustment: { adjustmentType: "DOLLAR" },
                modelSpecificAdjustments: [],
            },
            resultModelSpecificAdjustments: [],
            snackbar: false,
            globalConfirmationMessage: "Your changes have been successfully saved",
            timeout: 2500,
        };
    },
    computed: {
        selectedDealer: get("loggedInUser/selectedDealer"),
        formTitle() {
            const result =
                this.editedIndex === -1
                    ? `Model-Specific ${this.dataType === "trade" ? "Trade-in" : "Sell@Home"} Value Adjustment`
                    : `Edit Model-Specific ${this.dataType === "trade" ? "Trade-in" : "Sell@Home"} Value Adjustment`;
            return result;
        },
        dealerIds() {
            const result = this.$route.query.dealerIds;
            return result;
        },
        programIds() {
            const result = NISSAN_BUY_AT_HOME_ID;
            return result;
        },
        userDealerAccessList: get("loggedInUser/userDealerAccessList"),
        dealerName() {
            const locatedDealer = lodashFind(this.userDealerAccessList, ["id", this.dealerIds]);
            const result = !lodashIsNil(locatedDealer) ? locatedDealer.name : "";
            return result;
        },
        hasOfferAdjustmentEditAccess() {
            const result = this.$acl.hasDealerPermission(this.dealerIds, "trade-in-adjustment:edit");
            return result;
        },
        selectedDealerId: get("loggedInUser/selectedDealer@id"),
    },
    watch: {
        dialog(val) {
            val || this.close();
        },
        dialogDelete(val) {
            val || this.closeDelete();
        },
        selectedDealer(val) {
            if (this.selectedDealer.id !== this.dealerIds) {
                window.location = `${ADJUSTMENT_PATH_ENTRY_POINTS}?dealerIds=${val.id}`;
            }
        },
    },
    created() {
        this.initialize();
        this.retrieveData();
        this.retrieveYears();
    },
    methods: {
        isFormInvalidOrOfferAdjustmentDisabled() {
            const resultA = this.isValidForm === false;
            const resultB = this.hasOfferAdjustmentEditAccess === false;
            const result = resultA || resultB;
            return result;
        },
        initialize() {
            this.retrieveTradeInAdjustments();
            console.log("Loaded trade in adjustments");
        },
        containsOnlyNumbers(str) {
            return /^\d+$/.test(str);
        },
        yearListUpdate() {
            lodashMap(this.dataSet.modelSpecificAdjustments, (vehicle) => {
                let result = "";
                if (vehicle.isYearRange === true) {
                    this.overwriteYearForDisplay(vehicle);
                } else {
                    result = vehicle.year;
                }
                return result;
            });
        },
        adjTypeUpdate() {
            lodashMap(this.dataSet.modelSpecificAdjustments, (itor) => {
                let result = "";
                if (itor.adjustmentType === "DOLLAR") {
                    itor.adjustmentType = "$";
                } else {
                    itor.adjustmentType = "%";
                }
            });
        },
        overwriteYearForDisplay(item) {
            const startYear = item.isStartYearLimit === true ? "Older" : item.startYear;
            const endYear = item.isEndYearLimit === true ? "Newer" : item.endYear;
            item.startYear = startYear;
            item.endYear = endYear;
            const result = startYear + " - " + endYear;
            item.year = result; // TODO: added this line to overwrite result to year. Create new field for display.
        },
        overwriteYearForDisplayFromEdit(item) {
            item.isStartYearLimit = item.startYear === "Older";
            item.isEndYearLimit = item.endYear === "Newer";

            const startYear = item.isStartYearLimit === true ? "Older" : item.startYear;
            const endYear = item.isEndYearLimit === true ? "Newer" : item.endYear;
            item.startYear = startYear;
            item.endYear = endYear;
            const result = startYear + " - " + endYear;
            item.year = result; // TODO: added this line to overwrite result to year. Create new field for display.
        },
        editItem(item) {
            this.editedIndex = this.dataSet.modelSpecificAdjustments.indexOf(item);
            if (item.adjustmentType === this.adjustmentTypeOptions[0].value) {
                this.editedItem.adjustmentType = this.adjustmentTypeOptions[0].text;
            } else if (item.adjustmentType === this.adjustmentTypeOptions[1].value) {
                this.editedItem.adjustmentType = this.adjustmentTypeOptions[1].text;
            }
            // Storing the item Id to send back to BE as indentifier.
            // const idTracker = item.id;
            // this.idTrackingForDelete = idTracker;

            this.editedItem = Object.assign({}, item);
            this.dialog = true;
            const make = lodashFind(this.listOfMakes, { text: item.make.text });
            this.getModelsList({ value: make.value });
        },

        deleteItem(item) {
            const itemIndex = this.dataSet.modelSpecificAdjustments.indexOf(item);
            this.initialize();
            this.deletedIndex = itemIndex;
            this.dialogDelete = true;
        },

        deleteItemConfirm() {
            const resultModelSpecificAdjustmentId = this.resultModelSpecificAdjustments[this.deletedIndex].id;
            this.deleteTradeInAdjustmentSpecific(resultModelSpecificAdjustmentId);
            this.dataSet.modelSpecificAdjustments.splice(this.deletedIndex, 1);
            this.closeDelete();
        },
        // TODO: rename close to something meaningful
        close() {
            this.dialog = false;
            this.$nextTick(() => {
                this.editedItem = Object.assign({}, this.defaultItem);
                this.editedIndex = -1;
            });
        },
        closeDelete() {
            this.dialogDelete = false;
            this.$nextTick(() => {
                this.editedItem = Object.assign({}, this.defaultItem);
                this.deletedIndex = -1;
            });
        },
        saveSpecific() {
            // This one is called by edit/add
            if (this.editedItem.isYearRange === true) {
                this.overwriteYearForDisplayFromEdit(this.editedItem);
            }
            // TODO: Need to get this from the select mapping setup.
            // TODO: This might change in the future.
            const data = {
                tradeInAdjustmentPrimary: {
                    dealerId: this.dealerIds,
                    programId: this.programIds,
                    dataType: this.dataType,
                    isDeleted: null,
                    isGlobalPricingEnabled: this.dataSet.isGlobalPricingEnabled,
                    modelSpecificAdjustment: {
                        id: null,
                        isDeleted: false,
                        adjustmentType: this.editedItem.adjustmentType === "$" ? "DOLLAR" : "PERCENT",
                        adjustmentAmount: this.editedItem.adjustmentAmount,
                        year: this.editedItem.isYearRange !== true ? this.editedItem.year : null,
                        make: this.editedItem.make.text,
                        model: this.editedItem.model,
                        isYearRange: this.editedItem.isYearRange,
                        startYear: this.editedItem.startYear !== "Older" ? this.editedItem.startYear : null,
                        isStartYearLimit: this.editedItem.startYear === "Older",
                        endYear: this.editedItem.endYear !== "Newer" ? this.editedItem.endYear : null,
                        isEndYearLimit: this.editedItem.endYear === "Newer",
                    },
                },
            };
            if (this.editedIndex > -1) {
                if (this.editedItem.id === "undefined") {
                    const resultModelSpecificAdjustmentId = this.resultModelSpecificAdjustments[this.editedIndex].id;
                    data.tradeInAdjustmentPrimary.modelSpecificAdjustment.id = resultModelSpecificAdjustmentId;
                } else {
                    data.tradeInAdjustmentPrimary.modelSpecificAdjustment.id = this.editedItem.id;
                }
            }

            this.persistSpecificAdjustment(data);
            if (this.editedIndex > -1) {
                Object.assign(this.dataSet.modelSpecificAdjustments[this.editedIndex], this.editedItem);
            } else {
                this.dataSet.modelSpecificAdjustments.push(this.editedItem);
            }

            this.close();
        },
        persistSpecificAdjustment(data) {
            this.upsertTradeInAdjustmentSpecific(data);
        },
        isValidToSubmit() {
            if (this.editedItem.adjustmentAmount > 1) {
                return true;
            } else {
                return false;
            }
        },
        retrieveTradeInAdjustments() {
            api.get(`${ADJUSTMENT_PATH}/${this.dealerIds}/${this.programIds}/${this.dataType}`)
                .then((response) => {
                    this.dataSet = lodashGet(response, "data", {});
                    if (!this.dataSet?.dealerId) {
                        this.dataSet = {
                            ...this.dataSet,
                            dealerId: this.$route.query?.dealerIds || this.selectedDealerId || null,
                            modelSpecificAdjustments: [],
                        };
                    }

                    this.resultModelSpecificAdjustments = this.dataSet.modelSpecificAdjustments;
                    this.yearListUpdate();
                    this.adjTypeUpdate(); // New method to display correct data for "PERCENT" and "DOLLAR"
                    this.transformDataSetMakes();
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        retrieveData() {
            api.get(`${ADJUSTMENT_PATH}/makes/${this.dealerIds}`)
                .then((response) => {
                    const makes = lodashGet(response, "data.makes", []);
                    const results = [];
                    lodashMap(makes, (make) => {
                        const makesData = Object.entries(make)[0];
                        results.push({ text: makesData[0], value: makesData[1] });
                    });
                    this.listOfMakes = results;
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        transformDataSetMakes() {
            lodashMap(this.dataSet.modelSpecificAdjustments, (itor) => {
                itor.make = { text: itor.make };
            });
        },
        retrieveYears() {
            api.get(`${ADJUSTMENT_PATH}/years/${this.dealerIds}`)
                .then((response) => {
                    console.log(response.data);
                    this.listOfYears = response.data.years;
                    this.buildListOfYearsOlderAndNewer();
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        buildListOfYearsOlderAndNewer() {
            const older = "Older";
            const newer = "Newer";
            const years = this.listOfYears;
            this.listOfStartYears = [older, ...years];
            this.listOfEndYears = [...years, newer];
        },
        getModelsList(item) {
            const makeId = item.value;
            api.get(`${ADJUSTMENT_PATH}/models/${this.dealerIds}/${makeId}`)
                .then((response) => {
                    console.log(response.data);
                    this.listOfModels = response.data.models;
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        saveGlobal() {
            const body = {
                tradeInAdjustmentPrimary: {
                    dealerId: this.dealerIds,
                    programId: this.programIds,
                    dataType: this.dataType,
                    isDeleted: null,
                    isGlobalPricingEnabled: this.dataSet.isGlobalPricingEnabled,
                    globalAdjustment: {
                        isDeleted: null,
                        adjustmentType: this.dataSet.globalAdjustment.adjustmentType,
                        adjustmentAmount: this.dataSet.globalAdjustment.adjustmentAmount,
                    },
                },
            };

            this.upsertTradeInAdjustmentGlobal(body);
        },
        upsertTradeInAdjustmentGlobal(body) {
            api.post(`${ADJUSTMENT_PATH}/global`, body)
                .then((response) => {
                    console.log("Posted Global");
                    this.snackbar = true;
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        upsertTradeInAdjustmentSpecific(body) {
            api.post(`${ADJUSTMENT_PATH}/specific`, body)
                .then((response) => {
                    const resultModelSpecificAdjustments = lodashGet(response, "response.modelSpecificAdjustments", []);
                    this.resultModelSpecificAdjustments = resultModelSpecificAdjustments;
                    this.initialize();
                    console.log("Posted Specific");
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        deleteTradeInAdjustmentSpecific(specificId) {
            api.delete(`${ADJUSTMENT_PATH}/specific/${this.dealerIds}/${this.programIds}/${specificId}`)
                .then((response) => {
                    console.log("Deleted specificId: " + specificId);
                })
                .catch((error) => {
                    console.error(error);
                });
        },
    },
};
</script>

<style lang="scss" scoped>
.boxSize {
    width: 207px;
}
.superScriptSize {
    font-size: 8px;
}
.superScriptSizeTitle {
    font-size: 10px;
}
.textarea {
    resize: none;
}
.checkBoxWidth {
    width: 170px;
}
</style>
