import Vue from "vue";
import VueRouter from "vue-router";
import { routerOptions, layout, route, configureRouter } from "Util/routerHelper";
import FormPage from "Modules/ConfigPageBuilders/views/FormPage";
import PreviewFormPage from "@/modules/ConfigPageBuilders/views/PreviewFormPage.vue";

Vue.use(VueRouter);

const PATH_PREFIX = {
    websiteTheming: "/website-theming",
    websiteConfiguration: "/website-configuration",
    websiteComponents: "/website-components",
    markingSettings: "/marketing-settings",
};

const propsUtil = (route, pageName) => {
    return (route) => {
        const page = pageName;
        const dealerIds = route.query.dealerIds;

        return { page, dealerIds };
    };
};

const routes = [
    layout("layoutwithbreadcrumbs", [
        {
            name: "SmartLinksQrCodes",
            path: PATH_PREFIX.markingSettings + "/smart-links-qr-codes",
            component: PreviewFormPage,
            props: propsUtil(route, "Smart Links QR Codes"),
        },
        {
            name: "ClientTheming",
            path: PATH_PREFIX.websiteTheming + "/client-theming",
            component: FormPage,
            props: propsUtil(route, "Client Theming"),
        },
        {
            name: "SystemTheming",
            path: PATH_PREFIX.websiteTheming + "/system-theming",
            component: FormPage,
            props: propsUtil(route, "System Theming"),
        },
        {
            name: "MyDealPage",
            path: PATH_PREFIX.websiteConfiguration + "/my-deal-page",
            component: FormPage,
            props: propsUtil(route, "My Deal Page"),
        },
        {
            name: "SelfSelectedCreditScore",
            path: PATH_PREFIX.websiteConfiguration + "/self-selected-credit-score",
            component: FormPage,
            props: propsUtil(route, "Self Selected Credit Score"),
        },
        {
            name: "VehicleDetailsPage",
            path: PATH_PREFIX.websiteComponents + "/vehicle-details-page",
            component: FormPage,
            props: propsUtil(route, "Vehicle Details Page"),
        },
        {
            name: "HamburgerMenu",
            path: PATH_PREFIX.websiteConfiguration + "/hamburger-menu",
            component: PreviewFormPage,
            props: propsUtil(route, "Hamburger Menu"),
        },
        {
            name: "CTAButtons",
            path: PATH_PREFIX.websiteConfiguration + "/cta-buttons",
            component: PreviewFormPage,
            props: propsUtil(route, "CTA Buttons"),
        },
    ]),
];

//

const router = new VueRouter({
    mode: "history",
    routes,
    ...routerOptions,
});

configureRouter(router);

export default router;
