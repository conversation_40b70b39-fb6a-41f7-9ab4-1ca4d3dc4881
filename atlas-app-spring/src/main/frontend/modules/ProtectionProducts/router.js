import Vue from "vue";
import VueRouter from "vue-router";
import { configureRouter, layout, route, routerOptions } from "Util/routerHelper";
import loggedInUser from "@/store/loggedInUser";

Vue.use(VueRouter);

const PATH_PREFIX = "/protection-products";

const routes = [layout("Default", [route("ProtectionProducts", "ProtectionProductsView", null, PATH_PREFIX)])];

const router = new VueRouter({
    mode: "history",
    routes,
    ...routerOptions,
});

router.beforeEach((to, from, next) => {
    // if nesnaFeatureSubscriptionEnabled is false, redirect to selected dealer details page
    if (loggedInUser.state.nesnaFeatureSubscriptionEnabled === false && to.path === "/protection-products") {
        next(`/dealer/details?dealerIds=${to.query.dealerIds}`);
    } else {
        next();
    }
});

configureRouter(router);

export default router;
