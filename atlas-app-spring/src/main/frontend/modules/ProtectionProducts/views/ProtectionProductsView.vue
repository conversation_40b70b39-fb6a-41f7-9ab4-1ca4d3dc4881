<template>
    <div class="protection-products-view">
        <v-card elevation="2">
            <Toast ref="toastRef" />
            <div class="d-flex align-center justify-space-between pa-4">
                <div>
                    <v-card-title> Protection Product Selections </v-card-title>
                    <v-card-subtitle> Please select at least one protection product. </v-card-subtitle>
                </div>
                <v-btn color="primary" :disabled="isSaveAndPublishDisabled" @click="save"> SAVE & PUBLISH </v-btn>
            </div>
            <v-skeleton-loader v-if="dealerNesnaProtectionProductsLoader.isLoading" type="table" />
            <v-form v-else v-model="valid">
                <v-data-table
                    :headers="headers"
                    :items="dealerNesnaProtectionProducts"
                    :expanded.sync="expanded"
                    item-key="masterNesnaProductId"
                    show-expand
                    class="table"
                >
                    <template #item.data-table-expand="{ item, index, expand, isExpanded }">
                        <v-switch
                            :input-value="item.enabled"
                            @click="expand(!isExpanded)"
                            @change="updateEnabledFieldOnChange($event, index)"
                        ></v-switch>
                    </template>
                    <template #item.productTypeDescription="{ item }">
                        <ProductModal :product="item" />
                    </template>
                    <template #item.adjustedValue="{ item }">
                        <v-tooltip top :disabled="item.enabled">
                            <template #activator="{ on, attrs }">
                                <div v-on="on">
                                    <v-select
                                        v-model="item.adjustedValue"
                                        :items="[
                                            { value: null, text: 'Select' },
                                            { value: 'RETAIL', text: 'Retail Price' },
                                        ]"
                                        class="mt-4"
                                        :disabled="!item.enabled"
                                        :rules="item.enabled ? [validationRules.required] : []"
                                        label="Adjusted Value"
                                        outlined
                                        dense
                                        v-bind="attrs"
                                    >
                                    </v-select>
                                </div>
                            </template>
                            <span>To edit please enable this product</span>
                        </v-tooltip>
                    </template>
                    <template #item.adjustmentType="{ item }">
                        <v-tooltip top :disabled="item.enabled">
                            <template #activator="{ on, attrs }">
                                <div v-on="on">
                                    <v-select
                                        v-model="item.adjustmentType"
                                        :items="[
                                            { value: null, text: 'Select' },
                                            { value: 'DOLLAR', text: '$' },
                                            { value: 'PERCENTAGE', text: '%' },
                                        ]"
                                        class="mt-4"
                                        :disabled="!item.enabled"
                                        :rules="item.enabled ? [validationRules.required] : []"
                                        label="Markup Type"
                                        outlined
                                        dense
                                        v-bind="attrs"
                                    ></v-select>
                                </div>
                            </template>
                            <span>To edit please enable this product</span>
                        </v-tooltip>
                    </template>
                    <template #item.markupAmount="{ item }">
                        <v-tooltip top :disabled="item.enabled">
                            <template #activator="{ on, attrs }">
                                <v-text-field
                                    v-model="item.markupAmount"
                                    class="mt-4"
                                    label="Markup Amount"
                                    type="number"
                                    :disabled="!item.enabled"
                                    :rules="item.enabled ? [validationRules.range] : []"
                                    outlined
                                    dense
                                    v-bind="attrs"
                                    v-on="on"
                                ></v-text-field>
                            </template>
                            <span>To edit please enable this product</span>
                        </v-tooltip>
                    </template>
                    <template #expanded-item="{ item }">
                        <td :colspan="headers.length" class="pl-90">
                            <pricingTier
                                :id="item.id"
                                :levels="item.levels"
                                @onValidityChange="handleValidityChange"
                            ></pricingTier>
                        </td>
                    </template>
                </v-data-table>
            </v-form>
        </v-card>
        <v-card class="description mt-3">
            <v-card-text>
                Any price adjustment or “mark-up” related to these Protection Products is set by the dealership. Nissan
                dealerships are solely responsible for complying with state and federal consumer protection laws
                regarding vehicle prices, including mark-ups.
            </v-card-text>
        </v-card>
    </div>
</template>

<script>
import { call, get } from "vuex-pathify";
import ProductModal from "Modules/ProtectionProducts/components/ProductModal.vue";
import Toast from "../components/Toast.vue";
import pricingTier from "../components/PricingTier.vue";
import lodashIsEqual from "lodash/isEqual";

export default {
    name: "ProtectionProductsView",
    components: { Toast, ProductModal, pricingTier },

    data: () => ({
        headers: [
            {
                text: "Enable",
                align: "start",
                sortable: false,
                value: "data-table-expand",
            },
            { text: "Name", value: "productTypeDescription", sortable: false },
            { text: "Adjusted Value", value: "adjustedValue", sortable: false, width: 190 },
            { text: "Markup Type", value: "adjustmentType", sortable: false, width: 190 },
            { text: "Markup Amount", value: "markupAmount", sortable: false },
        ],
        nesnaProductionProductList: [],
        expandedRowsIds: [],
        expanded: [],
        validationRules: {
            required: (value) => !!value || "Required.",
            range: (value) => (value !== "" && value >= 0 && value <= 9999) || "Value must be between 0 and 9999.",
        },
        isToastVisible: false,
        valid: false,
        levelValidity: {}, // id: validity (Boolean)
    }),

    computed: {
        dealerNesnaProtectionProducts: get("protectionProducts/dealerNesnaProtectionProducts@data"),
        dealerSavedNesnaProtectionProducts: get("protectionProducts/dealerSavedNesnaProtectionProducts"),
        dealerNesnaProtectionProductsLoader: get("protectionProducts/dealerNesnaProtectionProducts@loader"),
        dealerIds() {
            return this.$route.query.dealerIds;
        },
        areChangesMade() {
            if (this.dealerNesnaProtectionProducts == null || this.dealerSavedNesnaProtectionProducts == null) {
                return false;
            }
            return !lodashIsEqual(this.dealerNesnaProtectionProducts, this.dealerSavedNesnaProtectionProducts);
        },
        enabledProducts() {
            return this.dealerNesnaProtectionProducts != null
                ? this.dealerNesnaProtectionProducts.filter((product) => product.enabled)
                : [];
        },
        isEnabledProductFormValid() {
            return this.enabledProducts.every((product) => this.levelValidity[product.id]);
        },
        isSaveAndPublishDisabled() {
            return !this.areChangesMade || !this.valid || !this.isEnabledProductFormValid;
        },
    },

    watch: {
        "$route.query.dealerIds": function (newDealerId, oldDealerId) {
            if (newDealerId !== oldDealerId && this.$route.path === "/protection-products") {
                window.location = `/protection-products?dealerIds=${newDealerId}`;
            }
        },
    },

    async mounted() {
        await this.fetchDealerNesnaProtectionProducts(this.dealerIds);
        this.expanded = this.enabledProducts;
    },

    methods: {
        fetchDealerNesnaProtectionProducts: call("protectionProducts/fetchDealerNesnaProtectionProducts"),
        saveDealerNesnaProducts: call("protectionProducts/saveDealerNesnaProducts"),
        updateEnabledField: call("protectionProducts/updateEnabledField"),

        updateEnabledFieldOnChange(event, index) {
            this.updateEnabledField({ index, value: !!event });
            if (event) {
                this.populateDefaultValues(index);
            } else {
                this.resetApiDefaultValues(index);
            }
        },
        close() {
            console.log("Close");
        },

        save() {
            const products = this.dealerNesnaProtectionProducts.map((product) => {
                // initial save dealer id will be null
                if (product.dealerId === null) {
                    return { ...product, dealerId: this.dealerIds };
                }
                return product;
            });
            const requestBody = { nesnaProtectionProducts: products };
            const requestObject = { dealerIds: this.dealerIds, requestBody };
            this.saveDealerNesnaProducts(requestObject);
            this.$refs.toastRef.showToast();
        },

        populateDefaultValues(index) {
            let product = this.dealerNesnaProtectionProducts[index];
            if (!product.markupAmount) {
                product.markupAmount = 0;
            }
            if (!product.adjustmentType) {
                product.adjustmentType = "DOLLAR";
            }
            if (!product.adjustedValue) {
                product.adjustedValue = "RETAIL";
            }
        },

        resetApiDefaultValues(index) {
            let product = this.dealerNesnaProtectionProducts[index];
            const savedProduct = this.dealerSavedNesnaProtectionProducts[index];
            product.markupAmount = savedProduct.markupAmount;
            product.adjustmentType = savedProduct.adjustmentType;
            product.adjustedValue = savedProduct.adjustedValue;
        },

        handleValidityChange({ id, validity }) {
            this.levelValidity = { ...this.levelValidity, [id]: validity };
        },
    },
};
</script>

<style lang="scss">
.protection-products-view {
    background-color: #eeeeee;
    padding: 16px;
    p {
        margin: 0;
    }
    .description {
        color: #757575;
        font-size: 14px;
    }
    .expanded-container {
        background: white;
    }

    .v-input__control {
        .v-text-field__details {
            padding: 0 !important;
        }
    }

    .pl-90 {
        padding-left: 90px !important;
    }
}
</style>
