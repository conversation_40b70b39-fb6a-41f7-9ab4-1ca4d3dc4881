<template>
    <v-form v-model="valid" class="mb-3" @submit.prevent="onSubmit">
        <v-card>
            <v-container>
                <v-row>
                    <v-col cols="12" md="4">
                        <v-text-field
                            v-model="stockNumber"
                            label="Stock"
                            outlined
                            dense
                            :rules="[rules.characterLimit]"
                            append-icon="mdi-pound-box-outline"
                        />
                    </v-col>

                    <v-col cols="12" md="4">
                        <v-text-field
                            v-model="vin"
                            label="VIN"
                            outlined
                            dense
                            :rules="[rules.vin]"
                            append-icon="mdi-pound-box-outline"
                        />
                    </v-col>

                    <v-col cols="12" md="4" class="d-flex justify-center">
                        <div class="d-flex flex-row justify-center">
                            <v-btn size="sm" type="submit" color="primary" :disabled="isLoading || !valid" class="mr-2">
                                Search
                            </v-btn>

                            <v-btn text size="sm" :disabled="isLoading" @click="clearFilters"> Reset </v-btn>
                        </div>
                    </v-col>
                </v-row>
            </v-container>
        </v-card>
    </v-form>
</template>

<script>
import { sync, call, get } from "vuex-pathify";
import { characterLimit, vin } from "Util/formRules";
import { sanitize } from "Util/sanitize";

export default {
    name: "InventorySearchForm",

    data() {
        return {
            rules: { characterLimit, vin },
            valid: true,
        };
    },

    computed: {
        isLoading: get("inventorySearch/<EMAIL>"),
        stockNumber: sync("inventorySearch/filters@stockNumber"),
        vin: sync("inventorySearch/filters@vin"),
    },

    methods: {
        doSearch: call("inventorySearch/doSearch"),
        clearFilters: call("inventorySearch/clearFilters"),

        onSubmit() {
            if (!this.valid) return;
            this.stockNumber = sanitize(this.stockNumber);
            this.vin = sanitize(this.vin);

            this.doSearch();
        },
    },
};
</script>
