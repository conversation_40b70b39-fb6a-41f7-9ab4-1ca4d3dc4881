<template>
    <v-card>
        <v-toolbar flat>
            <v-toolbar-title class="grey--text">Vehicles</v-toolbar-title>
            <v-spacer />
            <table-column-selector id="table-column-config" v-model="displayFields" :fields="fields" />
            <export-column-selector
                id="table-column-export"
                v-model="exportFields"
                :display-fields="displayFields"
                :fields="fields"
                @doExport="prepareExport"
            />
        </v-toolbar>

        <v-divider />

        <filter-chips :store="store" />

        <table-search-count-label :page-number="pageNumber" :page-size="pageSize" :total-elements="totalElements" />

        <v-divider />

        <v-data-table
            :headers="fieldsForDisplay"
            :items="searchResults"
            :loading="isLoading"
            :server-items-length="totalElements"
            :sort-by="sortBy"
            :sort-desc="sortDesc"
            hide-default-footer
            show-expand
            @update:options="updateOptions"
        >
            <template #item.active="{ item }">
                <boolean-indicator :value="item.active" />
            </template>
            <template #item.certified="{ item }">
                <boolean-indicator :value="item.certified" />
            </template>
            <template v-if="isProgramUser" #item.dealer.name="{ item }">
                {{ item.dealer.name }}
            </template>
            <template #item.vin="{ item }">
                <a href="#" @click="openVehicleDetailsModal(item)">
                    {{ item.vin }}
                </a>
            </template>
            <template #item.stockType="{ item }">
                <span v-if="item.certified">Certified</span>
                <span v-else>{{ item.stockType }}</span>
            </template>
            <template #item.ymm="{ item }"> {{ item.year }} {{ item.make }} {{ item.model }}</template>
            <template #item.inTransit="{ item }">
                <boolean-indicator :value="item.inTransit" />
            </template>
            <template #item.exteriorColor="{ item }">
                <span :style="{ '--vehicle-color-var': '#' + item.exteriorColorHexCode }" class="colorSquare">
                    {{ item.exteriorColor }}
                </span>
            </template>
            <template #item.price="{ item }">
                {{ item.price | currency("$", ",") }}
            </template>

            <template #expanded-item="{ headers, item }">
                <td :colspan="headers.length">
                    <program-overlay :vehicle-doc="item" />
                </td>
            </template>
            <template #item.address="{ item }">
                <span v-if="item.address">
                    {{ item.address.street }} {{ item.address.city }}, {{ item.address.stateCode }}
                    {{ item.address.zipCode }}
                </span>
            </template>
            <template #item.isSuppressed="{ item }">
                <boolean-indicator :value="item.isSuppressed" />
            </template>
        </v-data-table>

        <v-container class="pt-2">
            <v-row>
                <v-col cols="12">
                    <v-pagination
                        :value="page"
                        :total-visible="10"
                        :length="totalPages"
                        :disabled="isLoading"
                        @input="changePage"
                    />
                </v-col>
            </v-row>
        </v-container>
        <v-dialog v-model="showVehicleDetailsModal" width="700">
            <v-card>
                <v-card-title>
                    <v-img :src="vehicleImg" class="vehicle-icon" />
                    Vehicle Details
                    <v-img :src="close" class="close-icon" @click="closeModal" />
                </v-card-title>
                <div v-if="vehicleData" class="sub-title">
                    <span>{{ vehicleData.year }} {{ vehicleData.make }} {{ vehicleData.model }}</span>
                    <span class="under-text">{{ vehicleData.trim }}</span>
                </div>
                <div v-if="vehicleData" class="description">
                    <v-row no-gutters>
                        <v-col cols="12" md="8"> Exterior: {{ vehicleData.exteriorColor }} </v-col>
                        <v-col class="float-right" cols="12" md="4"> VIN: {{ vehicleData.vin }} </v-col>
                        <v-col cols="12" md="8"> Interior: {{ vehicleData.interiorColor }} </v-col>
                        <v-col class="float-right" cols="12" md="4">
                            Stock Number: {{ vehicleData.stockNumber }}
                        </v-col>
                    </v-row>
                </div>
                <div>
                    <div v-if="!showGallery" class="pa-6">
                        <v-skeleton-loader type="image"></v-skeleton-loader>
                    </div>
                    <div v-if="showGallery" id="gallery-container" ref="galleryContainer">
                        <div
                            v-if="imagesList && imagesList.length > 0"
                            class="row d-flex flex-column flex-sm-row no-gutters"
                        >
                            <div id="main-image-container" class="col-12 col-sm-8 pr-sm-3 mb-4 mb-sm-0">
                                <flickity
                                    v-if="imagesList.length > 0"
                                    id="main-image"
                                    ref="flickity"
                                    :options="flickityOptions"
                                >
                                    <div
                                        v-for="image in imagesList"
                                        :id="`vehicle${image.index}`"
                                        :key="image.index"
                                        class="carousel-cell"
                                    >
                                        <v-img :src="image.url"></v-img>
                                    </div>
                                </flickity>
                            </div>
                            <div id="image-nav" class="col-12 col-sm-4 d-flex flex-sm-wrap align-content-start">
                                <div
                                    v-for="(image, imgindex) in imagesList"
                                    :id="`thumbnail-${image.index}`"
                                    :key="imgindex"
                                    :ref="`thumbnail${image.index}`"
                                    :class="{
                                        'thumbnail-selected': image.index === currentIndex,
                                    }"
                                    class="thumbnail"
                                >
                                    <a class="selectable" @click="thumbNailClickHandler(image)">
                                        <v-img
                                            :alt="`${image.type} thumbnail image #${imgindex + 1}`"
                                            :src="image.url"
                                            fluid
                                        />
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="options">
                    <span class="title">Installed Options</span>
                    <ul v-if="installedOptions && installedOptions.length > 0">
                        <li v-for="(item, optionsIndex) in installedOptions" :key="optionsIndex">{{ item }}</li>
                    </ul>
                    <p v-else>No Options Found</p>
                </div>
            </v-card>
        </v-dialog>
    </v-card>
</template>
<script>
import { call, get, sync } from "vuex-pathify";
import TableColumnSelector from "Components/TableColumnSelector";
import TableSearchCountLabel from "Components/TableSearchCountLabel";
import tableUtils from "@/util/tableUtils";
import ProgramOverlay from "Modules/Inventory/components/InventorySearch/ProgramOverlay";
import BooleanIndicator from "Components/BooleanIndicator";
import FilterChips from "Components/Search/FilterChips";
import vehicleImg from "./../icons/vehicle.png";
import Flickity from "vue-flickity";
import _ from "lodash";
import Close from "../icons/close.png";
import ExportColumnSelector from "Components/ExportColumnSelector.vue";

export default {
    name: "InventoryList",
    components: {
        ExportColumnSelector,
        FilterChips,
        BooleanIndicator,
        ProgramOverlay,
        TableColumnSelector,
        TableSearchCountLabel,
        Flickity,
    },
    props: {
        store: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            close: Close,
            selected: null,
            currentIndex: null,
            index: 0,
            focus: false,
            isExpanded: false,
            imagesList: null,
            vehicleData: null,
            flickityOptions: {
                initialIndex: 0,
                prevNextButtons: true,
                pageDots: false,
                wrapAround: true,
                imagesLoaded: true,
                cellAlign: "center",
                fullscreen: true,
            },
            fields: [
                {
                    value: "active",
                    text: "Active",
                },
                {
                    value: "pricingValid",
                    text: "Valid",
                },
                {
                    value: "stockNumber",
                    text: "Stock #",
                },
                {
                    value: "certified",
                    text: "Certified",
                },
                {
                    value: "vin",
                    text: "VIN",
                },
                {
                    value: "isSuppressed",
                    text: "Suppressed",
                    sortable: true,
                },
                {
                    value: "stockType",
                    text: "Stock Type",
                    sortable: true,
                },
                {
                    value: "ymm",
                    text: "Year Make Model",
                    sortable: false,
                },
                {
                    value: "inTransit",
                    text: "In Transit",
                    sortable: true,
                },
                {
                    value: "exteriorColor",
                    text: "Color",
                    sortable: true,
                },
                {
                    value: "year",
                    text: "Year",
                    sortable: true,
                },
                {
                    value: "make",
                    text: "Make",
                    sortable: true,
                },
                {
                    value: "model",
                    text: "Model",
                    sortable: true,
                },
                {
                    value: "miles",
                    text: "Mileage",
                    sortable: true,
                },
                {
                    value: "trim",
                    text: "Trim",
                    sortable: true,
                },
                {
                    value: "bodyStyle",
                    text: "Body Style",
                },
                {
                    value: "price",
                    text: "Price",
                    sortable: true,
                },
                {
                    text: "Dealer Location",
                    value: "address",
                    sortable: false,
                    width: 200,
                },
                {
                    text: "DMA Name",
                    value: "address.dma.name",
                    sortable: false,
                },
                {
                    text: "DMA Rank",
                    value: "address.dma.rank",
                    sortable: true,
                },
                {
                    text: "DMA Code",
                    value: "address.dma.code",
                    sortable: true,
                },
            ],
            showVehicleDetailsModal: false,
            vehicleImg: vehicleImg,
            showGallery: false,
            installedOptions: null,
        };
    },
    computed: {
        searchResults: get("inventorySearch/searchLoader@data"),
        pageNumber: get("inventorySearch/pageMetadata@number"),
        pageSize: get("inventorySearch/pageMetadata@size"),
        totalPages: get("inventorySearch/pageMetadata@totalPages"),
        totalElements: get("inventorySearch/pageMetadata@totalElements"),
        isLoading: get("inventorySearch/<EMAIL>"),
        page: sync("inventorySearch/pageable@page"),
        sort: sync("inventorySearch/pageable@sort"),
        displayFields: sync("inventorySearch/displayFields"),
        exportFields: sync("inventorySearch/exportFields"),
        sortBy: get("inventorySearch/getSortBy"),
        sortDesc: get("inventorySearch/getSortDesc"),
        exportLabels: sync("inventorySearch/exportLabels"),
        isProgramUser() {
            return this.$acl.hasAuthority("ROLE_PROGRAM");
        },
        fieldsForDisplay() {
            return tableUtils.intersectFieldsForDisplay(this.fields, this.displayFields);
        },
        dealerIds() {
            return this.$route.query.dealerIds;
        },
        programIds() {
            return this.$route.query.programIds === "" ? null : this.$route.query.programIds;
        },
        imagesQty: function () {
            return this.imagesList ? this.imagesList.length : 0;
        },
    },
    watch: {
        dealerIds(_val) {
            this.doPageLoad();
        },
        programIds(_val) {
            this.doPageLoad();
        },
        currentIndex(val) {
            this.selected = this.imagesList[val];
            this.index = this.selected.index;
        },
    },
    mounted() {
        this.doPageLoad();
        this.addProgramField();
    },

    methods: {
        init(data) {
            const isWrapped = true;
            const isInstant = false;
            let index = 0;
            let el = this.$refs.galleryContainer;
            this.imagesList = [];
            this.currentIndex = 0;

            if (data.imageUrl) {
                _.forEach(data.imageUrl, (image) => {
                    this.imagesList.push({
                        index,
                        url: image,
                        type: "inventory",
                    });
                    index++;
                });
            }

            this.selected = this.imagesList[0];

            if (this.$refs.flickity) {
                this.$refs.flickity.select(this.index, isWrapped, isInstant);
            }

            el.addEventListener("touchend", (event) => {
                this.currentIndex = this.flickityFS.selectedIndex;
                this.intoViewThumbnail();
            });

            el.addEventListener("click", (event) => {
                this.focus = !!event.target.closest("#gallery-container");

                this.currentIndex = this.$refs.flickity.selectedIndex();
                this.intoViewThumbnail();
            });

            document.addEventListener("keyup", (event) => {
                switch (event.code) {
                    case "ArrowLeft":
                        // left arrow
                        this.previous();
                        break;
                    case "ArrowRight":
                        // right arrow
                        this.next();
                        break;
                }

                this.currentIndex = this.$refs.flickity.selectedIndex();
                this.intoViewThumbnail();
            });
        },
        next() {
            const isWrapped = true;
            const isInstant = false;

            if (!this.focus) {
                return;
            }

            if (this.imagesQty === 0) {
                return;
            }

            if (this.imagesQty === this.index + 1) {
                this.index = 0;
                this.selected = this.imagesList[this.index];
                this.$refs.flickity.select(this.index, isWrapped, isInstant);
                return;
            }

            this.index++;
            this.selected = this.imagesList[this.index];
            this.$refs.flickity.select(this.index, isWrapped, isInstant);
        },
        previous() {
            const isWrapped = true;
            const isInstant = false;

            if (!this.focus) {
                return;
            }

            if (this.imagesQty === 0) {
                return;
            }

            if (this.index === 0) {
                this.index = this.imagesQty - 1;
                this.selected = this.imagesList[this.index];
                this.$refs.flickity.select(this.index, isWrapped, isInstant);
                return;
            }

            this.index--;
            this.selected = this.imagesList[this.index];
            this.$refs.flickity.select(this.index, isWrapped, isInstant);
        },
        intoViewThumbnail() {
            let thumbnail =
                this.$refs[`thumbnail${this.currentIndex}`] || document.querySelector(`thumbnail-${this.currentIndex}`);

            if (Array.isArray(thumbnail)) {
                thumbnail = thumbnail.shift();
            }

            if (thumbnail) {
                thumbnail.scrollIntoViewIfNeeded();
            }
        },
        doPageLoad: call("inventorySearch/doPageLoad"),
        doSort: call("inventorySearch/doSort"),
        changePage: call("inventorySearch/changePage"),
        updateSort: call("inventorySearch/updateSort"),
        doExport: call("inventorySearch/doExport"),
        addProgramField() {
            if (!this.isProgramUser) {
                return;
            }

            let dealerNameObject = {
                text: "Dealer Name",
                value: "dealer.name",
                sortable: true,
            };
            const programInsert = (arr, index, newItem) => [...arr.slice(0, index), newItem, ...arr.slice(index)];
            let newFields = programInsert(this.fields, 1, dealerNameObject);
            this.fields = newFields;
        },
        updateOptions(options) {
            const newSortBy = _.get(options, "sortBy[0]") || "";
            const newSortDesc = _.get(options, "sortDesc[0]") || false;

            if (newSortBy === this.sortBy && newSortBy === "") {
                return;
            }

            if (newSortBy !== this.sortBy || newSortDesc !== this.sortDesc) {
                if (newSortBy === "" || newSortDesc === "") {
                    this.updateSort("");
                } else {
                    const direction = newSortDesc ? "desc" : "asc";
                    this.updateSort(`${newSortBy},${direction}`);
                }
            }
        },
        openVehicleDetailsModal(data) {
            this.$nextTick(() => {
                this.vehicleData = data;
                this.init(data);
            });
            setTimeout(() => {
                this.showGallery = true;
            }, 5000);
            this.showVehicleDetailsModal = true;
            this.installedOptions = _.compact(data.options);
        },
        thumbNailClickHandler(image) {
            this.selected = image;
            this.index = image.index;
            const isWrapped = true;
            const isInstant = false;

            this.$refs.flickity.select(this.index, isWrapped, isInstant);
        },
        closeModal() {
            this.showVehicleDetailsModal = false;
            this.imagesList = null;
            this.showGallery = false;
            this.installedOptions = null;
        },
        prepareExport() {
            this.exportLabels.length = 0;
            for (let field of this.exportFields) {
                let header = _.find(this.fields, function (o) {
                    return _.toString(o.value) === _.toString(field);
                });
                if (header === undefined) {
                    console.error("Error setting label for export, FIELD: ", field);
                    return;
                }
                this.exportLabels = [...this.exportLabels, header.text];
            }
            this.doExport();
        },
    },
};
</script>

<style lang="scss">
@import "~vuetify/src/styles/settings/_variables";

#gallery-container {
    margin: 26px;

    #main-image-container {
        height: 267px;
        position: relative;
    }

    #main-image {
        height: 100%;
        overflow-y: hidden;

        .carousel-cell {
            width: 100%; /* full width */
            height: auto;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        img.vehicle-image-main {
            object-fit: cover;
        }

        &.is-fullscreen {
            z-index: 9999;

            .flickity-slider {
                .vehicle-image-wrapper {
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    img {
                        min-width: 100%;

                        &.vehicle-image-main {
                            object-fit: contain;
                        }
                    }
                }
            }
        }

        .flickity-viewport {
            height: 100% !important;
            @media #{map-get($display-breakpoints, 'sm-and-down')} {
                height: 180px !important;
                position: static;
            }
        }
    }

    #image-nav {
        height: 100%;
        overflow: scroll;
        max-height: 280px;

        p {
            font-size: 1rem;
        }

        .thumbnail {
            width: 33.33%;
            cursor: pointer;
            padding: 3px;
            max-width: 33.33%;
            min-width: 20%;
            scroll-snap-align: center;

            &-selected {
                opacity: 0.5;

                img {
                    box-shadow: 0 0 5px 2px rgb(22, 141, 243);
                }
            }
        }
    }

    .fullscreen-icon-wrapper {
        position: absolute;
        display: flex !important;
        bottom: 10px;
        right: 20px;
        background-color: black;

        &:hover {
            opacity: 0.5;
        }
    }

    .exit-fullscreen {
        position: fixed;
        top: 5%;
        right: 5%;
        z-index: 99999;
        cursor: pointer;
        background-color: rgba(0, 0, 0, 0.6);
        display: flex;
        padding: 3px;
        box-shadow: 0px 0px 3px 1px rgba(255, 255, 255, 0.15);
        color: white;
    }
}
</style>
<style lang="scss" scoped>
@import "~vuetify/src/styles/settings/_variables";

.v-data-table-header th {
    white-space: nowrap !important;
}

.vehicle-icon {
    height: 16px;
    max-width: 18px !important;
    margin-right: 7px;
}

span.colorSquare:before {
    content: "";
    display: inline-block;
    width: 15px;
    height: 15px;
    margin-right: 5px;
}

.colorSquare:before {
    vertical-align: middle;
    background-color: var(--vehicle-color-var);
}

.sub-title {
    font-size: 20px;
    line-height: 32px;
    font-weight: 500;
    padding-left: 26px;
    display: grid;
    margin-top: 12px;
}

.under-text {
    font-size: 14px;
    //padding-left: 26px;
    line-height: 21px;
    color: #9e9e9e;
    font-weight: 400;
}

.description {
    color: black;
    padding-left: 26px;
    font-weight: 400;
    font-size: 13px;
    line-height: 24px;
    margin: 0px;
}

.options {
    font-weight: 400;
    font-size: 14px;
    line-height: 21px;
    color: #717171;
    padding: 0px 0px 26px 26px;

    .title {
        font-weight: 500;
        font-size: 20px;
        line-height: 32px;
        color: black;
    }
}

ul {
    display: flex;
    width: 100%;
    margin: 0;
    flex-wrap: wrap;
    margin-top: 20px;
    @media #{map-get($display-breakpoints, 'sm-and-down')} {
        display: inline-block;
    }
}

li {
    width: 33%;
    flex: 0 0 33%;
    padding: 0 10px;

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
        width: 100%;
    }
}

.close-icon {
    max-width: 14px;
    height: 14px;
    right: 26px;
    top: 26px;
    float: right;
    position: absolute;
}
</style>
