<template>
    <search-page :key="dealerIds + programIds">
        <inventory-search-form slot="searchForm" />
        <inventory-list slot="searchList" :store="store" />
        <inventory-facets slot="searchFacets" />
    </search-page>
</template>
<script>
import isNil from "lodash/isNil";
import { call, sync } from "vuex-pathify";
import InventorySearchForm from "../components/InventorySearch/InventorySearchForm";
import InventoryList from "../components/InventorySearch/InventoryList";
import SearchPage from "Components/Search";
import InventoryFacets from "@/modules/Inventory/components/InventorySearch/InventoryFacets";

export default {
    components: {
        InventoryFacets,
        SearchPage,
        InventoryList,
        InventorySearchForm,
    },
    data: () => ({
        store: "inventorySearch",
    }),
    computed: {
        dealerIds() {
            let isNullOrUndefined = isNil(this.$route.query.dealerIds);
            let isEmpty = isNullOrUndefined ? true : this.$route.query.dealerIds.trim().length === 0;

            if (isNullOrUndefined || isEmpty) {
                return null;
            }

            return this.$route.query.dealerIds;
        },
        filterDealerIds: sync("inventorySearch/filters@dealerIds"),
        programIds() {
            return this.$route.query.programIds === "" ? null : this.$route.query.programIds;
        },
        filterProgramIds: sync("inventorySearch/filters@programIds"),
        filterSearchMethods: sync("inventorySearch/searchMethods"),
        filterStockTypes: sync("inventorySearch/filters@stockTypes"),
        stockTypes() {
            return this.$route.query.stockTypes;
        },
    },
    watch: {
        dealerIds(value) {
            this.filterDealerIds = value;
        },
        programIds(value) {
            this.filterSearchMethods = { ...this.filterSearchMethods, programIds: "POSITIVE" };
            this.filterProgramIds = value;
        },
    },
    created() {
        if (this.stockTypes) {
            this.filterStockTypes = this.stockTypes;
        }
        if (this.dealerIds) {
            this.filterDealerIds = this.dealerIds;
        }
        this.setSearchUri(`/vehicles`);
    },
    methods: {
        setSearchUri: call("inventorySearch/setSearchUri"),
    },
};
</script>
