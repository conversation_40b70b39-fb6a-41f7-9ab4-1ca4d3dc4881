<template>
    <v-form ref="form" lazy-validation @submit.prevent="searchHandler">
        <v-card>
            <v-card-title class="font-size-18">Search by model and year</v-card-title>
            <v-divider></v-divider>
            <v-container class="ml-0">
                <v-row>
                    <v-col cols="12" md="3">
                        <v-select
                            v-model="modelYear"
                            :items="modelYears"
                            :item-text="'modelName'"
                            :item-value="'modelValue'"
                            label="Vehicle Year"
                            outlined
                            size="sm"
                            :rules="[(v) => !!v || 'Vehicle Year is required']"
                            @change="fieldValueChangeHandler"
                        ></v-select>
                    </v-col>

                    <v-col cols="12" md="3">
                        <v-select
                            v-if="modelData && modelData.length > 0"
                            v-model="modelCode"
                            :items="modelData"
                            :item-text="'modelLineName'"
                            :item-value="'modelLineCode'"
                            label="Vehicle Model"
                            outlined
                            size="sm"
                            :rules="[(v) => !!v || 'Vehicle Model is required']"
                            @change="fieldValueChangeHandler"
                        ></v-select>
                        <v-skeleton-loader v-else type="text@3"></v-skeleton-loader>
                    </v-col>
                </v-row>

                <v-row class="mb-3">
                    <v-col cols="12" md="3">
                        <div class="d-flex align-center fill-height">
                            <div class="d-flex flex-row search-btn-wrapper">
                                <v-btn size="sm" class="mr-1 white--text" type="submit" color="primary"> Search </v-btn>
                                <v-btn text size="sm" class="ml-1" @click="resetHandler"> Reset </v-btn>
                            </div>
                        </div>
                    </v-col>
                </v-row>
            </v-container>
        </v-card>
    </v-form>
</template>

<script>
import api from "Util/api";

export default {
    name: "AccessoriesSearchForm",
    data() {
        return {
            modelYear: null,
            modelCode: null,
            modelData: [],
        };
    },
    computed: {
        modelYears() {
            const currentYear = new Date().getFullYear();
            const startYear = currentYear - 3;
            const endYear = currentYear + 1;
            const yearRange = [];

            for (let year = startYear; year <= endYear; year++) {
                yearRange.push({
                    modelName: String(year),
                    modelValue: "MY" + String(year).substring(2),
                });
            }

            return yearRange;
        },
    },
    mounted() {
        this.fetchModelDataHandler();
    },
    methods: {
        fetchModelDataHandler() {
            api.get(`/dealer/accessories/models`)
                .then((response) => {
                    this.modelData = response.data;
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        searchHandler() {
            let validate = this.$refs.form.validate();
            let formObject = { modelYear: this.modelYear, modelCode: this.modelCode };
            if (validate) {
                this.$emit("searchVehicle", formObject);
            }
        },
        resetHandler() {
            this.$refs.form.reset();
            this.fieldValueChangeHandler();
        },
        fieldValueChangeHandler() {
            this.$emit("fieldValueChange");
        },
    },
};
</script>

<style lang="scss" scoped>
.font-size-18 {
    font-size: px2rem(16);
}
</style>
