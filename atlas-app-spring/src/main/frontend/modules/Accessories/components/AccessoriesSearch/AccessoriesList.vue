<template>
    <v-card v-if="accessories && accessories.length > 0">
        <v-data-table :headers="headers" :items="accessories" :hide-default-footer="true" :items-per-page="-1">
            <template #[`item.action`]="{ item }">
                <div v-if="isLoading && selectedAccessoryId === item.id" class="ml-2">
                    <v-progress-circular indeterminate size="24" color="primary"></v-progress-circular>
                </div>
                <span v-else class="custom-underline cursor" @click="accessoriesFlagHandler(item)">
                    {{ item.isEnabled ? "Disable" : "Enable" }}
                </span>
            </template>
        </v-data-table>
    </v-card>
</template>

<script>
import api from "Util/api";
import { get } from "vuex-pathify";
export default {
    name: "AccessoriesList",
    props: {
        accessoriesData: {
            type: Array,
            required: true,
            default() {
                return [];
            },
        },
        modelYear: {
            type: String,
            required: true,
        },
        modelLineCode: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            headers: [
                { text: "Accessories", value: "name", width: 90 },
                { text: "", value: "action", width: 90 },
            ],
            accessories: this.accessoriesData,
            isLoading: false,
            selectedAccessoryId: null,
        };
    },
    computed: {
        dealer: get("accessoriesDealerStore/selectedDealer@data"),
        dealerNnaId() {
            return _.get(this.dealer, "nnaDealerId", null);
        },
    },
    methods: {
        accessoriesFlagHandler(accessoryData) {
            this.isLoading = true;
            this.selectedAccessoryId = accessoryData.id;
            let toggle = !accessoryData.isEnabled;
            api.put(`/dealer/accessories/${this.dealerNnaId}`, {
                partNumber: accessoryData.partNumber,
                name: accessoryData.name,
                modelYear: this.modelYear,
                modelLineCode: this.modelLineCode,
                toggle: toggle,
            })
                .then((response) => {
                    let index = _.findIndex(
                        this.accessories,
                        (accessory) => {
                            return accessory.name === response.data.name;
                        },
                        0
                    );
                    if (index !== -1) {
                        this.accessories.splice(index, 1, response.data);
                    }
                    this.isLoading = false;
                    this.selectedAccessoryId = null;
                })
                .catch((error) => {
                    this.isLoading = false;
                    this.selectedAccessoryId = null;
                    this.$toast.error(error.response.data.message);
                });
        },
    },
};
</script>

<style lang="scss" scoped>
.custom-underline {
    text-decoration: underline;
}
.cursor {
    cursor: pointer;
}
.v-data-table ::v-deep th {
    font-size: 16px !important;
}
</style>
