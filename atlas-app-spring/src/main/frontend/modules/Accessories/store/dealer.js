import { make } from "vuex-pathify";
import loader from "@/util/loader";
import api from "@/util/api.js";

const initialState = {
    selectedDealer: {
        data: null,
        loader: loader.defaultState(),
    },
};

const mutations = {
    ...make.mutations(initialState),
    SET_SELECTED_DEALER_LOADER(state, payload) {
        _.set(state, "selectedDealer.loader", payload);
    },
    SET_SELECTED_DEALER_DATA(state, payload) {
        _.set(state, "selectedDealer.data", payload);
    },
};

const actions = {
    ...make.actions(initialState),
    fetchDealerInfo({ commit }, dealerId) {
        commit("SET_SELECTED_DEALER_LOADER", loader.started());

        api.get(`/dealer/${dealerId}/dealer-info`)
            .then((response) => {
                commit("SET_SELECTED_DEALER_DATA", response.data);
                commit("SET_SELECTED_DEALER_LOADER", loader.successful());
            })
            .catch((error) => {
                commit("SET_SELECTED_DEALER_LOADER", loader.error(error));
            });
    },
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
};
