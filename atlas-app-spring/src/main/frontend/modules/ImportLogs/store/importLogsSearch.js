import { make } from "vuex-pathify";
import loader from "@/util/loader";
import searchUtils from "@/util/searchUtils";

const uriRoot = "/importLogs";

const initialState = {
    ...searchUtils.state(),
    searchUri: null,
    initialLoad: true,
    pushHistoryEnabled: true,
    pageable: searchUtils.parsePageableFromUrl({
        sort: "createdDate,desc",
        page: 1,
    }),
    facets: null,
    filters: searchUtils.parseFiltersFromUrl(),
    searchLoader: {
        loader: loader.defaultState(),
        data: [],
    },
    displayFields: ["created", "stockType", "vin", "message", "type", "date"],
    pageMetadata: {
        size: 0,
        totalElements: 0,
        totalPages: 0,
        number: 0,
    },

    pills: {
        dealerIds: {
            enabled: false,
        },
        vin: {
            enabled: false,
        },
        stockType: {
            enabled: false,
        },
        type: {
            enabled: false,
        },
    },
};

const mutations = {
    ...make.mutations(initialState),
    ...searchUtils.mutations(),
};

const actions = {
    ...make.actions(initialState),
    ...searchUtils.actions(uriRoot, "Import Logs Search"),
    setSearchUri({ commit, state }, searchUri) {
        commit("SET_SEARCH_URI", searchUri);
    },
};

const getters = {
    ...make.getters(initialState),
    ...searchUtils.getters(),
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
    getters,
};
