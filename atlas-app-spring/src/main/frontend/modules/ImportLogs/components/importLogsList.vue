<template>
    <v-card>
        <v-toolbar flat>
            <v-toolbar-title class="grey--text">Car Errors</v-toolbar-title>
        </v-toolbar>

        <v-divider />

        <table-search-count-label :page-number="pageNumber" :page-size="pageSize" :total-elements="totalElements" />

        <v-divider />

        <v-data-table
            :loading="isLoading"
            :headers="headers"
            :items="searchResults"
            :server-items-length="totalElements"
            :sort-by="sortBy"
            :sort-desc="sortDesc"
            hide-default-footer
            @update:options="updateOptions"
        >
        </v-data-table>

        <v-container class="pt-2">
            <v-row>
                <v-col cols="12">
                    <v-pagination :value="page" :total-visible="10" :length="totalPages" @input="changePage" />
                </v-col>
            </v-row>
        </v-container>
    </v-card>
</template>

<script>
import { get, sync, call } from "vuex-pathify";
import TableSearchCountLabel from "Components/TableSearchCountLabel";
import tableUtils from "@/util/tableUtils";

export default {
    name: "ImportLogList",
    components: {
        TableSearchCountLabel,
    },
    props: {
        store: {
            type: String,
            required: true,
        },
        dealerIdsPage: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            headers: [
                {
                    value: "jobIdFragment",
                    text: "Job Id",
                    sortable: false,
                },
                {
                    value: "stockType",
                    text: "Stock Type",
                    sortable: false,
                },
                {
                    value: "vin",
                    text: "VIN",
                    sortable: false,
                },
                {
                    value: "message",
                    text: "Message",
                    sortable: false,
                },
                {
                    value: "type",
                    text: "Step",
                    sortable: false,
                },
                {
                    value: "createdDate",
                    text: "Date",
                    sortable: false,
                },
            ],
        };
    },
    computed: {
        searchResults: get("importLogsSearch/searchLoader@data"),
        pageNumber: sync("importLogsSearch/pageMetadata@number"),
        pageSize: get("importLogsSearch/pageMetadata@size"),
        totalPages: get("importLogsSearch/pageMetadata@totalPages"),
        totalElements: get("importLogsSearch/pageMetadata@totalElements"),
        isLoading: get("importLogsSearch/<EMAIL>"),
        page: sync("importLogsSearch/pageable@page"),
        sort: sync("importLogsSearch/pageable@sort"),
        displayFields: sync("importLogsSearch/displayFields"),
        exportFields: sync("importLogsSearch/exportFields"),
        sortBy: get("importLogsSearch/getSortBy"),
        sortDesc: get("importLogsSearch/getSortDesc"),
        filterDealerIds: sync("importLogsSearch/filters@dealerIds"),
        fieldsForDisplay() {
            return tableUtils.intersectFieldsForDisplay(this.headers, this.displayFields);
        },
    },
    created() {
        this.filterDealerIds = this.dealerIdsPage;
        this.pageNumber = 0;
        this.doPageLoad();
    },
    methods: {
        doPageLoad: call("importLogsSearch/doPageLoad"),
        doSort: call("importLogsSearch/doSort"),
        changePage: call("importLogsSearch/changePage"),
        updateSort: call("importLogsSearch/updateSort"),
        updateOptions(options) {
            const newSortBy = _.get(options, "sortBy[0]") || "";
            const newSortDesc = _.get(options, "sortDesc[0]") || false;

            if (newSortBy === this.sortBy && newSortBy === "") {
                return;
            }

            if (newSortBy !== this.sortBy || newSortDesc !== this.sortDesc) {
                if (newSortBy === "" || newSortDesc === "") {
                    this.updateSort("");
                } else {
                    const direction = newSortDesc ? "desc" : "asc";
                    this.updateSort(`${newSortBy},${direction}`);
                }
            }
        },
    },
};
</script>
