<template>
    <v-form class="mb-3" @submit.prevent="doSearch">
        <v-card>
            <v-card-title>Vehicle Import Logs</v-card-title>
            <v-divider></v-divider>
            <v-container fluid>
                <v-row>
                    <v-col cols="3" style="min-width: 290px !important">
                        <v-text-field v-model="vin" label="VIN" dense outlined hide-details @keyup.enter="doSearch" />
                    </v-col>
                    <v-col cols="2">
                        <v-select
                            v-model="stockType"
                            dense
                            outlined
                            hide-details
                            :items="stockTypeOptions"
                            label="Stock Type"
                        />
                    </v-col>
                    <v-col cols="2">
                        <v-select v-model="type" dense outlined hide-details :items="logTypeOptions" label="Log Type" />
                    </v-col>
                    <v-col cols="auto">
                        <v-btn block size="sm" :loading="submitting" color="primary" dark @click.prevent="doSearch">
                            <span>Search</span>
                        </v-btn>
                    </v-col>
                    <v-col cols="6" sm="2" lg="1">
                        <v-btn block text size="sm" :loading="submitting" @click="reset">
                            <span>Reset</span>
                        </v-btn>
                    </v-col>
                </v-row>
            </v-container>
        </v-card>
    </v-form>
</template>

<script>
import { sync, dispatch, get } from "vuex-pathify";

export default {
    name: "ImportLogSearchForm",
    props: {
        stockTypePage: {
            default: "NEW",
            type: String,
            required: false,
        },
        dealerIdsPage: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            submitting: false,
            stockTypeOptions: ["NEW", "USED"],
            logTypeOptions: ["PRICING_ERROR", "DUPLICATE"],
        };
    },
    computed: {
        isLoading: get("importLogsSearch/<EMAIL>"),
        vin: sync("importLogsSearch/filters@vin"),
        type: sync("importLogsSearch/filters@type"),
        stockType: sync("importLogsSearch/filters@stockType"),
        dealerIds: sync("importLogsSearch/filters@dealerIds"),
    },
    created() {
        this.stockType = this.stockTypePage;
        this.dealerIds = this.dealerIdsPage;
    },
    methods: {
        doSearch() {
            dispatch("importLogsSearch/doSearch");
        },
        clearFilters() {
            dispatch("importLogsSearch/clearFilters");
        },
        reset() {
            this.vin = "";
            this.stockType = this.stockTypePage;
            this.dealerIds = this.dealerIdsPage;
            this.type = "PRICING_ERROR";
            this.doSearch();
        },
    },
};
</script>

<style scoped></style>
