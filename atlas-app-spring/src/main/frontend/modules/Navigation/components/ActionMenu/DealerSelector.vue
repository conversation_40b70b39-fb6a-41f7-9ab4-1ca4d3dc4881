<template>
    <v-col cols="6" sm="3" class="pb-0">
        <v-select
            :value="dealerId"
            :items="accessList"
            item-text="text"
            item-value="value"
            placeholder="Select Dealer"
            single-line
            dense
            @change="setSelectedDealer"
        ></v-select>
    </v-col>
</template>
<script>
import { get, sync } from "vuex-pathify";
import api from "@/util/api";
import lodashIsNil from "lodash/isNil";
import forEach from "lodash/forEach";
import sortBy from "lodash/sortBy";

export default {
    name: "DealerSelector",
    props: {
        dealerId: {
            type: String,
            required: false,
            default: null,
        },
    },
    data() {
        return {
            initLoad: true,
        };
    },
    computed: {
        userDealerAccessList: get("loggedInUser/userDealerAccessList"),
        userProgramDealerAccessList: get("loggedInUser/userProgramDealerAccessList"),
        selectedDealer: sync("loggedInUser/selectedDealer"),
        nesnaFeatureSubscriptionEnabled: sync("loggedInUser/nesnaFeatureSubscriptionEnabled"),
        atlasDealerTrackPhase2Enabled: sync("loggedInUser/isAtlasDealerTrackPhase2Enabled"),
        dealerAccessList() {
            return this.normalizeAccessList(this.userDealerAccessList);
        },
        programDealerAccessList() {
            const newFormattedArray = this.normalizeAccessList(this.userProgramDealerAccessList);
            return [{ text: "All Dealers", value: null }, ...newFormattedArray];
        },
        accessList() {
            if (this.isProgramUser === true) {
                return this.programDealerAccessList;
            }

            return this.dealerAccessList;
        },
        isProgramUser() {
            return this.$acl.hasAuthority("ROLE_PROGRAM");
        },
    },
    watch: {
        dealerId(val) {
            if (val == null) {
                this.selectedDealer = null;
            }
        },
    },
    created() {
        this.syncSelectedDealerInStore(this.dealerId);
        this.setSelectedDealer(this.dealerId);
    },
    methods: {
        syncSelectedDealerInStore(dealerId) {
            if (dealerId === null) {
                this.selectedDealer = null;
                return;
            }

            this.userDealerAccessList.forEach((dealer) => {
                if (dealer.id === dealerId && dealerId != null) {
                    this.selectedDealer = dealer;
                }
            });

            if (this.selectedDealer) {
                // set digital retail eligibility for site deep links (garage, certificate, etc)
                api.get(`/dealer/${dealerId}/split/enabledForDigitalRetail`)
                    .then((response) => {
                        this.selectedDealer.enabledForDigitalRetail = response.data;
                    })
                    .catch((error) => console.error(error));
            }
        },
        setSelectedDealer(dealerId) {
            let dealerIdParam = lodashIsNil(dealerId) ? "" : dealerId;
            api.post(`/users/selectedDealer?dealerId=${dealerIdParam}`)
                .then((response) => {
                    this.nesnaFeatureSubscriptionEnabled = response.data.nesnaFeatureSubscriptionEnabled;
                    this.atlasDealerTrackPhase2Enabled = response.data.atlasDealerTrackEnabled;
                })
                .catch((error) => console.error("setSelectedDealer error: ", error))
                .finally(() => {
                    if (this.initLoad) {
                        this.initLoad = false;
                        return;
                    }

                    this.$router.push({
                        params: { dealerId: dealerId },
                        query: { ...this.$route.query, dealerIds: dealerId },
                    });
                });

            this.syncSelectedDealerInStore(dealerId);
        },
        normalizeAccessList(dealerList) {
            const newFormattedArray = [];
            forEach(dealerList, (item) => {
                newFormattedArray.push({ text: item.name, value: item.id });
            });
            return sortBy(newFormattedArray, ["text"]);
        },
    },
};
</script>
