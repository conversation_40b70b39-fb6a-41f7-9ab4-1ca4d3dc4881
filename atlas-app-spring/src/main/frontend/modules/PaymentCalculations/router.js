import Vue from "vue";
import VueRouter from "vue-router";
import { routerOptions, layout, route, configureRouter } from "Util/routerHelper";
import FormPage from "Modules/ConfigPageBuilders/views/FormPage";

Vue.use(VueRouter);

const PATH_PREFIX = "/payment-calculations";
const routeModule = "PaymentCalculations";

const routes = [
    layout("layoutwithbreadcrumbs", [
        {
            name: "LeaseSettings",
            path: PATH_PREFIX + "/lease-settings",
            component: FormPage,
            props: (route) => {
                const page = "Lease Settings";
                const dealerIds = route.query.dealerIds;
                return { page, dealerIds };
            },
        },
        {
            name: "NewCarFinanceSettings",
            path: PATH_PREFIX + "/new-car-finance-settings",
            component: FormPage,
            props: (route) => {
                const page = "New Car Calculation";
                const dealerIds = route.query.dealerIds;
                return { page, dealerIds };
            },
        },
        {
            name: "UsedCarFinanceSettings",
            path: PATH_PREFIX + "/used-car-finance-settings",
            component: FormPage,
            props: (route) => {
                const page = "Used Car Calculation";
                const dealerIds = route.query.dealerIds;
                return { page, dealerIds };
            },
        },
        {
            name: "Upfront Taxes & Fees",
            path: PATH_PREFIX + "/upfront-taxes-and-fees",
            component: FormPage,
            props: (route) => {
                const page = "Upfront Taxes & Fees";
                const dealerIds = route.query.dealerIds;
                return { page, dealerIds };
            },
        },
    ]),
];

const router = new VueRouter({
    mode: "history",
    routes,
    ...routerOptions,
});

configureRouter(router);

export default router;
