<template>
    <div id="usage" class="d-flex flex-column">
        <h1 class="mb-3">Cs-Buy-Widget Usage</h1>

        <div>Add this script tag just before the ending <code>&lt;/body&gt;</code> tag</div>
        <pre class="language-js">
<code>
&lt;script type='text/javascript' src="https://widgets-dev.carsaver.com/dealer-site-plugin/cs-buy-widget.min.js"&gt;&lt;/script&gt;
</code>
</pre>
        <div class="mt-3">Add the widget to the location you want the button to appear.</div>

        <pre class="language-markup">
*Ensure dealer-id, vin & campaign-id are configured correctly, example is using placeholder values.
<code>
&lt;cs-buy-widget
    dealer-id="12345"
    vin="1234567890ABCDEFG"
    campaign-id="xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"&gt;
    Buy NOW
&lt;/cs-buy-widget&gt;
</code>
</pre>
    </div>
</template>
<script>
export default {
    name: "Usage",
};
</script>
