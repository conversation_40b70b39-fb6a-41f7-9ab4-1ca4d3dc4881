<template>
    <div id="properties" class="d-flex flex-column">
        <h1 class="mb-3">Cs-Buy-Widget Properties</h1>
        <div class="mb-4">All available properties that can be applied to the cs-buy-widget</div>

        <v-data-table
            :headers="headers"
            :items="properties"
            :items-per-page="-1"
            hide-default-footer
            disable-sort
            class="property-table"
        >
            <template #item.required="{ item }">
                <boolean-indicator v-if="item.required" :value="item.required" />
            </template>
        </v-data-table>
    </div>
</template>
<script>
import BooleanIndicator from "Components/BooleanIndicator";
export default {
    name: "Properties",
    components: { BooleanIndicator },
    data() {
        return {
            headers: [
                {
                    text: "Property",
                    value: "property",
                    width: "200px",
                },
                {
                    text: "Type",
                    value: "type",
                },
                {
                    text: "Required",
                    value: "required",
                },
                {
                    text: "Default",
                    value: "default",
                },
                {
                    text: "Description",
                    value: "description",
                },
            ],
            properties: [
                {
                    property: "vin",
                    type: "String",
                    default: "",
                    description: "VIN of the vehicle referenced, 17 Digit Vehicle Identification Number.",
                    required: true,
                },
                {
                    property: "dealer-id",
                    type: "String",
                    default: "",
                    description: "CarSaver Dealer Identifier for your dealership",
                    required: true,
                },
                {
                    property: "campaign-id",
                    type: "String",
                    default: "",
                    description:
                        "Campaign Identification Number provided by CarSaver. " +
                        "A campaign in the CarSaver platform can be thought of as a single site with its associated configurations. " +
                        "We have several Campaigns in the CarSaver platform. " +
                        "Each Campaign (or site) that runs on the CarSaver platform has an associated ID number.",
                    required: true,
                },
                {
                    property: "provider",
                    type: "String",
                    default: "",
                    description: "Dealer ID provider provided by CarSaver.",
                },
                {
                    property: "source",
                    type: "String",
                    default: "",
                    description: "Tracking source provided by CarSaver.",
                },
                {
                    property: "deal-type",
                    type: "String",
                    default: "'LOWEST'",
                    description: "The type of deal (FINANCE, LEASE, LOWEST) you want the default to be.",
                },
                {
                    property: "width",
                    type: "String",
                    default: "",
                    description: "Set's the width of the button. e.g. '200px'.",
                },
                {
                    property: "height",
                    type: "String",
                    default: "",
                    description: "Set's the height of the button. e.g. '60px'.",
                },
                {
                    property: "font",
                    type: "String",
                    default: "'bold 14px Helvetica, Arial, sans-serif'",
                    description: "Set's the font css attribute of the text in the button.",
                },
                {
                    property: "font-size",
                    type: "String",
                    default: "'14px'",
                    description: "Set's the font size of the text in the button. e.g. '16px'",
                },
                {
                    property: "font-color",
                    type: "String",
                    default: "'#ffffff'",
                    description: "Set's the font color of the content in the button.",
                },
                {
                    property: "text-align",
                    type: "String",
                    default: "'center'",
                    description: "Set's the text alignment of the text in the button. e.g. 'left', 'center', 'right'.",
                },
                {
                    property: "padding",
                    type: "String",
                    default: "'10px'",
                    description: "Set's the padding CSS attribute for the button. e.g. '10px 50px 10px 10px'.",
                },
                {
                    property: "margin",
                    type: "String",
                    default: "",
                    description: "Set's the margin CSS attribute for the button. e.g. '10px 50px 10px 10px'.",
                },
                {
                    property: "background-color",
                    type: "String",
                    default: "'#c3002f'",
                    description: "Set's the background color for the button.",
                },
                {
                    property: "hover-background-color",
                    type: "String",
                    default: "'#9d0026'",
                    description: "Set's the background color for the button when in a hover state.",
                },
                {
                    property: "test",
                    type: "Boolean",
                    default: "false",
                    description:
                        "Shows the button regardless of whether the dealer or inventory are active. " +
                        "Disables the click functionality. *Must remove before deploying to production!",
                },
            ],
        };
    },
};
</script>
<style lang="scss">
.property-table {
    border: 1px solid #e9e9e9;
}
.property-table tr:hover {
    background-color: transparent;
}
.property-table .v-data-table-header {
    background-color: rgba(0, 0, 0, 0.05);
}
.property-table tr:nth-of-type(even) {
    background-color: rgba(0, 0, 0, 0.05);
}
</style>
