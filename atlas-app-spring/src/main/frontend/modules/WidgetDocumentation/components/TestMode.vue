<template>
    <div id="test-mode" class="d-flex flex-column">
        <h1 class="mb-3">Cs-Buy-Widget Test Mode</h1>
        <div>
            Test Mode will always show the button and disable the click functionality. Make sure to not enable test mode
            in a production setting. Here is an example of a button with test mode enabled:
        </div>

        <pre class="language-markup">
<code>
&lt;cs-buy-widget
    test="true"
    dev="true"
    dealer-id="12345"
    vin="1234567890ABCDEFG"
    campaign-id="xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"&gt;
    Buy NOW
&lt;/cs-buy-widget&gt;
</code>
</pre>
    </div>
</template>
<script>
export default {
    name: "TestMode",
};
</script>
