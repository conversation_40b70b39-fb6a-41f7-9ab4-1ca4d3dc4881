<template>
    <div id="dealerCodes" class="d-flex flex-column">
        <h2>NNA Dealer Codes</h2>
        <div>
            To configure the plugin to use the NNA Dealer Code for the <strong>dealer-id</strong> value instead of the
            CarSaver DealerId set the provider property to nissan.
        </div>

        <div>Then use the associated NNA Dealer Code for the <strong>dealer-id</strong> property.</div>

        <pre class="language-markdown">
<code>provider="nissan"</code>
</pre>
        <pre class="language-markup">
<code>
&lt;cs-buy-widget
    provider="nissan"
    dealer-id="12345"
    vin="1234567890ABCDEFG"
    campaign-id="ebd33059-445a-4cde-b74a-35cb068680a6"&gt;
    Buy@Home
&lt;/cs-buy-widget&gt;
</code>
</pre>
    </div>
</template>
<script>
export default {
    name: "NnaDealerCodes",
};
</script>
