<template>
    <div id="nnaTestMode" class="d-flex flex-column my-5">
        <h2>NNA Test Mode</h2>
        <div>
            Test mode will always show the button and disable the click functionality.
            <strong>Make sure to not enable test mode in a production setting.</strong>
        </div>
        <div>Here is an example of a button with test mode enabled:</div>
        <pre class="language-markup">
*Ensure to update to the correct dealer-id, VIN and remove the test property in a production setting.
<code>
&lt;cs-buy-widget
    test="true"
    provider="nissan"
    dealer-id="12345"
    vin="1234567890ABCDEFG"
    campaign-id="ebd33059-445a-4cde-b74a-35cb068680a6"&gt;
    Buy@Home
&lt;/cs-buy-widget&gt;
</code>
</pre>
    </div>
</template>
<script>
export default {
    name: "NnaTestMode",
};
</script>
