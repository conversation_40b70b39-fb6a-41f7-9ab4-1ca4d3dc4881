import Vue from "vue";
import VueRouter from "vue-router";
import { layout, route, routerOptions } from "Util/routerHelper";

Vue.use(VueRouter);

const PATH_PREFIX = "/widget-documentation";

const routes = [layout("Simple", [route("WidgetDocumentation", "WidgetDocumentationHome", null, PATH_PREFIX)])];

const router = new VueRouter({
    mode: "history",
    routes,
    ...routerOptions,
});

export default router;
