<template>
    <iframe
        v-if="!isLoading"
        class="digital-retail-iframe"
        :src="iframeUrl"
        :title="selectedDetailsLink.name + ' Iframe'"
        height="100%"
        width="100%"
    />
</template>

<script>
import { call, get, sync } from "vuex-pathify";
import EventBus from "Util/eventBus";

export default {
    name: "DigitalRetailIframe",
    data() {
        return {
            iframeUrl: null,
        };
    },
    computed: {
        selectedDetailsLink: get("customerDetails/selectedDetailsLink"),
        selectedIframeUrl: get("customerDetails/activeDetailsLinkUrl"),
        dealerUserId: get("loggedInUser/userId"),
        dealerId: get("loggedInUser/selectedDealer@id"),
        selectedUserId: get("userDetails/<EMAIL>"),
        otpToken: get("customerDetails/<EMAIL>"),
        otpLoader: get("customerDetails/otp@loader"),
        transformIframeUrl() {
            const testStr = "//ar-";
            const constainsTestStr = this.selectedIframeUrl.includes(testStr);

            if (constainsTestStr) {
                return this.selectedIframeUrl;
            }

            return this.selectedIframeUrl.replace("://", "://ar-");
        },
        customerDetailsLinksLoader: get("customerDetails/customerDetailsLinks@loader"),
        isLoading() {
            return this.otpLoader.isLoading && this.customerDetailsLinksLoader.isLoading;
        },
    },
    watch: {
        selectedDetailsLink(newVal, oldVal) {
            const newUrl = newVal.url;
            const oldUrl = oldVal.url;

            if (newUrl !== oldUrl) {
                this.setIframeUrl();
            }
        },
    },
    created() {
        this.setIframeUrl();
        // Listen for the refresh-iframe event
        EventBus.$on("refresh-digital-retail-iframe", this.refreshIframe);
    },
    beforeDestroy() {
        // Clean up event listener when component is destroyed
        EventBus.$off("refresh-digital-retail-iframe", this.refreshIframe);
    },
    methods: {
        fetchOtp: call("customerDetails/fetchOtp"),
        getIframeUrl() {
            const baseUrl = new URL(this.transformIframeUrl);
            baseUrl.searchParams.append("dealerUserId", this.dealerUserId);
            baseUrl.searchParams.append("otp", this.otpToken);
            baseUrl.searchParams.append("dealerId", this.dealerId);

            return baseUrl.toString();
        },
        setIframeUrl() {
            this.fetchOtp({
                dealerId: this.dealerId,
                targetUserId: this.selectedUserId,
            })
                .then(() => {
                    this.iframeUrl = this.getIframeUrl();
                })
                .catch((error) => {
                    console.error("failed to fetch otp", error);
                });
        },
        /**
         * Refreshes the iframe content by reloading it
         * This can be called from parent components using a ref
         * or triggered by the 'refresh-iframe' event from any component
         */
        refreshIframe() {
            // First, get a fresh OTP token and update the URL
            this.fetchOtp({
                dealerId: this.dealerId,
                targetUserId: this.selectedUserId,
            })
                .then(() => {
                    // Update the iframe URL with the new OTP token
                    this.iframeUrl = this.getIframeUrl();

                    // If we have access to the iframe element, reload its content
                    if (this.$refs.iframe) {
                        const iframe = this.$refs.iframe;

                        // Reload the iframe content by setting the src attribute
                        // This is more reliable than using iframe.contentWindow.location.reload()
                        iframe.src = this.iframeUrl;
                    }
                })
                .catch((error) => {
                    console.error("Failed to refresh iframe:", error);
                });
        },
    },
};
</script>

<style scoped lang="scss">
.digital-retail-iframe {
    border: none;
    min-height: 790px;
}
</style>
