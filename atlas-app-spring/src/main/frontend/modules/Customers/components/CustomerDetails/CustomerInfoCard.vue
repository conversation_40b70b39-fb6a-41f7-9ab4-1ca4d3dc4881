<template>
    <v-card class="customer-info-wrapper" flat>
        <CustomerContactInfo></CustomerContactInfo>
        <CustomerInfoPillContainer />
        <v-expand-transition><CustomerCreditInfo v-show="showInfo"></CustomerCreditInfo></v-expand-transition>

        <div class="text-center">
            <span v-if="showInfo"
                ><a href="#" @click="showInfo = false">Less Info</a
                ><v-icon small color="primary">mdi-chevron-up</v-icon></span
            >
            <span v-else
                ><a href="#" @click="showInfo = true">More Info</a
                ><v-icon small color="primary">mdi-chevron-down</v-icon></span
            >
        </div>
    </v-card>
</template>

<script>
import { call, get } from "vuex-pathify";
import CustomerContactInfo from "@/modules/Customers/components/CustomerDetails/CustomerContactInfo";
import CustomerCreditInfo from "@/modules/Customers/components/CustomerDetails/CustomerCreditInfo";
import CustomerInfoPillContainer from "Modules/Customers/components/CustomerDetails/CustomerInfoPillContainer.vue";
import lodashGet from "lodash/get";
export default {
    components: { CustomerInfoPillContainer, CustomerContactInfo, CustomerCreditInfo },

    props: {
        dealerId: {
            type: String,
            required: false,
            default: null,
        },
        userId: {
            type: String,
            required: true,
        },
    },

    data: () => {
        return {
            showInfo: true,
        };
    },

    computed: {
        user: get("userDetails/userModel@data"),
        featureFlags: get("loggedInUser/featureFlags"),
        isCustomerTagsFeatureEnabled() {
            return lodashGet(this.featureFlags, "CUSTOMER_TAGS_ENHANCEMENT_TOGGLE", false);
        },
        isCustomerInfoCardProgramUserSupportFeatureEnabled() {
            return lodashGet(this.featureFlags, "CUSTOMER_INFO_CARD_PROGRAM_USER_SUPPORT_TOGGLE", false);
        },
    },

    mounted() {
        if (this.isCustomerInfoCardProgramUserSupportFeatureEnabled) {
            this.getCustomerCreditInfo({ userId: this.userId, dealerId: this.dealerId });
        } else {
            this.getCustomerCreditInfoV1({ userId: this.userId, dealerId: this.dealerId });
        }
        if (this.isCustomerTagsFeatureEnabled) {
            this.getCustomerTags({ userId: this.userId, dealerId: this.dealerId });
        }
    },

    methods: {
        getCustomerCreditInfoV1: call("customerDetails/fetchCustomerCreditInfoV1"),
        getCustomerCreditInfo: call("customerDetails/fetchCustomerCreditInfo"),
        getCustomerTags: call("customerDetails/fetchCustomerTags"),
    },
};
</script>

<style scoped>
.customer-info-wrapper {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 16px;
    padding-bottom: 12px;
}
</style>
