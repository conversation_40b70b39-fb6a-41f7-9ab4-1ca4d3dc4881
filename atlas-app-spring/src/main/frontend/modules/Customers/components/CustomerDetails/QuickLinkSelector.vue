<template>
    <div>
        <v-menu offset-y>
            <template #activator="{ on, attrs }">
                <v-btn
                    block
                    :loading="getQuickLinksLoader.isLoading"
                    color="#212121"
                    outlined
                    dark
                    v-bind="attrs"
                    v-on="on"
                >
                    Quick Links
                    <v-icon right dark>mdi-chevron-down</v-icon>
                </v-btn>
            </template>
            <v-list>
                <v-list-item
                    v-for="(quickLinkStatus, index) in links"
                    :key="index"
                    link
                    @click="handleQuickLinkStatusChange(quickLinkStatus)"
                >
                    <v-list-item-title v-text="quickLinkStatus.title" />
                </v-list-item>
            </v-list>
        </v-menu>
        <quick-link-modal
            v-if="selectedLinkType"
            :show="showDialog"
            :selected-link-type="selectedLinkType"
            @hide="handleHideDialog"
        />
    </div>
</template>

<script>
import { get, call } from "vuex-pathify";
import QuickLinkModal from "Modules/Customers/components/UserDetails/QuickLinkModal.vue";

export default {
    name: "QuickLinkSelector",
    components: { QuickLinkModal },
    props: {
        userId: {
            type: String,
            required: true,
        },
        dealerId: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            showDialog: false,
            selectedLinkType: null,
        };
    },
    computed: {
        getQuickLinksData: get("userDetails/quickLinks@data"),
        getQuickLinksLoader: get("userDetails/quickLinks@loader"),
        links() {
            return this.getQuickLinksData;
        },
    },
    mounted() {
        this.fetchQuickLinksV2({
            userId: this.userId,
            dealerId: this.dealerId,
        });
    },
    methods: {
        fetchQuickLinksV2: call("userDetails/fetchQuickLinks"),

        handleQuickLinkStatusChange(linkType) {
            this.selectedLinkType = linkType;
            this.showDialog = true;
        },
        handleHideDialog() {
            this.showDialog = false;
            this.selectedLinkType = null;
        },
    },
};
</script>

<style lang="scss" scoped>
.program-text {
    .col:first-of-type {
        align-self: center;
    }
}
.copyContainer {
    flex-direction: column;

    button {
        text-transform: none;
        width: 100%;
        margin: 0;
    }
}
.no-spacing {
    padding: 0;
    margin: 0;
}
</style>
