<template>
    <v-dialog v-model="dialog" width="600" class="custom-dialog">
        <template v-if="$slots.activator" #activator="{ on, attrs }">
            <span v-bind="attrs" v-on="on">
                <slot name="activator" />
            </span>
        </template>

        <v-form v-model="valid">
            <v-card class="custom-dialog-card">
                <v-card-title>
                    Send Customer to CRM
                    <div class="close-modal-icon mr-1">
                        <v-btn class="ml-2" text icon @click="dialog = false">
                            <v-icon small>mdi-close</v-icon>
                        </v-btn>
                    </div>
                </v-card-title>
                <v-card-subtitle> This will send the following information to the dealer's CRM </v-card-subtitle>

                <v-card-text>
                    <v-textarea
                        v-model="message"
                        :rules="[rules.maxLength(1500)]"
                        outlined
                        dense
                        label="Message to Dealer (Optional)"
                    >
                    </v-textarea>
                    <p class="customer-confirm-heading px-2 pb-1">Please confirm the following information</p>

                    <div class="customer-details px-2">
                        <div class="customer-details-line d-flex">
                            <span>Customer:</span>
                            <span>{{ name }}</span>
                        </div>
                        <div class="customer-details-line d-flex">
                            <span>Dealership:</span>
                            <span>{{ dealership }}</span>
                        </div>
                        <div v-if="vehicle" class="customer-details-line d-flex-row">
                            <span>Vehicle of Interest:</span>
                            <span>{{ vehicle }}</span>
                        </div>
                    </div>
                </v-card-text>

                <!-- ACTIONS -->
                <v-card-actions class="d-flex flex-row pb-4 px-4">
                    <v-spacer></v-spacer>
                    <v-btn large text outlined min-width="150" @click="close"> Cancel </v-btn>
                    <v-btn
                        color="primary"
                        min-width="150"
                        :disabled="!valid"
                        :loading="loading?.isLoading"
                        large
                        @click="handleSubmit"
                    >
                        Send
                    </v-btn>
                </v-card-actions>
                <!-- ACTIONS -->
            </v-card>
        </v-form>
    </v-dialog>
</template>

<script>
import { call, sync } from "vuex-pathify";
import { defineComponent } from "vue";
import lodashGet from "lodash/get";
import api from "Util/api";
import { maxLength } from "Util/formRules";
import loader from "Util/loader";

export default defineComponent({
    name: "SendToCrmModal",
    props: {
        dealerId: {
            type: String,
            required: false,
            default: null,
        },
        selectedDealerId: {
            type: String,
            required: false,
            default: null,
        },
        userId: {
            type: String,
            required: false,
            default: null,
        },
        name: {
            type: String,
            required: false,
            default: null,
        },
        dealership: {
            type: String,
            required: false,
            default: null,
        },
        vehicle: {
            type: String,
            required: false,
            default: null,
        },
        from: {
            type: String,
            required: false,
            default: "users",
            validator(value) {
                // The value must be either "customers" or "prospects"
                return ["customers", "prospects"].includes(value);
            },
        },
    },
    emits: ["submit"],
    data() {
        return {
            message: "",
            valid: false,
            loading: null,
            rules: {
                maxLength,
            },
        };
    },
    computed: {
        dialog: sync("customerDetails/messageListener@sendToCrmModal/show"),
    },
    methods: {
        fetchUserLeads: call("userDetails/fetchUserLeads"),
        async handleSubmit() {
            if (!this.dealerId && !this.selectedDealerId) {
                this.$toast.error("Please select a dealer to send CRM.");
                return;
            }
            let toastMessage;
            let endpoint;
            try {
                this.loading = loader.started();

                if (this.from === "prospects")
                    endpoint = `/dealer/${this.dealerId}/prospects/${this.userId}/send-to-crm`; // From Prospects
                else endpoint = `/dealer/${this.dealerId}/users/${this.userId}/send-to-crm`; // From customers

                const result = await api.post(endpoint, {
                    leadComment: this.message,
                });
                if (result.status !== 200) {
                    toastMessage = "Error sending customer to your CRM.";
                } else {
                    toastMessage = `${this.name} has been sent to your CRM.`;
                    this.fetchUserLeads({ dealerId: this.dealerId });
                }
                this.loading = loader.successful();
            } catch (error) {
                toastMessage = lodashGet(error, "response.data.error", "Error sending customer to your CRM.");
                this.loading = loader.error(toastMessage);
            } finally {
                this.$toast(toastMessage);
                this.close();
            }
        },
        close() {
            this.message = "";
            this.dialog = false;
        },
    },
});
</script>

<style scoped lang="scss">
p {
    margin: 0;
}
.customer-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
    .customer-details-line {
        span:first-of-type {
            color: #212121; // 1st p tag with different color
            font-weight: 700;
            padding-right: 4px;
        }
        span {
            font-size: px2rem(15);
            color: #666;
        }
    }
}
.customer-confirm-heading {
    font-size: 15px;
    line-height: 150%;
    margin-bottom: 12px;
    color: #666;
}

.modal-title {
    line-height: 1.6;
}
.close-modal-icon {
    position: absolute;
    right: 0;
}
.close-btn {
    height: 32px !important;
    padding: 10px 16px !important;
}
</style>
