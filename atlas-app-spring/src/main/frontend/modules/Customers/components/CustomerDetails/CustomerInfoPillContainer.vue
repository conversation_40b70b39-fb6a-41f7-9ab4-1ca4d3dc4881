<template>
    <div class="pill-container">
        <StageName
            v-if="user.stageName"
            :stage="user.stageName"
            :program="user.program"
            :program-subscription-stages="user.programSubscriptionStages"
        />

        <div v-for="(tag, i) in customerTags" :key="i">
            <component :is="tag.category" :pill-text="tag.name"></component>
        </div>
    </div>
</template>

<script>
import { defineComponent } from "vue";
import StageName from "Modules/Customers/components/CustomerDetails/Pills/StageName.vue";
import { get } from "vuex-pathify";
import lodashGet from "lodash/get";

export default defineComponent({
    name: "CustomerInfoPillContainer",
    components: {
        StageName,
        ["Trade"]: () => import("Modules/Customers/components/CustomerDetails/Pills/Trade.vue"),
        ["Credit"]: () => import("Modules/Customers/components/CustomerDetails/Pills/CreditFlag.vue"),
        ["Program"]: () => import("Modules/Customers/components/CustomerDetails/Pills/Program.vue"),
        ["Incentives"]: () => import("Modules/Customers/components/CustomerDetails/Pills/Incentives.vue"),
        ["Customer"]: () => import("Modules/Customers/components/CustomerDetails/Pills/PreviousCustomer.vue"),
    },
    computed: {
        user: get("userDetails/userModel@data"),
        customerTags: get("customerDetails/customerTags@data"),
    },
});
</script>

<style scoped lang="scss">
.pill-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px 12px;
}
</style>
