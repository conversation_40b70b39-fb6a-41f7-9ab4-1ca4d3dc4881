<template>
    <base-modal v-model="showModal" title="Discrepancy" :max-width="600" :min-height="359">
        <!-- Timestamp -->
        <p class="summary-text mb-6">A discrepancy was noted for this vehicle on {{ formatDate(discrepancy?.date) }}</p>

        <!-- Description -->

        <div class="description-text mb-2">
            <span class="font-weight-bold">Description:</span> {{ discrepancy?.description }}
        </div>

        <!-- Notes -->
        <div class="notes-text mb-1"><span class="font-weight-bold">Notes:</span> {{ discrepancy?.notes }}</div>

        <!-- Actions -->
        <template #actions>
            <v-spacer />
            <v-btn outlined class="cta-btn" large @click="handleDelete"> REMOVE </v-btn>
            <v-btn class="cta-btn" color="primary" large @click="handleEdit"> EDIT </v-btn>
        </template>
    </base-modal>
</template>

<script>
import BaseModal from "@/components/BaseModal.vue";
import { get, sync, call } from "vuex-pathify";
import EventBus from "Util/eventBus";
export default {
    name: "ViewDiscrepancyModal",

    components: {
        BaseModal,
    },

    computed: {
        showModal: sync("vehicleOffers/viewDiscrepancyModal@isOpen"),
        discrepancy: get("vehicleOffers/discrepancy@data"),
        dealerId: get("vehicleOffers/dealerId"),
        tradeVehicleId: get("vehicleOffers/tradeVehicleId"),
    },

    methods: {
        openEditDiscrepancyModal: call("vehicleOffers/openEditDiscrepancyModal"),
        closeViewDiscrepancyModal: call("vehicleOffers/closeViewDiscrepancyModal"),
        deleteDiscrepancy: call("vehicleOffers/deleteDiscrepancy"),
        formatDate(date) {
            if (!date) return "";
            const d = new Date(date);
            return d.toLocaleString("en-US", {
                month: "2-digit",
                day: "2-digit",
                year: "numeric",
                hour: "numeric",
                minute: "2-digit",
                hour12: true,
                timeZoneName: "short",
            });
        },

        closeModal() {
            this.closeViewDiscrepancyModal();
        },

        handleDelete() {
            this.deleteDiscrepancy({ dealerId: this.dealerId, tradeVehicleId: this.tradeVehicleId })
                .then(() => {
                    this.$toast.success("Discrepancy deleted successfully");
                    EventBus.$emit("refresh-digital-retail-iframe");
                    this.closeModal();
                })
                .catch((error) => {
                    this.$toast.error("Failed to delete discrepancy, please try again at a later time.");
                    console.error("Failed to delete discrepancy:", error);
                });
        },

        handleEdit() {
            this.openEditDiscrepancyModal({
                dealerId: this.dealerId,
                tradeVehicleId: this.tradeVehicleId,
                discrepancy: this.discrepancy,
            });
            this.closeModal();
        },
    },
};
</script>

<style lang="scss" scoped>
.summary-text,
.description-text,
.notes-text {
    color: #212121;
    font-size: 14px;
}
.cta-btn {
    max-width: 150px !important;
    margin: 0 !important;
    width: calc(50% - 4px) !important;

    &:not(:last-child) {
        margin-right: 10px !important;
    }
}
</style>
