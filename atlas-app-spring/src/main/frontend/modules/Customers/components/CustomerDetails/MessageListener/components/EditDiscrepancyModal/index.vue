<template>
    <base-modal v-model="showModal" title="Discrepancy" :max-width="600" :min-height="359">
        <v-form ref="discrepancyForm" v-model="formValid" lazy-validation>
            <v-text-field
                v-model="discrepancyDescription"
                label="Description"
                required
                counter="50"
                outlined
                :maxlength="50"
                :rules="validationRules.description"
                class="custom-counter mb-4"
            />

            <v-textarea
                v-model="discrepancyNotes"
                label="Notes"
                required
                counter="500"
                outlined
                :rules="validationRules.notes"
                class="custom-counter mb-4"
                rows="4"
                auto-grow
            />
        </v-form>

        <!-- Actions -->
        <template #actions>
            <v-spacer />
            <v-btn outlined class="cta-btn" large @click="closeModal"> CANCEL </v-btn>
            <v-btn
                class="cta-btn"
                color="primary"
                large
                :loading="loader?.isLoading"
                :disabled="!formValid"
                @click="saveDiscrepancyHandler"
            >
                SAVE
            </v-btn>
        </template>
    </base-modal>
</template>

<script>
import BaseModal from "@/components/BaseModal.vue";
import { get, sync, call } from "vuex-pathify";
import EventBus from "Util/eventBus";

export default {
    name: "EditDiscrepancyModal",

    components: {
        BaseModal,
    },

    data() {
        return {
            formValid: true,
            validationRules: {
                description: [
                    (v) => !!v || "Description is required",
                    (v) => v?.length <= 50 || "Maximum of 50 characters",
                ],
                notes: [(v) => !!v || "Notes are required", (v) => v?.length <= 500 || "Maximum of 500 characters"],
            },
        };
    },

    computed: {
        dealerId: get("loggedInUser/selectedDealer@id"),
        showModal: sync("vehicleOffers/editDiscrepancyModal@isOpen"),
        loader: get("vehicleOffers/discrepancy@loader"),
        discrepancy: sync("vehicleOffers/discrepancy@data"),
        discrepancyDescription: sync("vehicleOffers/<EMAIL>"),
        discrepancyNotes: sync("vehicleOffers/<EMAIL>"),
    },

    watch: {
        showModal(newVal) {
            // Reset validation when modal is opened
            if (newVal && this.$refs.discrepancyForm) {
                this.$nextTick(() => {
                    this.$refs.discrepancyForm.resetValidation();
                });
            }
        },
    },

    methods: {
        saveDiscrepancy: call("vehicleOffers/saveDiscrepancy"),
        closeModal() {
            this.showModal = false;
        },

        async saveDiscrepancyHandler() {
            const valid = this.$refs.discrepancyForm.validate();
            if (!valid) return;

            const payload = { ...this.discrepancy };
            try {
                await this.saveDiscrepancy(payload);
                this.$toast.success("Discrepancy updated successfully");
                EventBus.$emit("refresh-digital-retail-iframe");
                this.closeModal();
            } catch (error) {
                console.error("Failed to save discrepancy:", error);
                this.$toast.error("Failed to update discrepancy. Please try again.");
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.cta-btn {
    max-width: 150px !important;
    margin: 0 !important;
    width: calc(50% - 4px) !important;

    &:not(:last-child) {
        margin-right: 10px !important;
    }
}
.input-field {
    :deep(.v-text-field__slot) {
        input,
        textarea {
            color: #616161 !important;
        }
    }
}
</style>
<style lang="scss">
.custom-counter {
    .v-counter {
        order: -1 !important;
        margin: 0;
        padding: 0;
        margin-left: -12px !important;
        margin-right: 12px;
    }
}
</style>
