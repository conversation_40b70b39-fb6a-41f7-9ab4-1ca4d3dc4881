<template>
    <div>
        <short-url-modal />
        <send-to-crm-modal
            :user-id="userId"
            :dealer-id="dealerId"
            :selected-dealer-id="dealerId"
            :name="selectedUserFullName"
            :dealership="dealerName"
            :vehicle="selectedVehicle"
            from="customers"
        >
        </send-to-crm-modal>
        <save-deal-modal :dealer-id="dealerId" :user-id="userId"> </save-deal-modal>
        <edit-discrepancy-modal />
        <edit-offer-modal />
        <view-discrepancy-modal />
    </div>
</template>
<script>
import { get, call } from "vuex-pathify";
import ShortUrlModal from "./components/ShortUrlModal.vue";
import SendToCrmModal from "Modules/Customers/components/CustomerDetails/SendToCrmModal";
import SaveDealModal from "./components/SaveDealModal";
import EditDiscrepancyModal from "./components/EditDiscrepancyModal";
import EditOfferModal from "./components/EditOfferModal";
import ViewDiscrepancyModal from "./components/ViewDiscrepancyModal";
import EventBus from "Util/eventBus";

export default {
    name: "MessageListener",
    components: {
        SendToCrmModal,
        ShortUrlModal,
        SaveDealModal,
        EditDiscrepancyModal,
        EditOfferModal,
        ViewDiscrepancyModal,
    },
    emits: ["refresh-digit-retail-iframe"],
    data() {
        return {
            baseUrl: "",
            // Define explicit trusted origins only for development
            trustedDevOrigins: ["http://localhost:3001"],
        };
    },
    computed: {
        dealerUserId: get("loggedInUser/userId"),
        dealerId: get("loggedInUser/selectedDealer@id"),
        dealerName: get("loggedInUser/selectedDealer@name"),
        otpToken: get("customerDetails/<EMAIL>"),
        user: get("userDetails/userModel@data"),
        selectedVehicle: get("customerDetails/messageListener@sendToCrmModal/selectedVehicle"),
        userId() {
            return this.user.id;
        },
        selectedUserFullName() {
            return `${this.user.firstName} ${this.user.lastName}`;
        },
    },
    created() {
        this.fetchCustomerDetailsLinks({ userId: this.user.id, dealerId: this.dealerId });
    },
    mounted() {
        window.addEventListener("message", this.messageHandler, false);
    },
    beforeDestroy() {
        window.removeEventListener("message", this.messageHandler);
    },
    methods: {
        openOverlay: call("customerDetails/openOverlay"),
        closeOverlay: call("customerDetails/closeOverlay"),
        openShortUrlModal: call("customerDetails/openShortUrlModal"),
        fetchCustomerDetailsLinks: call("customerDetails/fetchCustomerDetailsLinks"),
        openSendToCrmModal: call("customerDetails/openSendToCrmModal"),
        openEditOfferModal: call("vehicleOffers/openEditOfferModal"),
        openEditDiscrepancyModal: call("vehicleOffers/openEditDiscrepancyModal"),
        openViewDiscrepancyModal: call("vehicleOffers/openViewDiscrepancyModal"),
        isTrustedOrigin(origin) {
            if (this.trustedDevOrigins.includes(origin)) {
                return true;
            }

            const url = new URL(origin);

            if (url.protocol !== "https:") {
                return false;
            }

            const hostname = url.hostname;
            return hostname === "carsaver.com" || hostname.endsWith(".carsaver.com");
        },
        messageHandler(event) {
            const fromTrustedOrigin = this.isTrustedOrigin(event.origin);
            const actionTypeString = typeof event.data?.action === "string";
            let actionType = null; // overlay, quick-link-modal, send-to-crm-modal, save-deal-modal

            if (!fromTrustedOrigin || !actionTypeString) {
                return;
            }

            actionType = event.data.action.toLowerCase().trim();

            switch (actionType) {
                case "overlay":
                    this.overlayHandler(event.data);
                    break;
                case "quick-link-modal":
                    this.quickLinkModalHandler(event.data);
                    break;
                case "send-to-crm-modal":
                    this.openSendToCrmModalHandler(event.data);
                    break;
                case "save-deal-modal":
                    this.saveDealModalHandler(event.data);
                    break;
                case "go-to-deal-editing":
                    this.goToDealEditing(event.data);
                    break;
                case "unsaved-form-response":
                    this.unsavedFormResponseHandler(event.data);
                    break;
                case "edit-offer-modal":
                    this.editOfferModalHandler(event.data);
                    break;
                case "edit-discrepancy-modal":
                    this.editDiscrepancyModalHandler(event.data);
                    break;
                case "add-discrepancy-modal":
                    this.addDiscrepancyModalHandler(event.data);
                    break;
                case "view-discrepancy-modal":
                    this.viewDiscrepancyModalHandler(event.data);
                    break;
                default:
                    break;
            }
        },
        saveDealModalHandler(eventData) {
            const { certificateId, dealType } = eventData.context;

            if (!certificateId || !dealType) {
                console.trace("Error: certificateId or dealType is missing");
                return;
            }

            EventBus.$emit("open-save-deal-modal", { certificateId, dealType });
        },
        unsavedFormResponseHandler(eventData) {
            const unsavedData = !!eventData.response || false;

            if (unsavedData) {
                return;
            }
            this.$emit("refresh-digit-retail-iframe");

            this.closeOverlay();
        },
        editOfferModalHandler(eventData) {
            //window.postMessage({action: "edit-offer-modal", context: {dealerId: 123}}, '*')
            const { dealerId, tradeVehicleId, offer } = eventData.context;
            const selectedUserId = this.userId;

            this.openEditOfferModal({
                dealerId,
                tradeVehicleId,
                selectedUserId,
                offer,
            });
        },
        editDiscrepancyModalHandler(eventData) {
            //window.postMessage({action: "edit-discrepancy-modal", context: {dealerId: 123}}, '*')
            const { dealerId, tradeVehicleId, discrepancy } = eventData.context;
            this.openEditDiscrepancyModal({
                dealerId,
                tradeVehicleId,
                discrepancy,
            });

            // example context:
            // {
            //     dealerId: "123",
            //     tradeVehicleId: "456",
            //     discrepancy: {
            //         id: "123", //(if available)
            //         date: "2024-01-01",
            //         description: "Description",
            //         notes: "Notes"
            //     }
            // }
        },
        addDiscrepancyModalHandler(eventData) {
            //window.postMessage({action: "add-discrepancy-modal", context: {dealerId: 123}}, '*')
            const { dealerId, tradeVehicleId } = eventData.context;
            this.openEditDiscrepancyModal({
                dealerId,
                tradeVehicleId,
                discrepancy: null,
            });
        },
        viewDiscrepancyModalHandler(eventData) {
            //window.postMessage({action: "view-discrepancy-modal", context: {dealerId: 123}}, '*')
            const { dealerId, tradeVehicleId, discrepancy } = eventData.context;
            this.openViewDiscrepancyModal({
                dealerId,
                tradeVehicleId,
                discrepancy,
            });
        },
        goToDealEditing(eventData) {
            if (!eventData.context?.certificateId) {
                console.trace("Error: certificateId is missing");
                return;
            }

            const baseHost = window.location.origin;
            const dealId = eventData.context?.certificateId;
            const url = `${baseHost}/customers/${this.userId}/deal/${dealId}?dealerIds=${this.dealerId}`;

            window.open(url, "_self");
        },
        openSendToCrmModalHandler(eventData) {
            const vehicleData = eventData.context.vehicle || {};
            const selectedVehicle = `${vehicleData.year} ${vehicleData.make} ${vehicleData.model} ${vehicleData.trim}`;

            this.openSendToCrmModal(selectedVehicle);
        },
        overlayHandler(eventData) {
            const newIframeUrl = eventData.url || "";
            const overlayType = eventData.type.toLowerCase().trim();

            if (overlayType === "close") {
                this.closeOverlay();
                return;
            }

            this.openOverlay({
                iframeUrl: newIframeUrl,
                dealerId: this.dealerId,
                dealerUserId: this.dealerUserId,
                otpToken: this.otpToken,
            });
        },
        removeArPrefix(url) {
            return url.replace("://ar-", "://");
        },
        quickLinkModalHandler(eventData) {
            const { program, title, overlayUrl } = eventData.context;
            const { disableOpenPageButton, enableCloseButton } = eventData;
            const url = eventData.url || "";
            const transformedUrl = this.removeArPrefix(url);

            this.openShortUrlModal({
                longUrl: transformedUrl,
                overlayUrl: overlayUrl,
                program: program,
                title: title,
                disableOpenPageButton: disableOpenPageButton,
                enableCloseButton: enableCloseButton,
            });
        },
    },
};
</script>
