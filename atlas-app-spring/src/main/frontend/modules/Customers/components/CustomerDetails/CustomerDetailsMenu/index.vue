<template>
    <div>
        <MobileMenu v-show="mobile" />
        <DesktopMenu v-show="!mobile" />
    </div>
</template>

<script>
import DesktopMenu from "./components/DesktopMenu.vue";
import MobileMenu from "./components/MobileMenu.vue";

export default {
    name: "CustomerDetailsMenu",
    components: { DesktopMenu, MobileMenu },
    props: {
        mobile: {
            type: Boolean,
            required: false,
            default: false,
        },
    },
};
</script>

<style scoped lang="scss"></style>
