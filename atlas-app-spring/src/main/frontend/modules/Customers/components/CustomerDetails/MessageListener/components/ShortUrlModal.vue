<template>
    <v-dialog :value="show" persistent width="600">
        <v-card class="content">
            <v-card-title>
                <div class="title-section w-100">
                    {{ title }}
                    <button class="close-btn" @click="close">
                        <v-icon class="close-icon">mdi-close</v-icon>
                    </button>
                </div>
            </v-card-title>
            <v-card-subtitle> Program: {{ programName }} - {{ hostUrl }} </v-card-subtitle>
            <v-card-text class="quick-link-inputs">
                <v-text-field readonly outlined dense label="URL" hide-details :value="selectedUrl"></v-text-field>
                <v-checkbox v-model="shortUrlCheck" hide-details dense label="Short URL"></v-checkbox>
            </v-card-text>
            <v-card-actions class="card-actions">
                <v-spacer class="d-none d-sm-block" />
                <v-btn v-if="enableCloseButton" outlined class="close-btn" @click="close">Close</v-btn>
                <v-btn v-if="!disableOpenPageButton" outlined class="close-btn" @click="openPage">OPEN PAGE</v-btn>
                <v-btn :loading="isLoading" color="primary" @click="handleCopy">{{ copyText }}</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script>
import { call, get, sync } from "vuex-pathify";

export default {
    name: "ShortUrlModal",
    data() {
        return {
            shortUrlCheck: true,
            copyText: "Copy link to clipboard",
        };
    },
    computed: {
        show: sync("customerDetails/<EMAIL>"),
        title: get("customerDetails/<EMAIL>"),
        programName: get("customerDetails/<EMAIL>"),
        hostUrl: get("customerDetails/<EMAIL>"),
        longUrl: get("customerDetails/<EMAIL>"),
        shortUrl: get("customerDetails/<EMAIL>"),
        overlayUrl: get("customerDetails/<EMAIL>"),
        disableOpenPageButton: get("customerDetails/<EMAIL>"),
        enableCloseButton: get("customerDetails/<EMAIL>"),
        isLoading: get("customerDetails/<EMAIL>"),
        selectedUrl() {
            return this.shortUrlCheck ? this.shortUrl : this.removeArPrefix(this.longUrl);
        },
    },
    methods: {
        openOverlay: call("customerDetails/openOverlay"),
        openPage() {
            this.openOverlay({ iframeUrl: this.overlayUrl });
            this.close();
        },
        close() {
            this.show = false;
            this.reset();
        },
        handleCopy() {
            navigator.clipboard.writeText(this.selectedUrl);
            this.copyText = "Copied!";
        },
        reset() {
            this.copyText = "Copy link to clipboard";
            this.shortUrlCheck = true;
        },
        removeArPrefix(url) {
            return url.replace("://ar-", "://");
        },
    },
};
</script>

<style scoped lang="scss">
@import "~vuetify/src/styles/settings/_variables";
.title-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
    margin: 0;

    .close-btn {
        padding: 0;
        background: none;
        border: none;
        cursor: pointer;

        .close-icon {
            right: -5px;
        }
    }
}
.quick-link-inputs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 24px;
    > * {
        margin: 0 !important;
    }
}

.content {
    min-height: 196px;
}

.card-actions {
    @media #{map-get($display-breakpoints, 'md-and-down')} {
        display: flex !important;
        flex-direction: column-reverse !important;
        gap: 8px;
        > button {
            align-self: stretch !important;
            width: 100% !important;
            margin: 0 !important;
        }
    }

    > button {
        padding-left: 18px !important;
        padding-right: 18px !important;
    }
}
</style>
