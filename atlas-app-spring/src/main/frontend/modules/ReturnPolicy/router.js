import Vue from "vue";
import VueRouter from "vue-router";
import { configureRouter, layout, route, routerOptions } from "Util/routerHelper";

Vue.use(VueRouter);

const PATH_PREFIX = "/return-policy";

const isProgramReturnPolicyEnabled = window._CS_FEATURE_FLAGS?.PROGRAM_RETURN_POLICY_FEATURE ?? false;

const routes = isProgramReturnPolicyEnabled
    ? [layout("Default", [route("ReturnPolicy", "ProgramReturnPolicyHome", null, PATH_PREFIX)])]
    : [layout("Default", [route("ReturnPolicy", "ReturnPolicyHome", null, PATH_PREFIX)])];

const router = new VueRouter({
    mode: "history",
    routes,
    ...routerOptions,
});

configureRouter(router);

export default router;
