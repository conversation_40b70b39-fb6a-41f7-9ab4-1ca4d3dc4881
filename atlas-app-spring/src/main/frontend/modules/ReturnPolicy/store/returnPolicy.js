import { make } from "vuex-pathify";
import loader from "@/util/loader";
import api from "Util/api";

const initialState = {
    returnPolicyData: {
        data: {},
        loader: loader.defaultState(),
    },
    dealerPrograms: {
        data: [],
        loader: loader.defaultState(),
    },
};

const actions = {
    ...make.actions(initialState),

    saveAndPublish({ commit, state }, formData) {
        api.post(`/return-policy`, formData)
            .then((response) => {
                commit("SET_SAVE_AND_PUBLISH_DATA", response.data);
                commit("SET_SAVE_AND_PUBLISH_LOADER", loader.successful());
            })
            .catch((error) => {
                console.log(error);
                commit("SET_SAVE_AND_PUBLISH_LOADER", loader.error(error));
            })
            .finally(() => {
                this.loading = false;
            });
    },

    getDealerPrograms({ commit, state }, dealerId) {
        commit("SET_DEALER_PROGRAMS_LOADER", loader.started());

        return api
            .get(`/dealer/${dealerId}/programs`)
            .then((response) => {
                let result = [];
                response.data.forEach((element) => {
                    let program = {};
                    program.value = element.programId;
                    program.text = element.name;
                    result.push(program);
                });
                commit("SET_DEALER_PROGRAMS", result);
                commit("SET_DEALER_PROGRAMS_LOADER", loader.successful());
            })
            .catch((error) => {
                console.log(error);
                commit("SET_DEALER_PROGRAMS_LOADER", loader.error(error));
            });
    },
};

const mutations = {
    ...make.mutations(initialState),

    SET_SAVE_AND_PUBLISH_DATA: (state, payload) => {
        state.returnPolicyData.data = payload;
    },
    SET_SAVE_AND_PUBLISH_LOADER: (state, payload) => {
        state.returnPolicyData.loader = payload;
    },
    SET_DEALER_PROGRAMS: (state, payload) => {
        state.dealerPrograms.data = payload;
    },
    SET_DEALER_PROGRAMS_LOADER: (state, payload) => {
        state.dealerPrograms.loader = payload;
    },
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
};
