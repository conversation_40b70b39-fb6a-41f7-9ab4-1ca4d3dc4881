<template>
    <v-form
        ref="allCardsForm"
        v-model="isValidForm"
        lazy-validation
        class="container return-policy-page-container container--fluid"
    >
        <v-card outlined class="mb-3">
            <div class="d-flex align-center justify-space-between pa-3">
                <div>Customize Return Policy</div>
                <div>
                    <v-btn
                        :disabled="!hasWebsiteReturnPolicyEditAccess || !isValidForm"
                        color="primary"
                        @click="checkBoxesRule"
                    >
                        SAVE AND PUBLISH
                    </v-btn>
                </div>
            </div>
        </v-card>

        <v-container class="pa-0 ma-0" fluid>
            <v-row no-gutters>
                <v-col cols="12" md="6" class="pr-3">
                    <div class="pa-3">
                        <div class="font-weight-bold mb-3">Preview</div>
                        <v-card outlined class="pa-3">
                            <div class="font-weight-bold mb-1">Banner Preview</div>
                            <v-divider class="divider mb-3" />

                            <v-card v-if="formData.isActive" outlined class="preview-card-policy pa-3 mb-3">
                                <div class="d-flex justify-center pa-2">
                                    <car-shield-s-v-g class="shield"></car-shield-s-v-g>
                                    <h2 class="text-center">{{ formData.days }} - Day {{ formData.policyName }}</h2>
                                </div>
                                <div class="d-flex cta-card-title font-weight-bold center justify-center">
                                    {{ formData.policySubTitle }}
                                </div>
                                <div class="d-flex cta-card-title justify-center">
                                    Provided by
                                    <div class="font-weight-bold font-italic" style="margin-left: 2px">
                                        {{ dealerName }}
                                    </div>
                                </div>
                                <br />
                                <a class="d-flex cta-card-title justify-center" style="color: #c3002f">Learn More</a>
                            </v-card>

                            <div class="font-weight-bold mb-1">Modal Preview</div>
                            <v-divider class="divider mb-3" />

                            <v-card v-if="formData.isActive" outlined class="preview-card-policy pa-3">
                                <div class="d-flex justify-center pa-2">
                                    <CarIcon class="carIcon"></CarIcon>
                                </div>
                                <div class="d-flex justify-center pa-2">
                                    <car-shield-s-v-g class="shield"></car-shield-s-v-g>
                                    <h2 class="text-center">{{ formData.days }} - Day {{ formData.policyName }}</h2>
                                </div>
                                <div class="d-flex cta-card-title font-weight-bold center justify-center">
                                    {{ formData.policySubTitle }}
                                </div>
                                <div class="d-flex cta-card-title justify-center">
                                    Provided by
                                    <div class="font-weight-bold font-italic" style="margin-left: 2px">
                                        {{ dealerName }}
                                    </div>
                                </div>
                                <br />
                                <div class="d-flex justify-center pa-2">
                                    <hr class="hr" />
                                </div>
                                <div class="d-flex justify-center pa-2">
                                    <v-container fluid>
                                        <v-textarea
                                            v-model="formData.policyDescription"
                                            no-resize
                                            variant="solo"
                                            type="text"
                                            no-input
                                        ></v-textarea>
                                    </v-container>
                                </div>
                                <div class="text-center">
                                    <a
                                        v-if="formData.isActiveDisclosureLink"
                                        :href="formData.disclosureLink"
                                        target="_blank"
                                        style="color: #c3002f"
                                        >Read Dealer’s Return Policy</a
                                    >
                                </div>
                            </v-card>
                        </v-card>
                    </div>
                </v-col>
                <v-col cols="12" md="6">
                    <v-card outlined class="cta-forms-card pa-3">
                        <div class="mb-3">
                            Use the toggle switch and forms below to activate and customize your Return Policy as shown
                            on Buy@Home.
                        </div>
                        <div class="font-weight-bold mb-3 disclaimer">
                            Consult with your legal counsel in the development of your Return Policy Program; the Policy
                            Name, and the Subtitle/tagline.
                        </div>
                        <v-card outlined class="cta-card pa-3 mb-3">
                            <div class="d-flex justify-space-between">
                                <div class="custom-ml-8">
                                    <div class="cta-card-title font-weight-bold">Vehicle Return Policy</div>
                                    <div class="cta-card-subtitle">
                                        Give users peace of mind with the ability to return their vehicle
                                    </div>
                                </div>
                                <div>
                                    <v-switch v-model="formData.isActive"></v-switch>
                                </div>
                            </div>
                            <v-divider class="mb-3" />

                            <div class="mb-lg-4 py-2">
                                <div class="justify-space-between">
                                    <div class="label">
                                        Select the vehicle types you want to apply the Vehicle Return Policy to:
                                    </div>
                                    <div class="form-check py-2">
                                        <input
                                            id="flexCheckDefault"
                                            v-model="formData.vehicleType.isNewVehicle"
                                            :disabled="!formData.isActive"
                                            class="form-check-input"
                                            type="checkbox"
                                            value=""
                                        />
                                        <label class="form-check-label" for="flexCheckDefault"> New Vehicle </label>
                                    </div>
                                    <div class="form-check py-2">
                                        <input
                                            id="flexCheckChecked2"
                                            v-model="formData.vehicleType.isUsedVehicle"
                                            :disabled="!formData.isActive"
                                            class="form-check-input"
                                            type="checkbox"
                                            value=""
                                            checked
                                        />
                                        <label class="form-check-label" for="flexCheckChecked2"> Used Vehicle </label>
                                    </div>
                                    <div class="form-check py-2">
                                        <input
                                            id="flexCheckChecked3"
                                            v-model="formData.vehicleType.isCPOVehicle"
                                            :disabled="!formData.isActive"
                                            class="form-check-input"
                                            type="checkbox"
                                            value=""
                                            checked
                                        />
                                        <label class="form-check-label" for="flexCheckChecked3"> CPO Vehicle </label>
                                    </div>
                                </div>
                            </div>
                            <div class="cta-card-title font-weight-bold py-2">Customize your Return Policy</div>

                            <div class="d-flex">
                                <span class="d-flex mr-auto p-2 text-spacing"># of days for Return Policy:</span>
                                <div style="color: red; padding: 5px">*</div>
                                <v-responsive class="py-2" style="margin-right: 282px" max-width="160">
                                    <v-text-field
                                        ref="daysField"
                                        v-model="formData.days"
                                        :disabled="!formData.isActive"
                                        :rules="[numberRule]"
                                        clearable
                                        outlined
                                        label="Number of days"
                                        type="number"
                                        hint="Max 90 Days"
                                        persistent-hint
                                    ></v-text-field>
                                </v-responsive>
                            </div>

                            <div class="d-flex">
                                <span class="d-flex mr-auto p-2 text-spacing">Return Policy Name:</span>
                                <div style="color: red; padding: 5px">*</div>
                                <v-responsive class="py-2" max-width="444">
                                    <v-text-field
                                        v-model="formData.policyName"
                                        :disabled="!formData.isActive"
                                        :rules="policyNameRule"
                                        counter
                                        max="19"
                                        clearable
                                        outlined
                                        label="Add return policy name"
                                        type="input"
                                        hint="Max 20 characters"
                                        persistent-hint
                                        :maxlength="20"
                                    ></v-text-field>
                                </v-responsive>
                            </div>
                            <div class="d-flex">
                                <span class="mr-auto p-2 text-spacing">Sub-title:</span>
                                <div style="color: red; padding: 5px">*</div>
                                <v-responsive class="py-2" max-width="444">
                                    <v-text-field
                                        v-model="formData.policySubTitle"
                                        :disabled="!formData.isActive"
                                        :rules="policySubTitleRule"
                                        clearable
                                        counter
                                        outlined
                                        label="Add Return Policy Sub-title"
                                        type="input"
                                        hint="Max 60 characters"
                                        persistent-hint
                                        :maxlength="60"
                                    ></v-text-field>
                                </v-responsive>
                            </div>

                            <div class="d-flex">
                                <p class="mr-auto p-2 text-spacing">
                                    Description: <br />
                                    This information will appear <br />
                                    in a modal after a user <br />
                                    selects "Learn More"
                                </p>
                                <div style="color: red; padding: 5px">*</div>
                                <v-responsive class="py-2" max-width="444">
                                    <v-textarea
                                        v-model="formData.policyDescription"
                                        :disabled="!formData.isActive"
                                        :rules="policyDescriptionRule"
                                        counter
                                        :maxlength="500"
                                        clearable
                                        no-resize
                                        rows="1"
                                        height="250px"
                                        outlined
                                        label="Add Return Policy Description"
                                        type="input"
                                        hint="Max 500 characters"
                                        persistent-hint
                                    ></v-textarea>
                                </v-responsive>
                            </div>

                            <v-divider class="mb-3" />

                            <div class="d-flex justify-space-between">
                                <div class="custom-ml-8">
                                    <div class="cta-card-title font-weight-bold">
                                        Include the URL to your website's Vehicle Return disclosures
                                    </div>
                                </div>
                                <div>
                                    <v-switch v-model="formData.isActiveDisclosureLink"></v-switch>
                                </div>
                            </div>

                            <div class="d-flex">
                                <span class="mr-auto p-2 text-spacing">Disclosure Link</span>
                                <div style="color: red; padding: 5px">*</div>
                                <v-responsive class="py-2" max-width="444">
                                    <v-text-field
                                        v-model="formData.disclosureLink"
                                        :disabled="!formData.isActiveDisclosureLink"
                                        :rules="urlValidation"
                                        outlined
                                        clearable
                                        label="Add URL for Disclosure"
                                        type="input"
                                    ></v-text-field>
                                </v-responsive>
                            </div>
                        </v-card>
                    </v-card>
                </v-col>
            </v-row>
        </v-container>
    </v-form>
</template>

<script>
import { call, get } from "vuex-pathify";
import _ from "lodash";
import CarShieldSVG from "@/modules/ReturnPolicy/views/CarShieldSVG";
import CarIcon from "@/modules/ReturnPolicy/views/CarIcon";
import api from "Util/api";
import isNil from "lodash/isNil";

const MAX_CHARACTER_LENGTH = 57;
const MAX_CHARACTER_LENGTH_RETURN_POLICY_NAME = 19;
const MAX_DESCRIPTION_LENGTH = 800;
const MAX_DAYS = 90;
const MAX_NAME_CHARACTERS = 20;
const MAX_SUBTITLE_CHARACTERS = 60;
const MAX_DESCRIPTION_CHARACTERS = 500;

export default {
    components: { CarShieldSVG, CarIcon },
    data() {
        return {
            formData: {
                dealerId: this.dealerId,
                isActive: false,
                vehicleType: {
                    isNewVehicle: false,
                    isUsedVehicle: false,
                    isCPOVehicle: false,
                },
                days: null,
                policyDescription: null,
                policyName: null,
                policySubTitle: null,
                isActiveDisclosureLink: false,
                disclosureLink: null,
            },
            numberRule: (v) => {
                if (!v || !String(v).trim()) {
                    return "Number is required";
                }
                if (isNaN(parseFloat(v)) || v < 0 || v > MAX_DAYS) {
                    return "Number has to be between 0 and " + MAX_DAYS;
                }
                return true;
            },
            policyNameRule: [
                (v) => !!v || "A name is required",
                (v) => {
                    const result =
                        (v && v.length <= MAX_NAME_CHARACTERS) ||
                        "The name must be less than " + MAX_NAME_CHARACTERS + " characters";
                    return result;
                },
            ],
            policySubTitleRule: [
                (v) => !!v || "A subtitle is required",
                (v) => {
                    const result =
                        (v && v.length <= MAX_SUBTITLE_CHARACTERS) ||
                        "The policy subtitle must be less or equal than " + MAX_SUBTITLE_CHARACTERS + " characters";
                    return result;
                },
            ],
            policyDescriptionRule: [
                (v) => !!v || "A description is required",
                (v) => {
                    const result =
                        (v && v.length <= MAX_DESCRIPTION_CHARACTERS) ||
                        "The description must be less or equal than " + MAX_DESCRIPTION_CHARACTERS + " characters";
                    return result;
                },
            ],
            urlValidation: [(value) => !!value || "Required.", (value) => this.isURL(value) || "URL is not valid"],
            isLoading: false,
            isValidForm: true,
            types: ["text", "number", "email", "password", "search", "url", "tel", "date", "time", "range", "color"],
            maxChars: 20,
        };
    },
    computed: {
        dealerId() {
            let result = undefined;

            if (this.$route.params.dealerId) {
                result = this.$route.params.dealerId;
            } else if (this.$route.query.dealerIds) {
                result = this.$route.query.dealerIds;
            }

            return result;
        },
        hasWebsiteReturnPolicyEditAccess() {
            const result = this.$acl.hasDealerPermission(this.dealerId, "return-policy:edit");
            return result;
        },
        userDealerAccessList: get("loggedInUser/userDealerAccessList"),
        dealerName() {
            const locatedDealer = _.find(this.userDealerAccessList, ["id", this.dealerId]);
            const result = !_.isNil(locatedDealer) ? locatedDealer.name : "";
            return result;
        },
    },
    created() {
        this.loadValues();
    },
    mounted() {
        this.$nextTick(() => {
            this.$refs.daysField.validate(true);
        });
    },
    methods: {
        fetchPolicyData: call("returnPolicy/fetchReturnPolicyData"),
        doSaveAndPublish: call("returnPolicy/saveAndPublish"),
        saveAndPublish() {
            this.formData.dealerId = this.dealerId;
            this.doSaveAndPublish(this.formData);
        },
        checkBoxesRule() {
            const vehicleTypeList = [];
            vehicleTypeList[0] = this.formData.vehicleType.isNewVehicle;
            vehicleTypeList[1] = this.formData.vehicleType.isUsedVehicle;
            vehicleTypeList[2] = this.formData.vehicleType.isCPOVehicle;

            if (vehicleTypeList.includes(true)) {
                this.saveAndPublish();
            } else {
                alert("At least one vehicle type needs to be selected");
            }
        },
        loadValues() {
            const result = api
                .get(`/return-policy/${this.dealerId}`)
                .then((response) => {
                    console.log("Values loaded: " + response);
                    if (!isNil(response) && _.size(response.data) > 0) {
                        this.formData = response.data;
                    }
                })
                .catch((error) => {
                    console.log(error);
                });

            return result;
        },
        isURL(str) {
            let url;
            try {
                url = new URL(str);
            } catch (_) {
                return false;
            }
            return url.protocol === "http:" || url.protocol === "https:";
        },
        truncateText(value) {
            const input = value;
            if (input.length > this.maxChars) {
                value = input.slice(0, this.maxChars);
                this.formData.policyName = value;
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.dealer-page-container {
    background-color: $gray-200;
}
.shield {
    margin-right: 10px;
    color: darkred;
    height: 40px;
    width: 40px;
}
.carIcon {
    color: darkred;
    height: 130px;
    width: 130px;
}
.hr {
    overflow: visible;
    height: 0;
    width: 450px;
    border-color: red;
}
.preview-card-policy {
    .border-div {
        border: 1px solid #0000001f;
        border-radius: 3px;
    }
}
.disclaimer {
    background-color: lightgrey;
}
.text-spacing {
    margin-top: 20px;
    margin-left: 20px;
}
</style>
