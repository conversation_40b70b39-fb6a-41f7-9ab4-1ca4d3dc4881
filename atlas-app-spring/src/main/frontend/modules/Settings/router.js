import Vue from "vue";
import VueRouter from "vue-router";
import { routerOptions, layout, configureRouter, route } from "Util/routerHelper";
import FormPage from "Modules/ConfigPageBuilders/views/FormPage";
import loggedInUser from "@/store/loggedInUser";
import PreviewFormPage from "@/modules/ConfigPageBuilders/views/PreviewFormPage.vue";

Vue.use(VueRouter);

const SETTINGS_PATH_PREFIX = "/settings";

const propsUtil = (route, pageName) => {
    return (route) => {
        const page = pageName;
        const dealerIds = route.query.dealerIds;

        return { page, dealerIds };
    };
};

const routes = [
    layout("config", [
        {
            name: "NewCarFinanceSettings",
            path: SETTINGS_PATH_PREFIX + "/new-car-finance-settings",
            component: FormPage,
            props: (route) => {
                const page = "New Car Calculation";
                const dealerIds = route.query.dealerIds;
                return { page, dealerIds };
            },
        },
        {
            name: "UsedCarFinanceSettings",
            path: SETTINGS_PATH_PREFIX + "/used-car-finance-settings",
            component: FormPage,
            props: (route) => {
                const page = "Used Car Calculation";
                const dealerIds = route.query.dealerIds;
                return { page, dealerIds };
            },
        },
        {
            name: "ClientTheming",
            path: SETTINGS_PATH_PREFIX + "/client-theming",
            component: FormPage,
            props: (route) => {
                const page = "Client Theming";
                const dealerIds = route.query.dealerIds;
                return { page, dealerIds };
            },
        },
        {
            name: "MyDealPage",
            path: SETTINGS_PATH_PREFIX + "/my-deal-page",
            component: FormPage,
            props: (route) => {
                const page = "My Deal Page";
                const dealerIds = route.query.dealerIds;
                return { page, dealerIds };
            },
        },
        {
            name: "SmartLinksQrCodes",
            path: SETTINGS_PATH_PREFIX + "/smart-links-qr-codes",
            component: PreviewFormPage,
            props: propsUtil(route, "Smart Links QR Codes"),
        },
        {
            name: "CTAButtons",
            path: SETTINGS_PATH_PREFIX + "/cta-buttons",
            component: PreviewFormPage,
            props: propsUtil(route, "CTA Buttons"),
        },
        {
            name: "OverlaySettings",
            path: SETTINGS_PATH_PREFIX + "/overlay-settings",
            component: PreviewFormPage,
            props: propsUtil(route, "Overlay Settings"),
        },
        {
            name: "RateSheetConfiguration",
            path: SETTINGS_PATH_PREFIX + "/rate-sheet-configuration",
            component: FormPage,
            props: (route) => {
                const page = "Rate Sheet Configuration";
                const dealerIds = route.query.dealerIds;
                return { page, dealerIds };
            },
        },
        {
            name: "LeadPreferences",
            path: SETTINGS_PATH_PREFIX + "/lead-preferences",
            component: FormPage,
            props: (route) => {
                const page = "Lead Preferences";
                const dealerIds = route.query.dealerIds;
                return { page, dealerIds };
            },
        },
        {
            name: "GraphicsAndImages",
            path: SETTINGS_PATH_PREFIX + "/graphics-and-images",
            component: PreviewFormPage,
            props: (route) => {
                const page = "Graphics And Images";
                const dealerIds = route.query.dealerIds;
                return { page, dealerIds };
            },
        },
        {
            name: "Banners",
            path: SETTINGS_PATH_PREFIX + "/banners",
            component: FormPage,
            props: (route) => {
                const page = "Banners";
                const dealerIds = route.query.dealerIds;
                return { page, dealerIds };
            },
        },
    ]),
];

const router = new VueRouter({
    mode: "history",
    routes,
    ...routerOptions,
});

router.beforeEach((to, from, next) => {
    const featureFlags = loggedInUser.state.featureFlags || {};
    const redirectToDealerDetails = (to, next) => next(`/dealer/details?dealerIds=${to.query.dealerIds}`);

    if (to.path.includes(SETTINGS_PATH_PREFIX + "/smart-links-qr-codes") && !featureFlags["QR_CODE_SETTINGS_ENABLED"]) {
        redirectToDealerDetails(to, next);
    } else if (to.path.includes(SETTINGS_PATH_PREFIX) && !featureFlags["DRAWER_SETTINGS_ENABLED"]) {
        redirectToDealerDetails(to, next);
    } else {
        next();
    }
});

configureRouter(router);

export default router;
