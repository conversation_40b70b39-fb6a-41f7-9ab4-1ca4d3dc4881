<template>
    <reporting-metrics-row
        title="Leads"
        :is-loading="isLoading"
        :affected-by-date-range="true"
        :tooltip-content="headerTooltipContent"
    >
        <v-col cols="12" md="4" class="d-flex align-center justify-center">
            <div class="lead-pie-chart-wrapper">
                <apexchart
                    width="100%"
                    height="100%"
                    type="pie"
                    :series="stockTypeMetrics.series"
                    :options="stockTypeMetrics.chartOptions"
                />
            </div>
        </v-col>
        <v-col cols="12" md="4" class="d-flex align-center justify-center">
            <div class="lead-pie-chart-wrapper">
                <v-tooltip top open-on-focus>
                    <template #activator="{ on, attrs }">
                        <v-icon color="grey" dark v-bind="attrs" small class="bureau-icon" v-on="on">
                            mdi-information-outline
                        </v-icon>
                    </template>
                    <span
                        >This chart indicates whether the user's credit tier was determined by a call to the credit
                        bureau or whether the user self-selected their credit tier</span
                    >
                </v-tooltip>
                <apexchart
                    width="100%"
                    height="100%"
                    type="pie"
                    :series="prequalMetric.series"
                    :options="prequalMetric.chartOptions"
                />
            </div>
        </v-col>
        <v-col cols="12" md="4" class="d-flex align-center justify-center">
            <div class="lead-chart-wrapper">
                <v-tooltip top open-on-focus>
                    <template #activator="{ on, attrs }">
                        <v-icon color="grey" dark v-bind="attrs" small class="close-rate-icon" v-on="on">
                            mdi-information-outline
                        </v-icon>
                    </template>
                    <p>
                        The leads close rate is calculated by dividing the total number of sales in the time period
                        selected by the total unique leads in the same time period.
                    </p>
                </v-tooltip>
                <apexchart
                    width="100%"
                    height="100%"
                    type="radialBar"
                    :series="closeRateMetrics.series"
                    :options="closeRateMetrics.chartOptions"
                />
            </div>
        </v-col>
        <v-col cols="12" md="4" class="d-flex align-center justify-center">
            <div class="lead-chart-wrapper unique">
                <v-tooltip top open-on-focus>
                    <template #activator="{ on, attrs }">
                        <v-icon color="grey" dark v-bind="attrs" small class="unique-icon" v-on="on">
                            mdi-information-outline
                        </v-icon>
                    </template>
                    <span
                        >Total leads represents user actions that generate leads (contact dealer, schedule test drive,
                        create a finance application) while unique leads is a count of users</span
                    >
                </v-tooltip>
                <apexchart
                    width="100%"
                    height="100%"
                    type="bar"
                    :series="totalMetrics.series"
                    :options="totalMetrics.chartOptions"
                />
            </div>
        </v-col>
    </reporting-metrics-row>
</template>

<script>
import ReportingMetricsRow from "@/modules/Reporting/components/ReportingMetricsRow";
import api from "@/util/api";
import { get } from "vuex-pathify";
import _ from "lodash";

export default {
    name: "ReportingLeadMetrics",
    components: { ReportingMetricsRow },
    props: {
        programId: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            stockTypeMetrics: {
                series: [],
                chartOptions: {
                    chart: {
                        type: "pie",
                        toolbar: {
                            show: false,
                        },
                    },
                    dataLabels: {
                        enabled: true,
                        formatter: (val, opts) => {
                            return `${_.round(val)}% (${opts.w.config.series[opts.seriesIndex]})`;
                        },
                        textAnchor: "middle",
                        distributed: false,
                        offsetX: 0,
                        offsetY: 0,
                        style: {
                            fontSize: "14px",
                            fontFamily: "Helvetica, Arial, sans-serif",
                            fontWeight: "bold",
                            colors: undefined,
                        },
                        background: {
                            enabled: true,
                            foreColor: "#fff",
                            padding: 4,
                            borderRadius: 2,
                            borderWidth: 1,
                            borderColor: "#fff",
                            opacity: 0.9,
                            dropShadow: {
                                enabled: false,
                                top: 1,
                                left: 1,
                                blur: 1,
                                color: "#000",
                                opacity: 0.45,
                            },
                        },
                        dropShadow: {
                            enabled: false,
                            top: 1,
                            left: 1,
                            blur: 1,
                            color: "#000",
                            opacity: 0.45,
                        },
                    },
                    labels: [],
                    legend: {
                        position: "bottom",
                    },
                    tooltip: {
                        x: {
                            show: false,
                        },
                    },
                    noData: {
                        text: "No data available",
                    },
                    title: {
                        text: "Stock Type",
                    },
                },
            },
            totalMetrics: {
                series: [],
                chartOptions: {
                    chart: {
                        type: "bar",
                        toolbar: {
                            show: false,
                        },
                    },
                    plotOptions: {
                        bar: {
                            borderRadius: 4,
                            columnWidth: "100%",
                        },
                    },
                    xaxis: {
                        categories: [],
                        labels: {
                            show: false,
                        },
                    },
                    tooltip: {
                        x: {
                            show: false,
                        },
                    },
                    labels: [],
                    responsive: [
                        {
                            breakpoint: 480,
                            options: {
                                legend: {
                                    position: "bottom",
                                },
                            },
                        },
                    ],
                    title: {
                        text: "Total & Unique",
                    },
                },
            },
            closeRateMetrics: {
                series: [],
                chartOptions: {
                    chart: {
                        type: "radialBar",
                        offsetY: -10,
                    },
                    plotOptions: {
                        radialBar: {
                            startAngle: -135,
                            endAngle: 135,
                            dataLabels: {
                                name: {
                                    show: true,
                                    fontSize: "12px",
                                    color: undefined,
                                    offsetY: 120,
                                },
                                value: {
                                    offsetY: 0,
                                    fontSize: "22px",
                                    color: undefined,
                                    formatter: (val) => {
                                        return val + "%";
                                    },
                                },
                            },
                        },
                    },
                    fill: {
                        type: "gradient",
                        gradient: {
                            shade: "dark",
                            shadeIntensity: 0.15,
                            inverseColors: false,
                            opacityFrom: 1,
                            opacityTo: 1,
                            stops: [0, 50, 65, 91],
                        },
                    },
                    stroke: {
                        dashArray: 4,
                    },
                    labels: ["Close Rate for Unique Leads"],
                    title: {
                        text: "Close Rate",
                    },
                },
            },
            prequalMetric: {
                series: [],
                chartOptions: {
                    chart: {
                        type: "pie",
                        toolbar: {
                            show: false,
                        },
                    },
                    dataLabels: {
                        enabled: true,
                        formatter: (val, opts) => {
                            return `${_.round(val)}% (${opts.w.config.series[opts.seriesIndex]})`;
                        },
                        textAnchor: "middle",
                        distributed: false,
                        offsetX: 0,
                        offsetY: 0,
                        style: {
                            fontSize: "14px",
                            fontFamily: "Helvetica, Arial, sans-serif",
                            fontWeight: "bold",
                            colors: undefined,
                        },
                        background: {
                            enabled: true,
                            foreColor: "#fff",
                            padding: 4,
                            borderRadius: 2,
                            borderWidth: 1,
                            borderColor: "#fff",
                            opacity: 0.9,
                            dropShadow: {
                                enabled: false,
                                top: 1,
                                left: 1,
                                blur: 1,
                                color: "#000",
                                opacity: 0.45,
                            },
                        },
                        dropShadow: {
                            enabled: false,
                            top: 1,
                            left: 1,
                            blur: 1,
                            color: "#000",
                            opacity: 0.45,
                        },
                    },
                    labels: [],
                    legend: {
                        position: "bottom",
                    },
                    tooltip: {
                        x: {
                            show: false,
                        },
                    },
                    noData: {
                        text: "No data available",
                    },
                    title: {
                        text: "Bureau vs. Self-Select",
                    },
                },
            },
            vehicleSaleCounts: {},
            closeRate: null,
            isLoading: false,
            headerTooltipContent: "Information related to leads generated by customer activity",
        };
    },
    computed: {
        searchResults: get("dealerSearch/searchLoader@data"),
        dealerIds() {
            return _.map(this.searchResults, "id");
        },
        dateRange: get("reportingStore/chartFilters@dateRange"),
        reportingRequest() {
            return {
                dealerIds: this.dealerIds,
                dateRange: this.dateRange,
            };
        },
    },
    watch: {
        searchResults() {
            this.fetchStockTypeMetrics();
            this.fetchLeadCounts();
            this.fetchVehicleSaleCounts();
            this.fetchCloseRate();
            this.getPrequalInformation();
        },
        dateRange() {
            this.fetchStockTypeMetrics();
            this.fetchLeadCounts();
            this.fetchVehicleSaleCounts();
            this.fetchCloseRate();
            this.getPrequalInformation();
        },
    },
    methods: {
        fetchStockTypeMetrics() {
            this.isLoading = true;

            api.post(`/reporting/${this.programId}/leads/stocktype`, this.reportingRequest)
                .then((response) => {
                    let results = _.get(response, "data.results");
                    results = _.orderBy(results, "count", "desc");

                    this.stockTypeMetrics.chartOptions.labels.splice(0);
                    this.stockTypeMetrics.series.splice(0);
                    _.map(results, (r) => {
                        const name = _.upperFirst(_.lowerCase(r.name));
                        this.stockTypeMetrics.chartOptions.labels.push(name);
                        this.stockTypeMetrics.series.push(r.count);
                    });
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
        fetchLeadCounts() {
            this.isLoading = true;
            api.post(`/reporting/${this.programId}/leads/counts`, this.reportingRequest)
                .then((response) => {
                    this.totalMetrics.chartOptions.xaxis.categories.splice(0);
                    this.totalMetrics.chartOptions.xaxis.categories.push("Total", "Unique");
                    this.totalMetrics.series.splice(0);
                    this.totalMetrics.series.push(
                        { name: "Total", data: [response.data.count] },
                        { name: "Unique", data: [response.data.uniqueCount] }
                    );
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
        fetchVehicleSaleCounts() {
            api.post(`/reporting/${this.programId}/vehicle_sales/counts`, this.dealerIds).then((response) => {
                this.vehicleSaleCounts = response.data;
            });
        },
        fetchCloseRate() {
            this.isLoading = true;
            api.post(`/reporting/${this.programId}/leads/close_rate`, this.reportingRequest)
                .then((response) => {
                    const rate = _.get(response, "data", 0).toFixed(1);
                    this.closeRateMetrics.series.splice(0);
                    this.closeRateMetrics.series.push(rate);
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
        getPrequalInformation() {
            // this.prequalMetric.chartOptions.labels.push("Bureau provided");
            // this.prequalMetric.series.push(80);
            //
            // this.prequalMetric.chartOptions.labels.push("Self Selected");
            // this.prequalMetric.series.push(120);

            this.isLoading = true;

            api.post(`/reporting/${this.programId}/leads/softpullPrequal`, this.reportingRequest)
                .then((response) => {
                    let results = _.get(response, "data.results");
                    results = _.orderBy(results, "count", "desc");

                    this.prequalMetric.chartOptions.labels.splice(0);
                    this.prequalMetric.series.splice(0);
                    _.map(results, (r) => {
                        const name = r.name === "false" ? "Prequal" : "Self Selected";
                        this.prequalMetric.chartOptions.labels.push(name);
                        this.prequalMetric.series.push(r.uniqueCount);
                    });
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
    },
};
</script>
<style lang="scss" scoped>
.lead-chart-wrapper {
    height: 265px;
    position: relative;

    .close-rate-icon {
        font-size: 12px;
        position: absolute;
        top: -6px;
        left: 100px;
        z-index: 1;
    }
}
.lead-pie-chart-wrapper {
    height: 265px;
    position: relative;

    .bureau-icon {
        position: absolute;
        top: 3px;
        left: 188px;
        z-index: 1;
    }
}
.unique {
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
}
.unique .unique-icon {
    position: absolute;
    top: 4px;
    left: 130px;
    z-index: 1;
}
</style>
