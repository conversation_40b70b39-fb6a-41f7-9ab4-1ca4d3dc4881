<template>
    <v-form class="mb-3">
        <v-card>
            <v-container fluid>
                <v-row class="flex justify-space-between">
                    <v-col cols="12" md="4">
                        <v-select
                            :value="programId"
                            label="Program"
                            placeholder="Select Program"
                            :items="programs"
                            item-text="name"
                            item-value="id"
                            outlined
                            dense
                            hide-details
                            @change="setSelectedProgram"
                        />
                    </v-col>
                </v-row>
            </v-container>
        </v-card>
    </v-form>
</template>

<script>
import api from "Util/api";

export default {
    name: "ReportingSearchForm",
    props: {
        programId: {
            type: String,
            required: false,
            default: null,
        },
    },
    data() {
        return {
            programs: [],
        };
    },
    mounted() {
        this.fetchPrograms();
    },
    methods: {
        fetchPrograms() {
            this.isLoading = true;
            api.get(`/reporting/programs`)
                .then((response) => {
                    this.programs = response.data;
                })
                .catch((error) => {
                    console.log(error);
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
        setSelectedProgram(programId) {
            this.$router.push({
                name: "ProgramPerformanceReport",
                params: { programId: programId },
                query: this.$route.query,
            });
        },
    },
};
</script>
