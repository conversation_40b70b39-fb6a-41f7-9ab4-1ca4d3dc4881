<template>
    <div>
        <v-list-item class="py-0">
            <v-list-item-content>
                <v-list-item-title>
                    <v-menu
                        ref="menu"
                        v-model="menu"
                        :close-on-content-click="false"
                        :return-value="date"
                        transition="scale-transition"
                        min-width="auto"
                        :class="{ 'date-range-warning': !isDateRangeValid }"
                    >
                        <template #activator="{ on, attrs }">
                            <v-text-field
                                :value="dateRangeTextDisplay"
                                label="Chart Date Range"
                                prepend-icon="mdi-calendar"
                                clearable
                                readonly
                                v-bind="attrs"
                                :full-width="false"
                                style="max-width: 280px"
                                v-on="on"
                                @click:clear="clearDate"
                            ></v-text-field>
                        </template>
                        <v-date-picker
                            v-model="date"
                            v-click-outside="onTextFieldBlur"
                            :allowed-dates="getAllowedDates"
                            range
                            no-title
                            scrollable
                            @input="checkAutoClose()"
                        >
                            <v-spacer></v-spacer>
                            <v-btn text color="primary" @click="cancelDateSelection"> Cancel </v-btn>
                            <v-btn text color="primary" @click="saveDate"> OK </v-btn>
                        </v-date-picker>
                    </v-menu>
                </v-list-item-title>
            </v-list-item-content>
        </v-list-item>
        <v-alert v-show="!isDateRangeValid && isOkEvent" dense outlined type="error">
            Error: Invalid date range. Please select valid start and end dates.
        </v-alert>
    </div>
</template>

<script>
import { sync } from "vuex-pathify";

export default {
    name: "DateRangeFilter",
    data() {
        return {
            menu: false,
            date: [],
            isDateRangeValid: true,
            isOkEvent: false,
        };
    },
    computed: {
        dateRangeTextDisplay() {
            if (this.dateFormatted.length == 2) {
                return `${this.dateFormatted[0]} - ${this.dateFormatted[1]}`;
            }
            if (this.dateFormatted.length == 1) {
                return `${this.dateFormatted[0]} - `;
            }
            return "";
        },
        dateFormatted() {
            let formattedDate = [];

            if (this.date != null && this.date.length > 0) {
                if (!_.isNil(this.date[0])) {
                    formattedDate[0] = this.formatDate(this.date[0]);
                }
                if (!_.isNil(this.date[1])) {
                    formattedDate[1] = this.formatDate(this.date[1]);
                }
            }
            return formattedDate;
        },
        dateRangeFilter: sync("reportingStore/chartFilters@dateRange"),
    },
    methods: {
        cancelDateSelection() {
            this.validateDate();
            this.menu = false;
        },
        saveDate() {
            this.validateDate();
            this.$refs.menu.save(this.date);
        },
        validateDate() {
            if (this.date.length === 1) {
                this.isDateRangeValid = false;
            }
        },
        onTextFieldBlur() {
            if (this.isDateRangeValid) {
                this.isOkEvent = true;
            }
            this.validateDate();
        },
        checkAutoClose() {
            if (this.date.length > 1 && !_.isNil(this.date[1])) {
                const startDate = new Date(this.date[0]);
                const endDate = new Date(this.date[1]);

                // Swap dates if start date is greater than end date
                if (startDate > endDate) {
                    [this.date[0], this.date[1]] = [this.date[1], this.date[0]];
                }

                // Check the date range validity after possible swapping
                this.isDateRangeValid = new Date(this.date[0]) <= new Date(this.date[1]);

                // Only close the popup if the date range is valid
                if (this.isDateRangeValid) {
                    this.menu = false;
                    this.dateRangeFilter = this.dateFormatted;
                }
            }
        },
        formatDate(date) {
            if (!date) return null;

            const [year, month, day] = date.split("-");
            return `${month}/${day}/${year}`;
        },
        clearDate() {
            this.date = ["", ""];
            this.isDateRangeValid = true;
            this.dateRangeFilter = this.dateFormatted;
        },
        getAllowedDates(date) {
            const currentDate = new Date();
            const selectedDate = new Date(date);

            return selectedDate <= currentDate;
        },
    },
};
</script>
<style>
/* Example CSS for the warning highlight */
.date-range-warning .v-picker {
    border-color: #f44336; /* Red color to indicate the warning */
    box-shadow: 0 0 0 2px #f44336; /* Optional: Add a red border highlight */
}
</style>
