<template>
    <v-container fluid class="reporting-metrics-row">
        <v-card class="fill-height mb-4 metrics-row-card" :loading="isLoading">
            <v-card-title
                ><span>{{ title }}</span
                ><span v-if="affectedByDateRange" class="px-1 text-caption">{{ dateRangeText }}</span>
                <v-tooltip v-if="showTooltip" bottom>
                    <template #activator="{ on, attrs }">
                        <v-icon color="grey" dark v-bind="attrs" small class="pl-1" v-on="on">
                            mdi-information-outline
                        </v-icon>
                    </template>
                    {{ tooltipContent }}
                </v-tooltip>
            </v-card-title>
            <v-card-subtitle class="subtitle-styles">{{ subtitle }}</v-card-subtitle>
            <v-card-text class="text--primary">
                <v-row>
                    <slot />
                </v-row>
            </v-card-text>
        </v-card>
    </v-container>
</template>

<script>
import _ from "lodash";
import { get } from "vuex-pathify";

export default {
    name: "ReportingMetricsRow",
    props: {
        title: {
            type: String,
            required: true,
        },
        subtitle: {
            type: String,
            default: "",
        },
        tooltipContent: {
            type: String,
            default: "",
        },
        isLoading: {
            type: Boolean,
            default: false,
            required: false,
        },
        affectedByDateRange: {
            type: Boolean,
            default: false,
            required: false,
        },
    },
    computed: {
        showTooltip() {
            return !_.isEmpty(this.tooltipContent) && !_.isNil(this.tooltipContent);
        },
        dateRange: get("reportingStore/chartFilters@dateRange"),
        dateRangeText() {
            if (
                !this.dateRange[0] ||
                this.dateRange[0].length === 0 ||
                !this.dateRange[1] ||
                this.dateRange[1].length === 0
            ) {
                return "";
            }

            return `(${this.dateRange[0]} - ${this.dateRange[1]})`;
        },
    },
};
</script>
<style lang="scss" scoped>
.subtitle-styles {
    font-size: 12px;
}
</style>
