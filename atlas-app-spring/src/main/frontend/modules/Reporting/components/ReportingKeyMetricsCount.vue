<template>
    <reporting-metrics-row title="Key Metrics" :is-loading="isLoading" :tooltip-content="tooltipContent">
        <v-col>
            <v-data-table :headers="headers" :items="keyMetrics" hide-default-footer>
                <template #item.metric="{ item }">
                    {{ item.metric }}
                    <v-tooltip bottom max-width="500">
                        <template #activator="{ on, attrs }">
                            <v-icon color="grey" dark v-bind="attrs" x-small v-on="on">
                                mdi-information-outline
                            </v-icon>
                        </template>
                        <!--eslint-disable-next-line-->
                        <span v-html="getToolTip(item.metric)" />
                    </v-tooltip>
                </template>
            </v-data-table>
        </v-col>
    </reporting-metrics-row>
</template>

<script>
import api from "@/util/api";
import { get } from "vuex-pathify";
import _ from "lodash";
import ReportingMetricsRow from "../components/ReportingMetricsRow";

export default {
    name: "ReportingKeyMetricsCount",
    components: { ReportingMetricsRow },
    props: {
        programId: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            headers: [
                {
                    text: "Metric",
                    align: "start",
                    sortable: false,
                    value: "metric",
                },
                {
                    text: "Today",
                    align: "start",
                    value: "today",
                    sortable: false,
                },
                {
                    text: "Last 30 Days",
                    value: "last30Days",
                    sortable: false,
                },
                {
                    text: "Last 60 Days",
                    value: "last60Days",
                    sortable: false,
                },
                {
                    text: "Last 90 Days",
                    value: "last90Days",
                    sortable: false,
                },
            ],
            keyMetrics: [],
            isLoading: false,
            tooltipContent: "Sales in this chart are being tracked on the purchase date of the vehicle.",
        };
    },
    computed: {
        searchResults: get("dealerSearch/searchLoader@data"),
        dealerIds() {
            return _.map(this.searchResults, "id");
        },
    },
    watch: {
        searchResults() {
            this.fetchKeyMetrics();
        },
    },
    methods: {
        fetchKeyMetrics() {
            this.isLoading = true;
            api.post(`/reporting/${this.programId}/keyMetrics`, this.dealerIds)
                .then((response) => {
                    this.keyMetrics.splice(0);
                    const keyMetrics = _.get(response, "data", []);
                    _.map(keyMetrics, (metric) => {
                        let row = {
                            metric: metric.metric,
                            today: metric.todayCount,
                            last30Days: metric.last30DayCount,
                            last60Days: metric.last60DayCount,
                            last90Days: metric.last90DayCount,
                        };
                        this.keyMetrics.push(row);
                    });
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
        getToolTip(metric) {
            switch (metric.toLowerCase()) {
                case "clicks":
                    return `<strong>Clicks</strong> are defined as the <em>unique pageviews</em> of the application's
                        landing page.`;
                case "accounts created":
                    return `<strong>Accounts Created</strong> are defined as the <em>total unique users</em> created in the CarSaver system.`;
                case "account sign-ins":
                    return `<strong>Account Sign-Ins</strong> are defined as one sign in per unique user.`;
                case "leads":
                    return `<strong>Leads</strong> are unique, counting only one per user.`;
                case "sales":
                    return `Sales figures represented here are derived by/from completing a sales match between rdrs and lead data submitted through the selected program. Sales can be matched based on the individual and or household. Sales are recorded based on date vehicle sold.`;
                default:
                    return "";
            }
        },
    },
};
</script>
