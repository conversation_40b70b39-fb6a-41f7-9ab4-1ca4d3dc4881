<template>
    <div class="lender-chart-wrapper">
        <v-tooltip top open-on-focus>
            <template #activator="{ on, attrs }">
                <v-icon color="grey" dark v-bind="attrs" small v-on="on"> mdi-information-outline </v-icon>
            </template>
            <span>
                NMAC penetration rate is being provided as directional in nature due to the frequency in which we
                receive captive flags; monthly batch files.
            </span>
        </v-tooltip>
        <apexchart
            ref="nmacAverage"
            width="100%"
            height="100%"
            type="line"
            :series="nmacOverTime.series"
            :options="nmacOverTime.chartOptions"
        />
    </div>
</template>
<script>
export default {
    name: "NmacOvertimeChart",
    props: {
        nmacHistogramList: {
            type: Array,
            required: false,
            default: () => [],
        },
    },
    data() {
        return {
            nmacOverTime: {
                series: [
                    {
                        name: "Percentage of Apps",
                        data: [],
                    },
                ],
                chartOptions: {
                    chart: {
                        type: "area",
                        stacked: false,
                        height: 350,
                        zoom: {
                            type: "x",
                            enabled: true,
                            autoScaleYaxis: true,
                        },
                        toolbar: {
                            autoSelected: "zoom",
                            tools: {
                                download: false,
                                selection: true,
                                zoom: true,
                                zoomin: true,
                                zoomout: true,
                                pan: true,
                                reset: false,
                            },
                        },
                        events: {
                            beforeZoom: (chartContext, { xaxis }) => {
                                let series = chartContext.w.globals.seriesX;
                                let seriesRange = series[0][series[0].length - 1] - series[0][0];
                                let zoomRange = xaxis.max - xaxis.min;
                                if (zoomRange > seriesRange)
                                    return {
                                        xaxis: {
                                            min: series[0][0],
                                            max: series[0][series[0].length - 1],
                                        },
                                    };
                                else {
                                    return {
                                        xaxis: {
                                            min: xaxis.min,
                                            max: xaxis.max,
                                        },
                                    };
                                }
                            },
                        },
                    },
                    dataLabels: {
                        enabled: false,
                    },
                    stroke: {
                        curve: "straight",
                    },
                    title: {
                        text: "Percentage of NMAC Finance Apps Over Time",
                        align: "left",
                    },
                    yaxis: {
                        labels: {
                            formatter: function (val) {
                                return val.toFixed(0);
                            },
                        },
                        title: {
                            text: "Percentage of Apps",
                        },
                    },
                    xaxis: {
                        type: "datetime",
                    },
                    tooltip: {
                        shared: false,
                        y: {
                            formatter: function (val) {
                                return val.toFixed(2);
                            },
                        },
                    },
                },
            },
        };
    },
    watch: {
        nmacHistogramList(newValue, oldValue) {
            if (newValue !== oldValue) {
                this.setNmacOvertime();
            }
        },
    },
    mounted() {
        this.setNmacOvertime();
    },
    methods: {
        setNmacOvertime() {
            const nmacHistogram = this.nmacHistogramList;
            this.nmacOverTime.series[0].data.splice(0);
            let seriesValues = this.nmacOverTime.series[0].data;

            nmacHistogram.forEach((element) => {
                let formattedDate = new Date(element.date);
                seriesValues.push({ x: formattedDate, y: element.percentNmacApplications });
            });
            this.$refs.nmacAverage.updateSeries(
                [
                    {
                        data: seriesValues,
                    },
                ],
                false,
                true
            );
        },
    },
};
</script>
