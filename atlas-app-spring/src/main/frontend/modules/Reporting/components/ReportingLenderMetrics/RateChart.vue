<template>
    <div class="lender-chart-wrapper">
        <apexchart
            width="100%"
            height="100%"
            type="bar"
            :series="rateBuckets.series"
            :options="rateBuckets.chartOptions"
        />
    </div>
</template>
<script>
import _ from "lodash";

export default {
    name: "RateChart",
    props: {
        lenderList: {
            type: Array,
            required: false,
            default: () => [],
        },
    },
    data() {
        return {
            rateBuckets: {
                series: [],
                chartOptions: {
                    chart: {
                        type: "bar",
                        toolbar: {
                            show: false,
                        },
                    },
                    plotOptions: {
                        bar: {
                            columnWidth: "100%",
                            dataLabels: {
                                enabled: true,
                            },
                        },
                    },
                    stroke: {
                        colors: ["transparent"],
                        width: 3,
                    },
                    labels: [],
                    legend: {
                        show: false,
                    },
                    noData: {
                        text: "No data available",
                    },
                    grid: {
                        show: true,
                        row: {
                            colors: ["#fff", "#f2f2f2"],
                        },
                    },
                    title: {
                        text: "Number of Finance Applications grouped by APR",
                    },
                    tooltip: {
                        x: {
                            show: false,
                        },
                    },
                    xaxis: {
                        categories: [],
                        labels: {
                            style: {
                                fontSize: "12",
                            },
                        },
                        title: {
                            text: "Annual Percentage Rates",
                        },
                    },
                    yaxis: {
                        title: {
                            text: "Number of Apps",
                        },
                    },
                },
            },
            listOfAllRates: [],
        };
    },
    created() {
        this.setRates();
    },
    methods: {
        setRates() {
            const lenders = this.lenderList;

            this.rateBuckets.series.splice(0);
            this.rateBuckets.chartOptions.xaxis.categories.splice(0);

            this.setListOfAllRates(lenders);

            this.setRateChartCategories();

            this.setRateBuckets();
        },
        setListOfAllRates(lenders) {
            _.forEach(lenders, (lender) => {
                _.forEach(lender.rateList, (rate) => {
                    let parsedRateFloat = parseFloat(rate);
                    let roundedRate = _.round(parsedRateFloat, 2);

                    if (_.isFinite(roundedRate)) {
                        this.listOfAllRates.push(roundedRate);
                    }
                });
            });
        },
        setRateChartCategories() {
            let listOfUniqueRates = _.uniq(this.listOfAllRates);
            let sortedListOfUniqueRates = _.sortBy(listOfUniqueRates);
            let groupedListOfUniqueRates = _.groupBy(sortedListOfUniqueRates, Math.floor);

            for (const [key, value] of Object.entries(groupedListOfUniqueRates)) {
                let chartCategoryLabel;

                if (value.length > 1) {
                    chartCategoryLabel = `${value[0]}% - ${value[value.length - 1]}%`;
                } else {
                    chartCategoryLabel = `${value[0]}%`;
                }

                this.rateBuckets.chartOptions.xaxis.categories.push(chartCategoryLabel);
            }
        },
        setRateBuckets() {
            let sortedListOfAllRates = _.sortBy(this.listOfAllRates);
            let groupedRates = _.groupBy(sortedListOfAllRates, Math.floor);

            _.forEach(groupedRates, (groupedRate) => {
                let bucketName;

                if (groupedRate.length > 1) {
                    bucketName = `${groupedRate[0]}% - ${groupedRate[groupedRate.length - 1]}% APR`;
                } else {
                    bucketName = `${groupedRate[0]}% APR`;
                }

                this.rateBuckets.series.push({
                    name: bucketName,
                    data: [groupedRate.length],
                });
            });
        },
    },
};
</script>
