<template>
    <div class="lender-chart-wrapper">
        <apexchart
            width="100%"
            height="100%"
            type="bar"
            :options="lenderPieMetrics.chartOptions"
            :series="lenderPieMetrics.series"
        />
    </div>
</template>
<script>
import _ from "lodash";

export default {
    name: "LenderChart",
    props: {
        lenderList: {
            type: Array,
            required: false,
            default: () => [],
        },
    },
    data() {
        return {
            lenderPieMetrics: {
                series: [],
                chartOptions: {
                    dataLabels: {
                        dropShadow: {
                            enabled: true,
                        },
                    },
                    chart: {
                        type: "bar",
                        toolbar: {
                            show: false,
                        },
                    },
                    plotOptions: {
                        bar: {
                            barHeight: "100%",
                            distributed: true,
                        },
                    },
                    stroke: {
                        colors: ["transparent"],
                        width: 3,
                    },
                    labels: [],
                    legend: {
                        show: false,
                    },
                    noData: {
                        text: "No data available",
                    },
                    grid: {
                        show: true,
                        row: {
                            colors: ["#fff", "#f2f2f2"],
                        },
                    },
                    title: {
                        text: "Apps By Lender",
                    },
                    xaxis: {
                        categories: [],
                        labels: {
                            trim: true,
                            style: {
                                fontSize: "12",
                            },
                        },
                    },
                    yaxis: {
                        title: {
                            text: "Quantity of Apps By Lender",
                        },
                    },
                },
            },
        };
    },
    watch: {
        lenderList(newValue, oldValue) {
            if (newValue !== oldValue) {
                this.setLenderPieMetrics();
            }
        },
    },
    created() {
        this.setLenderPieMetrics();
    },
    methods: {
        setLenderPieMetrics() {
            this.lenderPieMetrics.series = [
                {
                    name: "Quantity of Apps By Lenders",
                    data: [],
                },
            ];
            this.lenderPieMetrics.chartOptions = {
                ...this.lenderPieMetrics.chartOptions,
                xaxis: {
                    ...this.lenderPieMetrics.chartOptions.xaxis,
                    categories: [],
                },
            };

            _.map(this.lenderList, (lender) => {
                this.lenderPieMetrics.series[0].data.push({ x: lender.lenderName, y: lender.applications });
                this.lenderPieMetrics.chartOptions.xaxis.categories.push(lender.lenderName);
            });
        },
    },
};
</script>
