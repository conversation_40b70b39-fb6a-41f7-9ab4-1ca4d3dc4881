<template>
    <div class="lender-chart-wrapper">
        <apexchart
            width="100%"
            height="100%"
            type="bar"
            :series="termBuckets.series"
            :options="termBuckets.chartOptions"
        />
    </div>
</template>
<script>
import _ from "lodash";

export default {
    name: "TermChart",
    props: {
        lenderList: {
            type: Array,
            required: false,
            default: () => [],
        },
    },
    data() {
        return {
            termBuckets: {
                series: [],
                chartOptions: {
                    chart: {
                        type: "bar",
                        toolbar: {
                            show: false,
                        },
                    },
                    plotOptions: {
                        bar: {
                            columnWidth: "100%",
                            dataLabels: {
                                enabled: true,
                            },
                        },
                    },
                    stroke: {
                        colors: ["transparent"],
                        width: 3,
                    },
                    labels: [],
                    legend: {
                        show: false,
                    },
                    noData: {
                        text: "No data available",
                    },
                    grid: {
                        show: true,
                        row: {
                            colors: ["#fff", "#f2f2f2"],
                        },
                    },
                    title: {
                        text: "Number of Finance Applications grouped by 12 month term increments",
                    },
                    tooltip: {
                        x: {
                            show: false,
                        },
                    },
                    xaxis: {
                        categories: [],
                        labels: {
                            style: {
                                fontSize: "12",
                            },
                        },
                        title: {
                            text: "Term Months",
                        },
                    },
                    yaxis: {
                        title: {
                            text: "Number of Apps",
                        },
                    },
                },
            },
            listOfAllTerms: [],
        };
    },
    created() {
        this.setTerms();
    },
    methods: {
        setTerms() {
            const lenders = this.lenderList;

            this.termBuckets.series.splice(0);
            this.termBuckets.chartOptions.xaxis.categories.splice(0);

            this.setListOfAllTerms(lenders);

            this.setTermChartCategories();

            this.setTermBuckets();
        },
        setListOfAllTerms(lenders) {
            _.forEach(lenders, (lender) => {
                _.forEach(lender.termList, (rate) => {
                    let parsedTermInt = parseInt(rate);

                    if (_.isFinite(parsedTermInt)) {
                        this.listOfAllTerms.push(parsedTermInt);
                    }
                });
            });
        },
        setTermChartCategories() {
            let listOfUniqueTerms = _.uniq(this.listOfAllTerms);
            let maxValueOfArray = _.max(listOfUniqueTerms);
            let numberOfGroups = _.round(maxValueOfArray / 12);
            let numberOfGroupsToArray = [];
            let monthArray = [];

            for (let i = 1; i <= numberOfGroups; i++) {
                numberOfGroupsToArray.push(i);
            }
            _.forEach(numberOfGroupsToArray, (value) => {
                monthArray.push(value * 12);
            });

            _.forEach(monthArray, (month) => {
                this.termBuckets.chartOptions.xaxis.categories.push(`${month} months`);
            });
        },
        setTermBuckets() {
            let sortedListOfAllTerms = _.sortBy(this.listOfAllTerms);
            let maxValueOfArray = _.max(sortedListOfAllTerms);
            let numberOfGroups = _.round(maxValueOfArray / 12);
            let numberOfGroupsToArray = [];
            let monthArray = [];

            for (let i = 1; i <= numberOfGroups; i++) {
                numberOfGroupsToArray.push(i);
            }
            _.forEach(numberOfGroupsToArray, (value) => {
                let termObject = {
                    id: value * 12,
                    data: [],
                };
                monthArray.push(termObject);
            });

            _.forEach(sortedListOfAllTerms, (term) => {
                _.forEach(monthArray, (month) => {
                    if (term <= month.id) {
                        month.data.push(term);
                    }
                });
            });

            _.forEach(monthArray, (month) => {
                let bucketName = `${month.id} Months`;

                this.termBuckets.series.push({
                    name: bucketName,
                    data: [month.data.length],
                });
            });
        },
    },
};
</script>
