<template>
    <reporting-metrics-row
        title="Model View"
        tooltip-content="This chart combines lead, deal, and sales data by model.  Sort on any value by clicking on the column header.
                            Sales in this chart are being tracked on the purchase date of the vehicle."
    >
        <v-col>
            <v-data-table
                :headers="headers"
                :items="models"
                :items-per-page="15"
                :loading="loading"
                sort-by="leadsWithVOI"
                sort-desc
            >
                <template v-for="header in headers" #[`header.${header.value}`]>
                    {{ header.text }}
                    <info-tooltip v-if="header.tooltip" :key="header.value" size="16">
                        {{ header.tooltip }}
                    </info-tooltip>
                </template>

                <template #[`item.avgSalePrice`]="{ item }">
                    {{ item.avgSalePrice | numeral("$0,0") }}
                </template>
                <template #[`item.avgTradeValue`]="{ item }">
                    {{ item.avgTradeValue | numeral("$0,0") }}
                </template>
                <template #[`item.averageRequestedAmountFinanced`]="{ item }">
                    {{ item.averageRequestedAmountFinanced | numeral("$0,0") }}
                </template>
                <template #[`item.shareOfSales`]="{ item }"> {{ item.shareOfSales }}% </template>
                <template #[`item.closeRate`]="{ item }"> {{ item.closeRate }}% </template>
            </v-data-table>
        </v-col>
    </reporting-metrics-row>
</template>

<script>
import api from "@/util/api";
import { get } from "vuex-pathify";
import _ from "lodash";
import ReportingMetricsRow from "../components/ReportingMetricsRow";
import InfoTooltip from "Components/InfoTooltip";

export default {
    name: "ModelViewMetrics",
    components: { InfoTooltip, ReportingMetricsRow },
    props: {
        programId: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            loading: false,
            headers: [
                {
                    text: "Models",
                    value: "modelName",
                    sortable: true,
                },

                {
                    text: "Leads With This VOI",
                    value: "leadsWithVOI",
                    sortable: true,
                    tooltip:
                        "This is the number of times a user entered Buy@Home with this model from your dealership as a vehicle of interest",
                },
                {
                    text: "Deal Avg Sale Price",
                    value: "avgSalePrice",
                    sortable: true,
                    tooltip: "The average sales price of all deals with this model",
                },
                {
                    text: "Deal Avg Trade Value",
                    value: "avgTradeValue",
                    sortable: true,
                    tooltip: "The average trade value of all trade-ins included on deals with this model",
                },
                {
                    text: "Deal Avg Credit Score",
                    value: "avgCreditScore",
                    sortable: true,
                    tooltip: "The average credit score of customers with this model in their deals",
                },
                {
                    text: "Buy@Home Sales",
                    value: "buyAtHomeSales",
                    sortable: true,
                    tooltip:
                        "The number of units sold of this model on Buy@Home from your dealership in the time period selected",
                },
                {
                    text: "Share of Sales",
                    value: "shareOfSales",
                    sortable: true,
                    tooltip: "The percentage of total Buy@Home sales this model represents",
                },
                {
                    text: "Close Rate",
                    value: "closeRate",
                    sortable: true,
                    tooltip:
                        "The percentage of leads with this model as the vehicle of interest that turned into sales",
                },
                // {
                //     text: "% NMAC",
                //     value: "NMACPercentage",
                //     sortable: true,
                //     tooltip: "",
                // },
                // {
                //     text: "Avg Gross Profit (front end)",
                //     value: "avgGrossProfitFE",
                //     sortable: true,
                //     tooltip: "",
                // },
                // {
                //     text: "Avg Gross Profit (back end)",
                //     value: "avgGrossProfitBE",
                //     sortable: true,
                //     tooltip: "",
                // },
                // {
                //     text: "Avg Rate",
                //     value: "avgRate",
                //     sortable: true,
                //     tooltip: "",
                // },
                // {
                //     text: "Avg Term",
                //     value: "avgTerm",
                //     sortable: true,
                //     tooltip: "",
                // },
            ],
            models: [],
        };
    },
    computed: {
        searchResults: get("dealerSearch/searchLoader@data"),
        dateRange: get("reportingStore/chartFilters@dateRange"),
        dealerIds() {
            return _.map(this.searchResults, "id");
        },
        reportingRequest() {
            return {
                dealerIds: this.dealerIds,
                dateRange: this.dateRange,
            };
        },
    },
    watch: {
        searchResults() {
            this.getModelMetrics();
        },
        dateRange() {
            this.getModelMetrics();
        },
    },
    methods: {
        getModelMetrics() {
            this.loading = true;

            api.post(`/reporting/model_view/${this.programId}/model_metrics`, this.reportingRequest)
                .then((response) => {
                    this.models = response.data;
                })
                .catch((err) => {
                    console.warn("API failed to load Model View Metrics: ", err);
                })
                .finally(() => {
                    this.loading = false;
                });
        },
    },
};
</script>
