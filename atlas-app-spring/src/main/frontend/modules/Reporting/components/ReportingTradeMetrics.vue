<template>
    <reporting-metrics-row
        title="Trades Evaluated"
        :tooltip-content="tooltipContent"
        :is-loading="isLoading"
        :affected-by-date-range="true"
    >
        <v-col cols="12" sm="4" class="d-flex align-center">
            <div class="trades-block d-flex flex-column align-center text-center">
                <div class="trades-value">{{ tradeReport.tradeCount }}</div>
                <div class="trades-label">Number of Trades</div>
            </div>
        </v-col>
        <v-col cols="12" sm="4" class="d-flex align-center">
            <div class="trades-block d-flex flex-column align-center text-center">
                <div class="trades-value">{{ tradeReport.tradePercentage }}%</div>
                <div class="trades-label">Percentage of Deals with Trades</div>
            </div>
        </v-col>
        <v-col cols="12" sm="4" class="d-flex align-center">
            <div class="trades-block d-flex flex-column align-center text-center">
                <div class="trades-value">{{ tradeReport.averageTradeValue | numeral("$0,0") }}</div>
                <div class="trades-label">Average Trade-In Value</div>
            </div>
        </v-col>
        <v-col cols="12" class="pie-chart-column mt-3">
            <div class="d-flex align-center justify-center">
                <div class="pie-chart-wrapper">
                    <apexchart
                        width="100%"
                        height="275"
                        type="bar"
                        :series="tradeMakes.series"
                        :options="tradeMakes.chartOptions"
                    />
                </div>
            </div>
        </v-col>
    </reporting-metrics-row>
</template>

<script>
import ReportingMetricsRow from "@/modules/Reporting/components/ReportingMetricsRow";
import api from "@/util/api";
import { get } from "vuex-pathify";
import _ from "lodash";

export default {
    name: "ReportingTradeMetrics",
    components: { ReportingMetricsRow },
    props: {
        programId: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            tooltipContent: "Includes trades for all deals created, not just sales",
            tradeMakes: {
                series: [],
                chartOptions: {
                    dataLabels: {
                        enabled: true,
                        dropShadow: {
                            enabled: true,
                        },
                    },
                    enableTooltip: true,
                    chart: {
                        type: "bar",
                        toolbar: {
                            show: false,
                        },
                    },
                    plotOptions: {
                        bar: {
                            distributed: true,
                            columnWidth: "100%",
                            dataLabels: {
                                enabled: true,
                            },
                        },
                    },
                    stroke: {
                        colors: ["transparent"],
                        width: 3,
                    },
                    labels: [],
                    legend: {
                        show: false,
                    },
                    noData: {
                        text: "No data available",
                    },
                    grid: {
                        show: true,
                        row: {
                            colors: ["#fff", "#f2f2f2"],
                        },
                    },
                    title: {
                        text: "Trade-In Vehicle By Makes",
                    },
                    tooltip: {
                        x: {
                            show: true,
                        },
                    },
                    xaxis: {
                        categories: [],
                        labels: {
                            trim: true,
                            style: {
                                fontSize: "12",
                            },
                        },
                    },
                    yaxis: {
                        title: {
                            text: "Number of Trade-In Vehicles",
                        },
                    },
                },
            },
            isLoading: false,
            tradeReport: {},
        };
    },
    computed: {
        searchResults: get("dealerSearch/searchLoader@data"),
        dealerIds() {
            return _.map(this.searchResults, "id");
        },
        dateRange: get("reportingStore/chartFilters@dateRange"),
        reportingRequest() {
            return {
                dealerIds: this.dealerIds,
                dateRange: this.dateRange,
            };
        },
    },
    watch: {
        searchResults() {
            this.fetchTradeReport();
        },
        dateRange() {
            this.fetchTradeReport();
        },
    },
    methods: {
        fetchTradeReport() {
            this.isLoading = true;

            api.post(`/reporting/${this.programId}/trades`, this.reportingRequest)
                .then((response) => {
                    let data = _.get(response, "data");
                    this.tradeReport = data;

                    this.tradeMakes.series = [
                        {
                            name: "Trade-In vehicles",
                            data: [],
                        },
                    ];
                    this.tradeMakes.chartOptions = {
                        xaxis: {
                            ...this.tradeMakes.chartOptions.xaxis,
                            categories: [],
                        },
                    };

                    _.map(data.tradeMakes, (r) => {
                        this.tradeMakes.series[0].data.push({ x: r.make, y: r.count });
                        this.tradeMakes.chartOptions.xaxis.categories.push(r.make);
                    });
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
    },
};
</script>
<style lang="scss">
.pie-chart-column {
    border-top: 1px solid #eeeeee;
}
.trades-block {
    width: 100%;

    .trades-value {
        font-size: px2rem(24);
        font-weight: bold;
    }
    .trades-label {
        font-size: px2rem(14);
        color: #808080;
    }
}
.pie-chart-wrapper {
    width: 100%;
}
</style>
