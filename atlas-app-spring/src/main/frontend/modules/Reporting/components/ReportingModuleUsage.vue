<template>
    <reporting-metrics-row
        title="Module Usage"
        :is-loading="isLoading"
        :affected-by-date-range="true"
        :tooltip-content="headerTooltipContent"
    >
        <v-col class="module-usage">
            <v-tooltip v-if="dataReceived" bottom max-width="500">
                <template #activator="{ on, attrs }">
                    <v-icon color="grey" dark v-bind="attrs" x-small class="credit-softpull" v-on="on">
                        mdi-information-outline
                    </v-icon>
                </template>
                <span> Users submitting a soft pull/prequal to the credit bureau </span>
            </v-tooltip>
            <v-tooltip v-if="dataReceived" bottom max-width="500">
                <template #activator="{ on, attrs }">
                    <v-icon color="grey" dark v-bind="attrs" x-small class="trade-in" v-on="on">
                        mdi-information-outline
                    </v-icon>
                </template>
                <span> Users entering a trade-in </span>
            </v-tooltip>
            <v-tooltip v-if="dataReceived" bottom max-width="500">
                <template #activator="{ on, attrs }">
                    <v-icon color="grey" dark v-bind="attrs" x-small class="deal-structure" v-on="on">
                        mdi-information-outline
                    </v-icon>
                </template>
                <span> Users progressing to the My Deal page to view a deal structure </span>
            </v-tooltip>
            <v-tooltip v-if="dataReceived" bottom max-width="500">
                <template #activator="{ on, attrs }">
                    <v-icon color="grey" dark v-bind="attrs" x-small class="credit-hardpull" v-on="on">
                        mdi-information-outline
                    </v-icon>
                </template>
                <span> Users submitting a finance application </span>
            </v-tooltip>
            <apexchart height="300" width="100%" type="bar" :options="options" :series="series" />
        </v-col>
    </reporting-metrics-row>
</template>

<script>
import ReportingMetricsRow from "@/modules/Reporting/components/ReportingMetricsRow";
import { get } from "vuex-pathify";
import _ from "lodash";
import api from "@/util/api";

export default {
    name: "ReportingModuleUsage",
    components: { ReportingMetricsRow },
    props: {
        programId: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            series: [],
            options: {
                chart: {
                    type: "bar",
                    toolbar: {
                        show: false,
                    },
                },
                plotOptions: {
                    bar: {
                        columnWidth: "60%",
                        borderRadius: 4,
                        distributed: true,
                        horizontal: true,
                        barHeight: "70%",
                    },
                },
                legend: {
                    show: false,
                },
                dataLabels: {
                    enabled: true,
                    dropShadow: {
                        enabled: true,
                    },
                },
                noData: {
                    text: "No data available",
                },
                xaxis: {
                    categories: [],
                },
            },
            isLoading: false,
            headerTooltipContent: "Below chart shows the number of users engaging with each section of the platform",
        };
    },
    computed: {
        searchResults: get("dealerSearch/searchLoader@data"),
        dateRange: get("reportingStore/chartFilters@dateRange"),
        dealerIds() {
            return _.map(this.searchResults, "id");
        },
        reportingRequest() {
            return {
                dealerIds: this.dealerIds,
                dateRange: this.dateRange,
            };
        },
        dataReceived() {
            return this.series[0].data.length;
        },
    },
    watch: {
        searchResults() {
            this.fetchModuleUsage();
        },
        dateRange() {
            this.fetchModuleUsage();
        },
    },

    methods: {
        fetchModuleUsage() {
            this.isLoading = true;
            api.post(`/reporting/${this.programId}/module-usage`, this.reportingRequest)
                .then((response) => {
                    let data = _.get(response, "data");
                    // if (data) {
                    //     this.dataReceived === true;
                    // }
                    // this.moduleUsage = data;
                    // this.moduleUsageMetrics.series.splice(0);
                    this.series = [
                        {
                            name: "Module Usage",
                            data: [],
                        },
                    ];
                    this.options = {
                        ...this.options,
                        xaxis: {
                            ...this.options.xaxis,
                            categories: [],
                        },
                    };

                    _.map(data.moduleReports, (entry) => {
                        if (entry.moduleName.toLowerCase() !== "account log in") {
                            this.series[0].data.push({ x: entry.moduleName, y: entry.count });
                            this.options.xaxis.categories.push(entry.moduleName);
                            // this.moduleUsageMetrics.chartOptions.xaxis.categories.push(entry.moduleName);
                            // this.moduleUsageMetrics.series.push({ name: entry.moduleName, data: [entry.count] });
                        }
                    });
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
    },
};
</script>

<style lang="scss" scoped>
.module-usage {
    .credit-hardpull,
    .credit-softpull,
    .trade-in,
    .deal-structure {
        position: absolute;
        z-index: 1;
        left: 108px;
    }

    .credit-softpull {
        top: 116px;
    }

    .trade-in {
        top: 174px;
    }

    .deal-structure {
        top: 232px;
    }

    .credit-hardpull {
        top: 290px;
    }
}
</style>
