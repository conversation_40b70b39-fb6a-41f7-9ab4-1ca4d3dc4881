import Vue from "vue";
import VueRouter from "vue-router";
import { layout, route, routerOptions, configureRouter } from "Util/routerHelper";

Vue.use(VueRouter);

const PATH_PREFIX = "/reporting";

const routes = [
    layout("Reporting", [
        route("Reporting", "ReportHome", null, PATH_PREFIX),
        route("Reporting", "ProgramPerformanceReport", null, PATH_PREFIX + "/:programId"),
    ]),
];

const router = new VueRouter({
    mode: "history",
    routes,
    ...routerOptions,
});

configureRouter(router);

export default router;
