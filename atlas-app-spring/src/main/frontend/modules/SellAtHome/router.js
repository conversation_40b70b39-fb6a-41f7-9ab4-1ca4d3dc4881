import Vue from "vue";
import VueRouter from "vue-router";
import { configureRouter, layout, route, routerOptions } from "Util/routerHelper";

Vue.use(VueRouter);

const PATH_PREFIX = "/sell-at-home";

const routes = [layout("Default", [route("SellAtHome", "SellAtHomeView", null, PATH_PREFIX)])];

const router = new VueRouter({
    mode: "history",
    routes,
    ...routerOptions,
});

configureRouter(router);

export default router;
