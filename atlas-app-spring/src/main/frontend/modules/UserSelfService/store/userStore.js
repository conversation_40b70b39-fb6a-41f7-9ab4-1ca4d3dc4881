import { make } from "vuex-pathify";
import loader from "@/util/loader";
import api from "@/util/api.js";
import _ from "lodash";

const initialState = {
    userModel: {
        data: null,
        loader: loader.defaultState(),
    },
    passwordDialog: false,
    editingUser: false,
    availableJobTitlesModel: {
        data: [],
        loader: loader.defaultState(),
    },
};

const mutations = {
    ...make.mutations(initialState),
    SET_USER_MODEL(state, payload) {
        _.set(state, "userModel", payload);
    },
    SET_CHANGE_PASSWORD_DIALOG(state, payload) {
        _.set(state, "passwordDialog", payload);
    },
    SET_EDITING_USER(state, payload) {
        _.set(state, "editingUser", payload);
    },
    SET_JOB_TITLES(state, payload) {
        _.set(state, "availableJobTitlesModel", payload);
    },
};

const actions = {
    ...make.actions(initialState),
    fetchUser({ commit, state }) {
        commit("SET_USER_MODEL", {
            data: null,
            loader: loader.started(),
        });
        api.get(`/account/profile/logged-in-user`)
            .then((response) => {
                commit("SET_USER_MODEL", {
                    data: _.get(response, "data", null),
                    loader: loader.successful(),
                });
            })
            .catch((error) => {
                console.log(error);
                commit("SET_USER_MODEL", {
                    data: null,
                    loader: loader.error(error),
                });
            });
    },
    setUserModel({ commit }, { response }) {
        commit("SET_USER_MODEL", {
            data: _.get(response, "data", null),
            loader: loader.successful(),
        });
    },
    fetchFormData({ commit, state }) {
        commit("SET_JOB_TITLES", {
            data: null,
            loader: loader.started(),
        });
        api.get(`/account/profile/form-data`)
            .then((response) => {
                commit("SET_JOB_TITLES", {
                    data: _.get(response, "data.jobTitles", null),
                    loader: loader.successful(),
                });
            })
            .catch((error) => {
                console.error(error);
                commit("SET_JOB_TITLES", {
                    loader: loader.error(),
                });
            });
    },
    updateChangePasswordDialog({ commit, state }, opened) {
        commit("SET_CHANGE_PASSWORD_DIALOG", opened);
    },
    updateEditingUser({ commit, state }, opened) {
        commit("SET_CHANGE_PASSWORD_DIALOG", opened);
    },
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
};
