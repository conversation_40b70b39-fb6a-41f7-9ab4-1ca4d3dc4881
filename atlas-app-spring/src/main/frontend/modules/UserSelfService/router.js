import Vue from "vue";
import VueRouter from "vue-router";
import { routerOptions, layout, route, configureRouter } from "Util/routerHelper";

Vue.use(VueRouter);

const PATH_PREFIX = "/userSelfService/";

const routes = [layout("Userselfservice", [route("UserSelfService", "UserSelfServiceHome", null, PATH_PREFIX)])];

const router = new VueRouter({
    mode: "history",
    routes,
    ...routerOptions,
});

configureRouter(router);

export default router;
