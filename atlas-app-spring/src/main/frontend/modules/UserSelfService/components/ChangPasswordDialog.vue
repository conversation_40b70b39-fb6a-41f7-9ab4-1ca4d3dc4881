<template>
    <v-card :loading="loading">
        <template slot="progress">
            <v-progress-linear color="primary" height="10" indeterminate />
        </template>
        <v-card-title> Change Password </v-card-title>
        <validation-observer ref="observer" v-slot="{ invalid }">
            <v-form @submit.prevent="submit">
                <v-card-text v-if="!complete">
                    <div>
                        <validation-provider v-slot="{ errors }" name="password" rules="required|password:@confirm">
                            <v-text-field
                                v-model="password"
                                label="Password"
                                outlined
                                dense
                                prepend-icon="mdi-lock"
                                required
                                :error-messages="errors"
                                type="password"
                            />
                        </validation-provider>
                        <validation-provider v-slot="{ errors }" name="confirm" rules="required">
                            <v-text-field
                                v-model="passwordConfirm"
                                label="Confirm Password"
                                outlined
                                dense
                                prepend-icon="mdi-lock"
                                required
                                :error-messages="errors"
                                type="password"
                            />
                        </validation-provider>
                    </div>
                </v-card-text>
                <v-card-actions v-if="!complete" class="justify-end">
                    <v-spacer />
                    <v-btn :disabled="invalid || loading" class="px-10" type="submit"> Change Password </v-btn>
                    <v-btn class="px-10" text @click="changePasswordDialogStatus"> Cancel </v-btn>
                </v-card-actions>
                <v-card-text v-if="complete">
                    <p>Your new password has been saved successfully.</p>
                </v-card-text>
                <v-card-actions v-if="complete" class="justify-end">
                    <v-btn color="primary" text @click="changePasswordDialogStatus"> Close </v-btn>
                </v-card-actions>
            </v-form>
        </validation-observer>
    </v-card>
</template>

<script>
import { sync } from "vuex-pathify";
import api from "Util/api";

export default {
    name: "ChangePasswordDialog",
    data: () => ({
        complete: false,
        loading: false,
        password: "",
        passwordConfirm: "",
        userId: null,
    }),
    computed: {
        dialog: sync("userStore/passwordDialog"),
    },
    methods: {
        submit() {
            if (this.$refs.observer.validate()) {
                this.loading = true;
                api.put("/account/profile/update-password", {
                    password: this.password,
                    passwordConfirm: this.passwordConfirm,
                })
                    .then(() => {
                        this.loading = false;
                        this.complete = true;
                    })
                    .catch(() => {
                        this.loading = false;
                    });
            }
        },
        changePasswordDialogStatus() {
            this.dialog = !this.dialog;
        },
    },
};
</script>
<style lang="scss" scoped>
.value {
    @media only screen and (min-width: 768px) {
        max-width: 315px;
    }
}
</style>
