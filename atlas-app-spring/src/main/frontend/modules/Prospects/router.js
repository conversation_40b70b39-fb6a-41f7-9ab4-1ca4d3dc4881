import Vue from "vue";
import VueRouter from "vue-router";
import { layout, route, routerOptions, configureRouter } from "Util/routerHelper";

Vue.use(VueRouter);

const PATH_PREFIX = "/prospects";

const routes = [layout("Default", [route("Prospects", "ProspectDetail", null, PATH_PREFIX + "/:prospectId")])];
const router = new VueRouter({
    mode: "history",
    routes,
    ...routerOptions,
});

configureRouter(router);

export default router;
