import { make } from "vuex-pathify";
import api from "@/util/api";
import loader from "@/util/loader";

const initialState = {
    prospect: {
        data: null,
        loader: loader.defaultState(),
    },
    leads: {
        data: null,
        loader: loader.defaultState(),
    },
};

const actions = {
    ...make.actions(initialState),

    fetchProspect({ commit }, prospectId) {
        commit("SET_PROSPECT_LOADER", loader.started());

        return api
            .get(`/prospects/${prospectId}`)
            .then((response) => {
                const prospect = response.data;

                commit("SET_PROSPECT", prospect);

                commit("SET_PROSPECT_LOADER", loader.successful());
                return _.get(response, "data");
            })
            .catch((error) => {
                console.error("fetchProspect error: ", error);
                commit("SET_PROSPECT_LOADER", loader.error(error));
                return Promise.reject(error);
            });
    },

    fetchLeads({ commit }, { prospectId, dealerId }) {
        commit("SET_LEADS_LOADER", loader.started());

        let params = "";
        if (dealerId) {
            params += "?dealerId=" + dealerId;
        }

        return api
            .get(`/prospects/${prospectId}/leads${params}`)
            .then((response) => {
                const leads = _.get(response, "data");
                commit("SET_LEADS", leads);
                commit("SET_LEADS_LOADER", loader.successful());
                return leads;
            })
            .catch((error) => {
                console.error("fetchProspect Leads error: ", error);
                commit("SET_LEADS_LOADER", loader.error(error));
                return Promise.reject(error);
            });
    },
};

const mutations = {
    ...make.mutations(initialState),

    SET_PROSPECT: (state, payload) => {
        state.prospect.data = payload;
    },

    SET_PROSPECT_LOADER: (state, payload) => {
        state.prospect.loader = payload;
    },

    SET_LEADS: (state, payload) => {
        state.leads.data = payload;
    },

    SET_LEADS_LOADER: (state, payload) => {
        state.leads.loader = payload;
    },
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
};
