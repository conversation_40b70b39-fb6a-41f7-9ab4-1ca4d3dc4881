<template>
    <!--loader-->
    <v-container v-if="loader.isLoading" class="fill-height">
        <v-row>
            <v-col cols="12">
                <div class="d-flex flex-column justify-center align-center">
                    <v-progress-circular :size="50" color="primary" indeterminate class="mb-2" />
                    <h1>Loading</h1>
                </div>
            </v-col>
        </v-row>
    </v-container>

    <!-- new prospect detail page for whisp prospects-->
    <ProspectUserDetails v-else-if="showNewProspectPage"></ProspectUserDetails>

    <!-- Old page -->
    <ProspectsHome v-else></ProspectsHome>
</template>

<script>
import { call, get } from "vuex-pathify";
import lodashGet from "lodash/get";
import lodashIsEmpty from "lodash/isEmpty";
import ProspectUserDetails from "@/modules/Prospects/views/ProspectUserDetails.vue";
import ProspectsHome from "@/modules/Prospects/views/ProspectsHome.vue";

export default {
    name: "ProspectDetail",
    components: {
        ProspectUserDetails,
        ProspectsHome,
    },
    computed: {
        featureFlags: get("loggedInUser/featureFlags"),
        loader: get("userProspects/prospect@loader"),
        prospectProfile: get("userProspects/prospect@data"),
        isWhispLeadEnabled() {
            return lodashGet(this.featureFlags, "WHISP_LEAD_ENABLED", false) || false;
        },
        isWhispProspect() {
            return this.prospectProfile.prospectType === "WHISP";
        },
        showNewProspectPage() {
            return this.isWhispLeadEnabled && this.isWhispProspect;
        },
        dealerId() {
            return this.$route.query.dealerIds;
        },
        prospectId() {
            return this.$route.params.prospectId;
        },
        programId() {
            if (this.$route.query.programIds) {
                return this.$route.query.programIds;
            }
            return null;
        },
    },
    watch: {
        dealerId(_value) {
            this.init();
        },
        programId(_value) {
            this.init();
        },
    },
    created() {
        this.init();
    },
    methods: {
        fetchProspect: call("userProspects/fetchProspect"),
        fetchLeads: call("userProspects/fetchLeads"),
        init() {
            this.fetchProspect(this.prospectId).catch(() => {
                this.loading = false;
                this.$router.push({
                    path: `/customers`,
                    query: {
                        dealerIds: this.dealerIds,
                    },
                });
            });
        },
    },
};
</script>

<style lang="scss">
.page-container {
    background-color: $gray-200;
}

.fill-page {
    height: 100%;
    display: flex;
    flex-wrap: wrap;
}
</style>
