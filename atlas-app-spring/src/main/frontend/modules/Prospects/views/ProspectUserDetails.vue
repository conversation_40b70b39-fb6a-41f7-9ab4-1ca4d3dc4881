<template>
    <v-container :key="dealerIds" fluid class="page-container">
        <v-row>
            <v-col cols="12" class="pb-0">
                <breadcrumbs :dealer-id="dealerIds" :user-name="fullUserName" />
            </v-col>
        </v-row>

        <!--  ACTION BAR  -->
        <v-row v-if="isAtlasActionbarEnabled">
            <v-col cols="12" class="pb-0">
                <action-bar :dealer-id="dealerIds" :user-id="user.id" :is-prospect="true" />
            </v-col>
        </v-row>
        <!--  ACTION BAR  -->

        <v-row>
            <v-col cols="12" class="pb-0">
                <ProspectInfoCard :dealer-id="dealerIds" :user-id="user.id"></ProspectInfoCard>
            </v-col>
        </v-row>
    </v-container>
</template>

<script>
import { call, get } from "vuex-pathify";
import ProspectInfoCard from "@/modules/Prospects/components/ProspectInfoCard.vue";
import Breadcrumbs from "@/modules/Prospects/components/Breadcrumbs.vue";
import ActionBar from "@/modules/Prospects/components/ActionBar.vue";
import lodashGet from "lodash/get";
import lodashIsNil from "lodash/isNil";

export default {
    name: "ProspectUserDetail",
    components: {
        ProspectInfoCard,
        Breadcrumbs,
        ActionBar,
    },
    data() {
        return {};
    },
    computed: {
        user: get("userProspects/prospect@data"),
        loader: get("userProspects/prospect@loader"),
        featureFlags: get("loggedInUser/featureFlags"),

        isSm() {
            return this.$vuetify.breakpoint.smAndDown;
        },
        isMd() {
            return this.$vuetify.breakpoint.mdAndDown;
        },
        dealerIds() {
            if (this.$route.params.dealerId) {
                return this.$route.params.dealerId;
            }

            if (this.$route.query.dealerIds) {
                return this.$route.query.dealerIds;
            }
            return null;
        },
        userId() {
            if (this.$route.params.prospectId) return this.$route.params.prospectId;

            return "";
        },
        programIds() {
            if (this.$route.query.programIds) {
                return this.$route.query.programIds;
            }
            return null;
        },
        isAtlasActionbarEnabled() {
            const result = lodashGet(this.featureFlags, "ATLAS_ACTION_BAR_ENABLED", false) || false;
            return result;
        },
        fullUserName() {
            return this.user.firstName + " " + this.user.lastName;
        },
    },
};
</script>
<style lang="scss" scoped>
@import "../../../sass/customer-prospect-shared.scss";
</style>
