<template>
    <v-data-table
        :loading="loader.isLoading"
        :headers="headers"
        :items="leads"
        :items-per-page="5"
        :page="leadsPageNumber"
        no-data-text="No leads found for this Upgrade Prospect"
        class="elevation-1"
        role="button"
    >
    </v-data-table>
</template>

<script>
import { get } from "vuex-pathify";
import lodashGet from "lodash/get";
import timeDateFormatter from "@/util/formatters";

export default {
    name: "Leads",
    props: {
        dealerId: {
            type: String,
            required: false,
            default: null,
        },
    },
    data() {
        return {
            leads: [],
            leadsPageNumber: 1,
            headers: [
                { text: "Vehicle Type", value: "tradeInType", sortable: false },
                {
                    text: "Vehicle",
                    value: "tradeInYMMT",
                    sortable: false,
                },
                {
                    text: "VIN",
                    value: "vin",
                    sortable: false,
                },
                {
                    text: "Lead Type",
                    value: "leadType",
                    sortable: false,
                },
                {
                    text: "time Sent",
                    value: "timeSent",
                    sortable: false,
                },
                {
                    text: "time Accepted",
                    value: "timeAccepted",
                    sortable: false,
                },
                {
                    text: "status",
                    value: "status",
                    sortable: false,
                },
                {
                    text: "Message",
                    value: "message",
                    sortable: false,
                },
                {
                    text: "Dealer",
                    value: "dealerName",
                    sortable: false,
                },
            ],
        };
    },
    computed: {
        leadsData: get("userProspects/leads@data"),
        loader: get("userProspects/leads@loader"),
    },
    watch: {
        leadsData(value) {
            this.leads = lodashGet(this.leadsData, "content", []);
        },
    },

    methods: {
        emitOpenEvent(dealId) {
            this.$emit("open", dealId);
        },
        timeDateFormatter(date) {
            return timeDateFormatter(date);
        },
    },
};
</script>
