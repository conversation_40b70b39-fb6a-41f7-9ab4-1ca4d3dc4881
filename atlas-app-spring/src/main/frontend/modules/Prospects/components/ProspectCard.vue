<template>
    <div>
        <v-card width="100%">
            <v-container>
                <v-row>
                    <v-col cols="12">
                        <v-card-title class="text-capitalize">
                            <div class="mr-auto">
                                {{ prospectProfile.firstName }}
                                {{ prospectProfile.lastName }}
                            </div>

                            <template v-if="displayCRMButton">
                                <send-to-crm-enhanced-dialog
                                    v-if="isSendToCRMEnhancedDialogEnabled"
                                    :user-id="prospectProfile.id"
                                    :dealer-id="dealerId"
                                    :selected-dealer-id="selectedDealerId"
                                    :name="prospectFullName"
                                    :dealership="selectedDealerName"
                                    from="prospects"
                                >
                                    <template #activator>
                                        <v-btn x-small outlined class="w-fit"> Send to CRM </v-btn>
                                    </template>
                                </send-to-crm-enhanced-dialog>
                                <v-btn
                                    v-else
                                    x-small
                                    outlined
                                    :disabled="disableSendToCRM"
                                    @click.prevent="openCrmModal()"
                                >
                                    Send to CRM
                                </v-btn>
                            </template>
                        </v-card-title>
                        <v-card-subtitle class="pb-0">
                            <div class="d-flex flex-column">
                                <div>
                                    Customer Since:
                                    {{ prospectProfile.createdDate | formatEpochDate }}
                                </div>
                            </div>
                        </v-card-subtitle>
                        <v-list two-line>
                            <credit-profile v-if="creditProfile" :credit-profile="creditProfile" />

                            <v-list-item>
                                <v-list-item-icon>
                                    <v-icon color="primary"> mdi-phone </v-icon>
                                </v-list-item-icon>

                                <v-list-item-content>
                                    <v-list-item-title>
                                        <a :href="`tel: ${prospectProfile.phoneNumber}`">
                                            {{ prospectProfile.phoneNumber | phoneFormatter }}
                                        </a>
                                    </v-list-item-title>
                                    <v-list-item-subtitle v-if="prospectProfile.phoneNumberInfo">
                                        {{ prospectProfile.phoneNumberInfo.carrier.type }}
                                    </v-list-item-subtitle>
                                </v-list-item-content>
                            </v-list-item>

                            <v-divider inset />

                            <v-list-item>
                                <v-list-item-icon>
                                    <v-icon color="primary"> mdi-email </v-icon>
                                </v-list-item-icon>

                                <v-list-item-content>
                                    <v-list-item-title>
                                        <a :href="`mailto: ${prospectProfile.email}`">
                                            {{ prospectProfile.email }}
                                        </a>
                                    </v-list-item-title>
                                    <v-list-item-subtitle> Personal </v-list-item-subtitle>
                                </v-list-item-content>
                            </v-list-item>

                            <v-divider inset />

                            <v-list-item>
                                <v-list-item-icon>
                                    <v-icon color="primary"> mdi-map-marker </v-icon>
                                </v-list-item-icon>

                                <v-list-item-content>
                                    <v-list-item-title>
                                        {{ addressLine1 }}
                                    </v-list-item-title>
                                    <v-list-item-subtitle>
                                        {{ addressLine2 }}
                                    </v-list-item-subtitle>
                                </v-list-item-content>
                            </v-list-item>

                            <v-divider inset />

                            <v-list-item>
                                <v-list-item-icon>
                                    <v-icon class="mdiNumericIcon" color="primary" size="large"> mdi-numeric </v-icon>
                                </v-list-item-icon>

                                <v-list-item-content>
                                    <v-list-item-title>
                                        {{ !!prospectProfile.pin ? prospectProfile.pin : "Non Pin User" }}
                                    </v-list-item-title>
                                    <v-list-item-subtitle v-if="!!prospectProfile.pin"> PIN </v-list-item-subtitle>
                                </v-list-item-content>
                            </v-list-item>

                            <template v-if="prospectProfile.clientAdvisor">
                                <v-divider inset />
                                <v-list-item>
                                    <v-list-item-icon>
                                        <img
                                            class="originating-salesperson-icon"
                                            src="@/assets/svgs/clinical-notes.svg"
                                            alt="Originating Salesperson Icon"
                                        />
                                    </v-list-item-icon>

                                    <v-list-item-content>
                                        <v-list-item-title> {{ prospectProfile.clientAdvisor }} </v-list-item-title>
                                        <v-list-item-subtitle> Originating Salesperson </v-list-item-subtitle>
                                    </v-list-item-content>
                                </v-list-item>
                            </template>
                        </v-list>
                    </v-col>
                </v-row>
            </v-container>
        </v-card>
        <v-dialog v-model="crmDialog" persistent width="400">
            <v-card>
                <v-card-title> Send Upgrade Prospect to CRM</v-card-title>
                <v-card-text>
                    Are you sure you want to send
                    <strong class="text-capitalize">
                        {{ prospectProfile.firstName }}
                        {{ prospectProfile.lastName }}
                    </strong>
                    to your CRM?
                </v-card-text>
                <v-card-actions>
                    <v-spacer />
                    <v-btn color="error" text @click="crmDialog = false"> Cancel</v-btn>
                    <v-btn color="success" text @click="sendToCrm()"> Send</v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import CreditProfile from "@/modules/Customers/components/UserDetails/CreditProfile";
import api from "Util/api";
import lodashGet from "lodash/get";
import lodashIsNil from "lodash/isNil";
import { call, get } from "vuex-pathify";
import isNil from "lodash/isNil";
import SendToCrmEnhancedDialog from "Modules/Customers/components/UserDetails/SendToCrmEnhancedDialog.vue";

export default {
    name: "ProspectsCard",
    components: { SendToCrmEnhancedDialog, CreditProfile },
    props: {
        prospectProfile: {
            type: Object,
            required: true,
        },
    },
    data() {
        return {
            creditProfile: null,
            crmDialog: false,
        };
    },
    computed: {
        featureFlags: get("loggedInUser/featureFlags"),
        isBMWProgramUser: get("loggedInUser/isBMWProgramUser"),
        selectedDealerId: get("loggedInUser/selectedDealer@id"),
        selectedDealerName: get("loggedInUser/selectedDealer@name"),
        addressLine1() {
            return lodashGet(this.prospectProfile.addressView, "street1");
        },
        addressLine2() {
            let addr2 = [];
            let addressLine2 = "";
            const city = lodashGet(this.prospectProfile.addressView, "city");
            const stateCode = lodashGet(this.prospectProfile.addressView, "state", "");
            const zip = lodashGet(this.prospectProfile.addressView, "zipCode", "");
            const stateZip = stateCode ? `${stateCode} ${zip}` : zip;
            addr2 = [city, stateZip];
            addressLine2 = addr2
                .filter(function (value) {
                    return !lodashIsNil(value);
                })
                .join(", ");
            return addressLine2;
        },
        prospectFullName() {
            return `${this.prospectProfile.firstName} ${this.prospectProfile.lastName}`;
        },
        isCRMLeadsForProspectEnabled() {
            return lodashGet(this.featureFlags, "CRM_LEADS_FOR_PROSPECTS", false) || false;
        },
        dealerId() {
            return this.$route.query.dealerIds;
        },
        isProgramUser() {
            return this.$acl.hasAuthority("ROLE_PROGRAM");
        },
        disableSendToCRM() {
            return !(this.isCRMLeadsForProspectEnabled && !isNil(this.dealerId));
        },
        displayCRMButton() {
            return this.isCRMLeadsForProspectEnabled;
        },
        isSendToCRMEnhancedDialogEnabled() {
            const result = lodashGet(this.featureFlags, "SEND_TO_CRM_ENHANCEMENT", false) || false;
            return result;
        },
    },
    mounted() {
        this.fetchCreditProfile(this.prospectProfile.id);
    },
    methods: {
        fetchLeads: call("userProspects/fetchLeads"),
        fetchCreditProfile(prospectId) {
            api.get(`/prospects/${prospectId}/credit-profile`).then((res) => {
                this.creditProfile = res.data;
            });
        },
        openCrmModal() {
            return (this.crmDialog = !this.crmDialog);
        },
        sendToCrm() {
            this.crmDialog = false;
            if (!this.dealerId) {
                const dealerMessage = "Please select a dealer to send CRM.";
                this.$toast.error(dealerMessage);
            } else {
                const dealerId = this.dealerId;
                const prospectId = this.prospectProfile.id;
                const firstName = this.prospectProfile.firstName;
                const lastName = this.prospectProfile.lastName;

                api.post(`/dealer/${dealerId}/prospects/${prospectId}/send-to-crm`)
                    .then(() => {
                        this.confirmText = `Lead has been delivered to your CRM for Upgrade Prospect ${firstName} ${lastName}`;
                        this.fetchLeads({
                            prospectId: prospectId,
                            dealerId: dealerId,
                        });
                    })
                    .catch(() => {
                        this.confirmText = "Error sending lead to your CRM.";
                    })
                    .finally(() => {
                        this.$toast(this.confirmText);
                    });
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.mdiNumericIcon {
    padding: 0px 2px;
    background-color: var(--v-primary-base) !important;
    color: white !important;
    border-radius: 2px;
}
.originating-salesperson-icon {
    width: 24px;
    height: 24px;
    flex-shrink: 0;
}
</style>
