<template>
    <v-card class="customer-info-wrapper" flat>
        <ProspectContactInfo :user-id="userId" :dealer-id="dealerId"></ProspectContactInfo>
    </v-card>
</template>

<script>
import ProspectContactInfo from "@/modules/Prospects/components/ProspectContactInfo.vue";

export default {
    components: {
        ProspectContactInfo,
    },

    props: {
        dealerId: {
            type: String,
            required: false,
            default: null,
        },
        userId: {
            type: String,
            required: true,
        },
    },
};
</script>

<style lang="scss" scoped>
.customer-info-wrapper {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 16px;
    padding-bottom: 20px;
}
</style>
