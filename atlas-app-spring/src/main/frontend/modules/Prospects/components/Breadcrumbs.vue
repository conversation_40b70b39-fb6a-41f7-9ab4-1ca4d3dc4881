<template>
    <v-card flat class="pa-4">
        <v-row>
            <v-col cols="12">
                <v-breadcrumbs class="pa-0">
                    <v-breadcrumbs-item :href="dealerIdsParam"> Customer Search </v-breadcrumbs-item>
                    <v-breadcrumbs-divider>/</v-breadcrumbs-divider>
                    <v-breadcrumbs-item :active="true">
                        <strong> {{ user.firstName }} {{ user.lastName }} </strong>
                    </v-breadcrumbs-item>
                </v-breadcrumbs>
            </v-col>
        </v-row>
    </v-card>
</template>

<script>
import { get } from "vuex-pathify";
export default {
    name: "Breadcrumbs",
    props: {
        dealerId: {
            type: String,
            required: false,
            default: null,
        },
    },
    computed: {
        user: get("userProspects/prospect@data"),
        dealerIdsParam() {
            const dealerIdParmPath = `/customers?dealerIds=${this.dealerId}`;
            const customersPath = "/customers";
            const path = this.dealerId ? dealerIdParmPath : customersPath;
            return path;
        },
    },
};
</script>
