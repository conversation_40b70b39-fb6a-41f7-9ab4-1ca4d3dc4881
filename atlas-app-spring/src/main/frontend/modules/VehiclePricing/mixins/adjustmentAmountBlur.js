import isNil from "lodash/isNil";
import lodashIsNaN from "lodash/isNaN";

export default {
    methods: {
        adjustmentAmountBlur({ adjustment }) {
            const adjustmentAmountInvalid =
                isNil(adjustment.amount) ||
                lodashIsNaN(adjustment.amount) ||
                (adjustment.type === "PERCENTAGE" && adjustment.amount < -100);
            if (adjustmentAmountInvalid) {
                adjustment.amount = null;
                adjustment.enabled = false;
            }
        },
    },
};
