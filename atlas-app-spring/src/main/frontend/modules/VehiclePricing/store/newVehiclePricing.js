import { make } from "vuex-pathify";

const state = {
    vehicleSearchAttempted: false,
    vehicleSearchFoundVehicle: false,
    vehicleSearchFailureMessage: null,
    enableLeasePricing: false,
    enforcePricingRules: false,
    pricingFormStyleFilter: null,
    pricingFormInventoryIdFilter: null,
    stockTypePriceAdjustment: {
        adjustment: {
            enabled: false,
        },
    },
    searchForm: {},
};

const mutations = {
    ...make.mutations(state),
};

const actions = {
    ...make.actions(state),
};

const getters = {
    ...make.getters(state),
};

export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters,
};
