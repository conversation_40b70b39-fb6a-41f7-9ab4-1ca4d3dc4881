import Vuex from "vuex";
import Vue from "vue";
import pathify from "./pathify";
import { createStore } from "@/store/common";
import vehiclePricing from "./vehiclePricing";
import usedVehiclePricing from "./usedVehiclePricing";
import newVehiclePricing from "./newVehiclePricing";

Vue.use(Vuex);

export default createStore(
    {
        vehiclePricing,
        usedVehiclePricing,
        newVehiclePricing,
    },
    [pathify.plugin]
);
