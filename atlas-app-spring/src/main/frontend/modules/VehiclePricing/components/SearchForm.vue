<template>
    <v-card>
        <v-card-title>Vehicle Search</v-card-title>
        <v-divider></v-divider>
        <v-container fluid>
            <v-form v-model="valid">
                <v-row>
                    <v-col cols="12" sm="4" lg="2">
                        <v-text-field
                            v-model="searchFormStockNumber"
                            :rules="[rules.characterLimit]"
                            dense
                            outlined
                            label="Stock #"
                        />
                    </v-col>
                    <v-col cols="12" sm="4" lg="2" style="min-width: 290px !important">
                        <v-text-field v-model="searchFormVin" :rules="[rules.vin]" label="VIN" dense outlined />
                    </v-col>
                    <v-col cols="12" sm="2" lg="1" style="min-width: 160px !important">
                        <v-select v-model="searchFormYear" dense outlined :items="yearOptions" label="Year" />
                    </v-col>
                    <v-col cols="12" sm="4" lg="2">
                        <v-select v-model="searchFormMake" dense outlined :items="makeOptions" label="Make" />
                    </v-col>
                    <v-col cols="12" sm="4" lg="2" style="min-width: 290px !important">
                        <v-select v-model="searchFormModel" dense outlined :items="modelOptions" label="Model" />
                    </v-col>
                    <v-col cols="12" sm="2" lg="auto" style="min-width: 160px !important">
                        <v-select v-model="searchFormActive" dense outlined :items="typeOptions" label="Type" />
                    </v-col>
                </v-row>
                <v-row>
                    <v-col cols="6" sm="2" lg="1">
                        <v-btn
                            type="submit"
                            size="sm"
                            :disabled="!valid"
                            :loading="submitting"
                            color="primary"
                            @click.prevent="search"
                        >
                            Search
                        </v-btn>
                    </v-col>
                    <v-col cols="6" sm="2" lg="1">
                        <v-btn block text size="sm" :loading="submitting" @click="reset">
                            <span>Reset</span>
                        </v-btn>
                    </v-col>
                </v-row>
            </v-form>
        </v-container>
    </v-card>
</template>

<script>
import api from "Util/api";
import { sync, get } from "vuex-pathify";
import { characterLimit, vin } from "Util/formRules";
import { sanitize } from "Util/sanitize";
import lodashGet from "lodash/get";

export default {
    name: "SearchForm",
    props: {
        dealerIds: {
            type: String,
            required: false,
            default: null,
        },
    },
    data() {
        return {
            submitting: false,
            typeOptions: [
                { text: "Active", value: true },
                { text: "Not Active", value: false },
            ],
            loading: true,
            rules: { characterLimit, vin },
            valid: true,
        };
    },
    computed: {
        ymm: get("vehiclePricing/ymm"),
        dealer: get("vehiclePricing/dealer"),
        stockType: get("vehiclePricing/stockType"),
        searchForm: sync("newVehiclePricing/searchForm"),
        searchFormActive: sync("newVehiclePricing/searchForm@active"),
        searchFormStockNumber: sync("newVehiclePricing/searchForm@stockNumber"),
        searchFormVin: sync("newVehiclePricing/searchForm@vin"),
        searchFormYear: sync("newVehiclePricing/searchForm@year"),
        searchFormMake: sync("newVehiclePricing/searchForm@make"),
        searchFormModel: sync("newVehiclePricing/searchForm@model"),
        searchFormDealerIds: sync("newVehiclePricing/searchForm@dealerIds"),

        yearOptions() {
            return lodashGet(this.ymm, "years");
        },

        makeOptions() {
            return lodashGet(this.ymm, "makes");
        },

        modelOptions() {
            return lodashGet(this.ymm, "models");
        },
    },
    mounted() {
        this.searchFormDealerIds = this.dealerIds;
    },
    created() {
        this.searchFormDealerIds = this.dealerIds;
        this.loading = true;
        this.search(this.$route.query);
    },
    methods: {
        reset() {
            this.searchFormActive = null;
            this.searchFormStockNumber = null;
            this.searchFormVin = null;
            this.searchFormYear = null;
            this.searchFormMake = null;
            this.searchFormModel = null;
            this.search();
        },
        search() {
            if (!this.valid) return;
            this.submitting = !this.loading;
            this.loading = true;

            const query = sanitize(this.searchForm, { deep: true });
            api.get(`/stratus/vehicle-pricing/${this.stockType}/form`, query)
                .then((response) => {
                    this.searchForm = response.data.searchForm;
                    this.loading = false;
                    this.submitting = false;
                    //commenting out for now as its causes an recursive loading situation for some reason
                    //it was originally put in place to allow deep linking into the pricing portal with filters
                    // const queryStr =
                    //     "?" +
                    //     Object.keys(query)
                    //         .map((k) => k + "=" + query[k])
                    //         .join("&")
                    //         .replace(/=null/g, "");
                    // window.history.pushState(null, null, queryStr);

                    this.$emit("search-completed", response.data);
                })
                .catch((error) => {
                    console.error(error);
                    this.loading = false;
                    this.submitting = false;
                })
                .finally(() => {
                    this.searchFormDealerIds = this.dealerIds;
                });
        },
    },
};
</script>
