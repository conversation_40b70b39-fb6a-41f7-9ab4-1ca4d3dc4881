<template>
    <thead>
        <tr>
            <th scope="col">Model, Trim, Stock Number</th>
            <th scope="col">Count, Vin</th>
            <th scope="col">MSRP</th>
            <th scope="col">Invoice</th>
            <th scope="col">Market Price</th>
            <th scope="col">Days In Inventory</th>
            <th scope="col">Enable Pricing</th>
            <th scope="col">Adjusted Value</th>
            <th scope="col">Adjustment Amount</th>
            <th v-if="enableLeasePricing" scope="col">
                Lease Adjustment (optional)
                <v-tooltip bottom>
                    <template #activator="{ on, attrs }">
                        <i aria-hidden="true" class="fas fa-info-circle" v-bind="attrs" v-on="on" />
                    </template>
                    <span>
                        If set, will be applied to calculate the discounted lease price, otherwise the finance
                        adjustment will be used
                    </span>
                </v-tooltip>
            </th>
            <th scope="col">Fixed Price Amount</th>
            <th scope="col">Enable Fixed Pricing</th>
            <th scope="col">Internet Price</th>
            <th scope="col">Sale Price</th>
            <th scope="col">Rebates</th>
            <th scope="col">Active</th>
        </tr>
    </thead>
</template>
<script>
export default {
    name: "NewPricedCarRowHeader",
    props: {
        enableLeasePricing: {
            type: Boolean,
            required: true,
            default: false,
        },
    },
};
</script>
