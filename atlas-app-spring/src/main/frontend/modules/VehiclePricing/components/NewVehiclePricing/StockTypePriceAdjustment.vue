<template>
    <v-card class="fill-height">
        <v-card-title>Global Pricing</v-card-title>
        <v-divider></v-divider>
        <v-container>
            <v-row>
                <v-col cols="12">
                    <v-checkbox
                        v-model="adjustmentEnabled"
                        :disabled="loading"
                        dense
                        class="mt-0"
                        label="Enable Global Pricing"
                        persistent-hint
                        hint="Enable if you want adjustment amounts to apply to ALL 'New' vehicles"
                    />
                </v-col>
            </v-row>
            <v-row>
                <v-col cols="12">
                    <v-select
                        v-model="adjustmentAdjustedValue"
                        outlined
                        dense
                        :disabled="!adjustmentEnabled"
                        :items="adjustedValueOptions"
                        label="Adjusted Value"
                        hide-details
                    />
                </v-col>
                <v-col cols="12">
                    <v-select
                        v-model="adjustmentType"
                        outlined
                        dense
                        hide-details
                        :disabled="!adjustmentEnabled"
                        :items="adjustmentTypeOptions"
                        label="Adjustment Type"
                    />
                </v-col>
                <v-col cols="12">
                    <v-text-field
                        v-model.number="adjustmentAmount"
                        label="Adjustment Amount"
                        outlined
                        dense
                        :prefix="adjustmentAmountInputPrefix"
                        :required="adjustmentEnabled"
                        :disabled="!adjustmentEnabled"
                        type="number"
                        pattern="^-{0,1}(?!0.)\d+$"
                        max="9999"
                        data-pattern-error="Please enter a valid whole dollar amount"
                        :rules="[rules.numericOnly, rules.maxValue(9999)]"
                        @blur="stockTypeAdjustmentAmountBlur"
                    />
                </v-col>
            </v-row>
            <v-row v-if="enableLeasePricing">
                <v-col cols="12">
                    <v-select
                        v-model="adjustmentAdjustedValue"
                        dense
                        outlined
                        :disabled="!adjustmentEnabled"
                        :items="adjustedValueOptions"
                        label="Lease Adjustment Amount (optional)"
                        hide-details
                    />
                </v-col>
                <v-col cols="12">
                    <v-text-field
                        v-model.number="adjustmentAmount"
                        outlined
                        dense
                        prefix="$"
                        :required="adjustmentEnabled"
                        :disabled="!adjustmentEnabled"
                        persistent-hint
                        type="number"
                        pattern="^-{0,1}(?!0.)\d+$"
                        max="9999"
                        :rules="[rules.numericOnly, rules.maxValue(9999)]"
                        data-pattern-error="Please enter a valid whole dollar amount"
                        hide-details
                        @blur="stockTypeAdjustmentAmountBlur"
                    />
                </v-col>
            </v-row>
        </v-container>
    </v-card>
</template>
<script>
import { get, sync } from "vuex-pathify";
import { numericOnly, maxValue } from "Util/formRules";
import isNil from "lodash/isNil";
import lodashIsNaN from "lodash/isNaN";

export default {
    name: "StockTypePriceAdjustment",

    props: {
        enableInternetPrice: {
            type: Boolean,
            required: false,
            default: false,
        },
        enableLeasePricing: {
            type: Boolean,
            required: true,
            default: false,
        },
        adjustedValueOptions: {
            type: Array,
            required: true,
        },
        adjustmentTypeOptions: {
            type: Array,
            required: true,
        },
    },

    data() {
        return {
            rules: { numericOnly, maxValue },
            valid: true,
        };
    },

    computed: {
        loading: get("vehiclePricing/loading"),
        adjustmentType: sync("newVehiclePricing/<EMAIL>"),
        adjustmentLeaseType: sync("newVehiclePricing/<EMAIL>"),
        adjustmentEnabled: sync("newVehiclePricing/<EMAIL>"),
        adjustmentAmount: sync("newVehiclePricing/<EMAIL>"),
        adjustmentLeaseAmount: sync("newVehiclePricing/<EMAIL>"),
        adjustmentAdjustedValue: sync("newVehiclePricing/<EMAIL>"),
        adjustmentAmountInputPrefix() {
            return this.adjustmentType === "PERCENTAGE" ? "%" : "$";
        },
    },

    methods: {
        stockTypeAdjustmentAmountBlur() {
            const adjustmentAmountInvalid =
                isNil(this.adjustmentAmount) ||
                lodashIsNaN(this.adjustmentAmount) ||
                (this.adjustmentType === "PERCENTAGE" && this.adjustmentAmount < -100);
            if (adjustmentAmountInvalid) {
                this.adjustmentAmount = 0;
            }
        },
    },
};
</script>
