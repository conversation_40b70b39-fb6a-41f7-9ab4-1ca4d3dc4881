<template>
    <tr
        class="inventory-vehicle-row price-adjustment-row"
        :class="pricedCar.vehicle.activeStatus ? '' : 'inactive-vehicle'"
    >
        <td class="stock-name" style="text-align: left; padding-left: 80px">
            {{ pricedCar.vehicle.stockNumber }}
        </td>
        <td>{{ pricedCar.vehicle.vin }}</td>
        <td>
            {{ pricedCar.vehicle.msrp | numeral("$0,0") }}
        </td>
        <td>
            {{ pricedCar.vehicle.invoicePrice | numeral("$0,0") }}
        </td>
        <td>
            {{ pricedCar.vehicle.averageMarketPrice | numeral("$0,0") }}
        </td>
        <td>{{ pricedCar.vehicle.daysInStock }}</td>
        <td>
            <v-checkbox
                :input-value="pricedCar.adjustment.enabled"
                :true-value="true"
                :false-value="false"
                @change="(v) => enablePricingChangeEvent(v)"
            />
        </td>
        <td>
            <adjustment-value
                :adjustment="pricedCar.adjustment"
                :adjusted-value-options="adjustedValueOptions"
                @input="(v) => change('adjustment.adjustedValue', v)"
            />
        </td>
        <td>
            <div class="d-inline-flex">
                <adjustment-type
                    :adjustment="pricedCar.adjustment"
                    :adjustment-type-options="adjustmentTypeOptions"
                    @input="(v) => change('adjustment.type', v)"
                />
                <v-text-field
                    :value="pricedCar.adjustment.amount"
                    :disabled="!pricedCar.adjustment.enabled || pricedCar.adjustment.fixedPrice"
                    type="number"
                    class="adjustment-amount"
                    pattern="^-{0,1}(?!0.)\d+$"
                    data-pattern-error="Please enter a whole number"
                    max="999999"
                    :rules="[rules.numericOnly, rules.maxValue(999999)]"
                    @blur="adjustmentAmountBlur(pricedCar)"
                    @input="(v) => change('adjustment.amount', v, 'number')"
                />
            </div>
        </td>
        <td v-if="enableLeasePricing">
            <div class="d-inline-flex">
                <v-select
                    :value="pricedCar.adjustment.leaseType"
                    :items="adjustmentTypeOptions"
                    :disabled="!pricedCar.adjustment.enabled || pricedCar.adjustment.fixedPrice"
                    class="adjustment-type"
                    @input="(v) => change('adjustment.leaseType', v)"
                />
                <v-text-field
                    :value="pricedCar.adjustment.leaseAmount"
                    :disabled="!pricedCar.adjustment.enabled || pricedCar.adjustment.fixedPrice"
                    type="number"
                    class="adjustment-lease-amount"
                    style="width: 180px"
                    pattern="^-{0,1}(?!0.)\d+$"
                    data-pattern-error="Please enter a whole number"
                    :rules="[rules.numericOnly, rules.maxValue(999999)]"
                    max="999999"
                    @input="(v) => change('adjustment.leaseAmount', v, 'number')"
                />
            </div>
        </td>
        <td>
            <v-text-field
                :value="pricedCar.adjustment.fixedPriceAmount"
                :disabled="!pricedCar.adjustment.enabled || !pricedCar.adjustment.fixedPrice"
                type="number"
                class="adjustment-fixed-price-amount inventory-vehicle"
                style="width: 180px"
                prefix="$"
                @blur="fixedPriceBlur(pricedCar)"
                @input="(v) => change('adjustment.fixedPriceAmount', v, 'number')"
            />
        </td>
        <td>
            <v-checkbox
                :input-value="pricedCar.adjustment.fixedPrice"
                :disabled="!pricedCar.adjustment.enabled"
                class="adjustment-fixed-price inventory-vehicle"
                @change="(v) => change('adjustment.fixedPrice', v)"
            />
        </td>
        <td>
            <span class="internet-price">
                {{ pricedCar.vehicle.internetPrice | numeral("$0,0") }}
            </span>
        </td>
        <td>
            <span class="carsaver-price">
                {{ pricedCar.vehicle.price | numeral("$0,0") }}
            </span>
        </td>
        <td>
            <span class="carsaver-rebates"> -{{ pricedCar.vehicle.totalCashIncentive | numeral("$0,0") }} </span>
        </td>
        <td>
            <v-tooltip left>
                <template #activator="{ on, attrs }">
                    <boolean-indicator :value="pricedCar.vehicle.activeStatus" v-bind="attrs" v-on="on" />
                </template>
                <span>
                    {{ pricedCar.vehicle.activeStatusDesc }}
                </span>
            </v-tooltip>
        </td>
    </tr>
</template>
<script>
import isNil from "lodash/isNil";
import toNumber from "lodash/toNumber";
import lodashIsNaN from "lodash/isNaN";
import lodashSet from "lodash/set";
import BooleanIndicator from "Components/BooleanIndicator";
import adjustmentAmountBlur from "@/modules/VehiclePricing/mixins/adjustmentAmountBlur";
import EventBus from "Util/eventBus";
import AdjustmentValue from "@/modules/VehiclePricing/components/AdjustmentValue";
import AdjustmentType from "@/modules/VehiclePricing/components/AdjustmentType";
import { numericOnly, maxValue } from "Util/formRules";

export default {
    name: "NewPricedCarRow",
    components: { AdjustmentType, AdjustmentValue, BooleanIndicator },
    mixins: [adjustmentAmountBlur],
    props: {
        pricedCar: {
            type: Object,
            required: true,
        },
        enableLeasePricing: {
            type: Boolean,
            required: true,
        },
        enableInternetPrice: {
            type: Boolean,
            required: false,
            default: false,
        },
        adjustedValueOptions: {
            type: Array,
            required: true,
        },
        adjustmentTypeOptions: {
            type: Array,
            required: true,
        },
    },
    data() {
        return {
            rules: { numericOnly, maxValue },
        };
    },
    methods: {
        fixedPriceBlur({ adjustment }) {
            const fixedPriceInvalid =
                isNil(adjustment.fixedPriceAmount) ||
                lodashIsNaN(adjustment.fixedPriceAmount) ||
                adjustment.fixedPriceAmount <= 0;
            if (fixedPriceInvalid) {
                adjustment.fixedPrice = false;
                adjustment.fixedPriceAmount = null;
            } else {
                const fixedPricedLessThan10000 = adjustment.fixedPriceAmount < 10000;
                if (fixedPricedLessThan10000) {
                    EventBus.$emit("low-fixed-price-warning-modal");
                }
            }
        },

        change(path, value, type = null) {
            const newValue = {
                ...this.pricedCar,
            };

            if (type === "number") {
                value = toNumber(value);
            }

            lodashSet(newValue, path, value);

            this.$emit("change", newValue);
        },

        enablePricingChangeEvent(value) {
            this.change("adjustment.enabled", value);

            if (value === true && this.pricedCar.adjustment.adjustedValue === null) {
                this.change("adjustment.adjustedValue", "INVOICE");
            }
        },
    },
};
</script>
