<template>
    <div>
        <div>
            <table-search-count-label :page-number="page - 1" :page-size="pageSize" :total-elements="totalElements" />

            <v-divider />
            <v-data-table
                :loading="loading"
                :page="page"
                :page-count="totalPages"
                :headers="headers"
                :items="cars"
                :server-items-length="totalElements"
                disable-pagination
                fixed-header
                hide-default-footer
                @update:options="updateOptions"
            >
                <template #item.vehicle.retailPrice="{ item }">
                    {{ item.vehicle.retailPrice | numeral("$0,0") }}
                </template>

                <template #item.vehicle.internetPrice="{ item }">
                    {{ item.vehicle.internetPrice | numeral("$0,0") }}
                </template>

                <template #item.adjustment.enabled="{ item }">
                    <v-checkbox v-model="item.adjustment.enabled" />
                </template>

                <template #item.adjustmentAmount="{ item, index }">
                    <div class="d-inline-flex">
                        <adjustment-type
                            :adjustment="item.adjustment"
                            :adjustment-type-options="adjustmentTypeOptions"
                            @input="(v) => change(index, 'adjustment.type', v)"
                        />
                        <v-text-field
                            :value="item.adjustment.amount"
                            :disabled="!item.adjustment.enabled || item.adjustment.fixedPrice"
                            type="number"
                            class="adjustment-amount"
                            style="width: 180px"
                            pattern="^-{0,1}(?!0.)\d+$"
                            data-pattern-error="Please enter a whole number"
                            max="999999"
                            :rules="[rules.numericOnly, rules.maxValue(999999)]"
                            @blur="adjustmentAmountBlur(item)"
                            @input="(v) => change(index, 'amount', parseInt(v))"
                        />
                    </div>
                </template>

                <template #item.fixedPriceAmount="{ item }">
                    <v-text-field
                        v-model.number="item.adjustment.fixedPriceAmount"
                        prefix="$"
                        :required="item.adjustment.fixedPrice"
                        :disabled="!item.adjustment.enabled || !item.adjustment.fixedPrice"
                        type="number"
                        pattern="^(?!0.)\d+$"
                        data-pattern-error="Please enter a valid whole dollar amount"
                        :rules="[rules.numericOnly, rules.maxValue(999999)]"
                        max="999999"
                        @blur="fixedPriceBlur(item)"
                    />
                </template>

                <template #item.enableFixedPricing="{ item }">
                    <v-checkbox v-model="item.adjustment.fixedPrice" :disabled="!item.adjustment.enabled" />
                </template>

                <template #item.vehicle.price="{ item }">
                    {{ item.vehicle.price | numeral("$0,0") }}
                </template>

                <template #item.vehicle.activeStatus="{ item }">
                    <v-tooltip bottom>
                        <template #activator="{ on, attrs }">
                            <boolean-indicator :value="item.vehicle.activeStatus" v-bind="attrs" v-on="on">
                                {{ item.vehicle.activeStatus }}
                            </boolean-indicator>
                        </template>

                        <span>
                            {{ item.vehicle.activeStatusDesc }}
                        </span>
                    </v-tooltip>
                </template>
            </v-data-table>
        </div>
        <div class="text-center pt-2">
            <v-pagination v-model="page" :length="totalPages" />
        </div>
    </div>
</template>

<script>
import BooleanIndicator from "Components/BooleanIndicator";
import adjustmentAmountBlur from "@/modules/VehiclePricing/mixins/adjustmentAmountBlur";
import lodashGet from "lodash/get";
import isNil from "lodash/isNil";
import lodashIsNaN from "lodash/isNaN";
import AdjustmentType from "@/modules/VehiclePricing/components/AdjustmentType";
import TableSearchCountLabel from "Components/TableSearchCountLabel";
import EventBus from "Util/eventBus";
import { get, sync } from "vuex-pathify";
import { numericOnly, maxValue } from "Util/formRules";

export default {
    components: { AdjustmentType, BooleanIndicator, TableSearchCountLabel },
    mixins: [adjustmentAmountBlur],
    props: {
        loading: {
            type: Boolean,
            required: false,
            default: false,
        },
        cars: {
            type: Array,
            required: true,
        },
        adjustmentTypeOptions: {
            type: Array,
            required: true,
        },
    },
    data() {
        return {
            options: {},
            headers: [
                {
                    text: "Stock Number",
                    value: "vehicle.stockNumber",
                },
                {
                    text: "VIN",
                    value: "vehicle.vin",
                },
                {
                    text: "Year",
                    value: "vehicle.year",
                },
                {
                    text: "Make",
                    value: "vehicle.make",
                },
                {
                    text: "Model",
                    value: "vehicle.model",
                },
                {
                    text: "Days In Inventory",
                    value: "vehicle.daysInStock",
                },
                {
                    text: "Retail Price",
                    value: "vehicle.retailPrice",
                },
                {
                    text: "Internet Price",
                    value: "vehicle.internetPrice",
                },
                {
                    text: "Enable Pricing",
                    value: "adjustment.enabled",
                },
                {
                    text: "Adjustment Amount",
                    value: "adjustmentAmount",
                },
                {
                    text: "Fixed Price Amount",
                    value: "fixedPriceAmount",
                },
                {
                    text: "Enable Fixed Pricing",
                    value: "enableFixedPricing",
                },
                {
                    text: "Sale Price",
                    value: "vehicle.price",
                },
                {
                    text: "Active",
                    value: "vehicle.activeStatus",
                },
            ],
            rules: { numericOnly, maxValue },
        };
    },
    computed: {
        totalPages: sync("usedVehiclePricing/pageMetadata@totalPages"),
        totalElements: sync("usedVehiclePricing/pageMetadata@totalElements"),
        pageSize: sync("usedVehiclePricing/pageMetadata@pageSize"),
        page: sync("usedVehiclePricing/pageable@page"),
        sort: get("usedVehiclePricing/pageable@sort"),
    },
    methods: {
        updateOptions(options) {
            let newSortBy = lodashGet(options, "sortBy[0]") || "";
            newSortBy = newSortBy.slice(newSortBy.indexOf(".") + 1, newSortBy.length);
            const newSortDesc = lodashGet(options, "sortDesc[0]") || false;

            if (newSortBy === this.sortBy && newSortBy === "") {
                return;
            }
            if (newSortBy !== this.sortBy || newSortDesc !== this.sortDesc) {
                if (newSortBy === "" || newSortDesc === "") {
                    EventBus.$emit("search", `year,desc`);
                } else {
                    const direction = newSortDesc ? "desc" : "asc";
                    EventBus.$emit("search", `${newSortBy},${direction}`);
                }
            }
        },
        change(index, path, value) {
            const newValue = {
                ...this.cars[index],
            };
            newValue.adjustment.amount = value;
            this.$set(newValue, path, value);

            this.$emit("change", index, newValue);
        },

        fixedPriceBlur({ vehicle, adjustment }) {
            const fixedPriceInvalid =
                isNil(adjustment.fixedPriceAmount) ||
                lodashIsNaN(adjustment.fixedPriceAmount) ||
                adjustment.fixedPriceAmount <= 0;
            if (fixedPriceInvalid) {
                adjustment.fixedPrice = false;
                adjustment.fixedPriceAmount = null;
            } else {
                const fixedPricedLessThanHalfOfInternetPrice =
                    vehicle.internetPrice && adjustment.fixedPriceAmount / vehicle.internetPrice < 0.5;
                if (fixedPricedLessThanHalfOfInternetPrice) {
                    EventBus.$emit("low-fixed-price-warning-modal");
                }
            }
        },
    },
};
</script>
