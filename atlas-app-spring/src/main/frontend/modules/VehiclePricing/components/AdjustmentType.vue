<template>
    <v-select
        :value="adjustment.type"
        :items="adjustmentTypeOptions"
        :disabled="!adjustment.enabled || adjustment.fixedPrice"
        class="adjustment-type"
        v-bind="$attrs"
        v-on="$listeners"
    />
</template>

<script>
export default {
    props: {
        adjustment: {
            type: Object,
            required: true,
        },
        adjustmentTypeOptions: {
            type: Array,
            required: true,
        },
    },
};
</script>

<style lang="scss" scoped>
.adjustment-type {
    max-width: 58px;
}
</style>
