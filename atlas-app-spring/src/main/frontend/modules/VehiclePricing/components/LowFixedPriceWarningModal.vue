<template>
    <v-dialog v-model="show" width="500">
        <v-card>
            <v-card-title class="headline"> WARNING - LOW FIXED PRICE </v-card-title>
            <v-card-text>
                <p>
                    The fixed price amount you just entered seems low, please verify that the amount entered is correct.
                </p>
                <p>
                    <strong>NOTE:</strong> This amount is NOT an adjustment value, it will be actual price of the
                    vehicle.
                </p>
            </v-card-text>
            <v-card-actions>
                <v-spacer />
                <v-btn color="primary" @click="show = false">Close</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script>
import EventBus from "@/util/eventBus";

export default {
    name: "LowFixedPriceWarningModal",

    data() {
        return {
            show: false,
        };
    },

    created() {
        EventBus.$on("low-fixed-price-warning-modal", () => (this.show = true));
    },
};
</script>
