<template>
    <v-select
        :value="adjustment.adjustedValue"
        :items="adjustedValueOptions"
        :disabled="!adjustment.enabled || adjustment.fixedPrice"
        class="adjusted-value"
        v-bind="$attrs"
        v-on="$listeners"
    />
</template>

<script>
export default {
    props: {
        adjustment: {
            type: Object,
            required: true,
        },
        adjustedValueOptions: {
            type: Array,
            required: true,
        },
    },
};
</script>

<style lang="scss" scoped>
.adjusted-value {
    max-width: 100px;
}
</style>
