import Vue from "vue";
import VueRouter from "vue-router";
import { configureRouter, layout, route, routerOptions } from "Util/routerHelper";

Vue.use(VueRouter);

const PATH_PREFIX = "/pricing";

const routes = [
    layout("Pricing", [
        route("VehiclePricing", "NewVehiclePricing", null, PATH_PREFIX + "/new"),
        route("VehiclePricing", "UsedVehiclePricing", null, PATH_PREFIX + "/used"),
    ]),
];

const router = new VueRouter({
    mode: "history",
    routes,
    ...routerOptions,
});

configureRouter(router);

export default router;
