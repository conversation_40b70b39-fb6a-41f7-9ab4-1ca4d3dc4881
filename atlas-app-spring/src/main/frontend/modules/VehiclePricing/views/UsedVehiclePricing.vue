<template>
    <v-skeleton-loader v-if="loading" type="card,card,table"></v-skeleton-loader>
    <v-container v-else class="pricing-container" fluid>
        <low-fixed-price-warning-modal />
        <v-row>
            <v-col>
                <search-form :key="dealerIds" :dealer-ids="dealerIds" @search-completed="handleSearchCompleted" />
            </v-col>
        </v-row>
        <v-row v-if="vehicleSearchFailureMessage && vehicleSearchFailureMessage.length > 0" class="my-0">
            <v-col class="pb-0">
                <v-alert type="warning" class="mb-0"> {{ vehicleSearchFailureMessage }}</v-alert></v-col
            >
        </v-row>
        <v-row class="mt-0">
            <v-col cols="12" md="6" class="pr-md-0 pb-0 pb-md-3">
                <stock-type-price-adjustment :key="dealerIds" :adjustment-type-options="adjustmentTypeOptions" />
            </v-col>
            <v-col cols="12" md="6">
                <priced-widget
                    :key="dealerIds"
                    name="used-priced"
                    title="% of Used Vehicles Priced"
                    chart-description="Priced"
                    :dealer-id="dealer.id"
                />
            </v-col>
        </v-row>
        <v-row class="mt-0">
            <v-col>
                <v-card>
                    <v-toolbar flat>
                        <v-toolbar-title>Used Vehicle Pricing</v-toolbar-title>
                    </v-toolbar>

                    <v-divider></v-divider>
                    <used-priced-car-table
                        :key="dealerIds"
                        :loading="tableLoading"
                        :cars="renderedPricedCars"
                        :adjustment-type-options="adjustmentTypeOptions"
                        @change="carRowChanged"
                    />
                </v-card>
            </v-col>
        </v-row>
        <v-btn color="warning" fab large fixed right dark bottom @click.prevent="savePricingAdjustments"> Save </v-btn>
    </v-container>
</template>

<script>
import { call, get, sync } from "vuex-pathify";
import LowFixedPriceWarningModal from "@/modules/VehiclePricing/components/LowFixedPriceWarningModal";
import SearchForm from "@/modules/VehiclePricing/components/UsedVehiclePricing/UsedSearchForm";
import StockTypePriceAdjustment from "@/modules/VehiclePricing/components/UsedVehiclePricing/StockTypePriceAdjustment";
import UsedPricedCarTable from "@/modules/VehiclePricing/components/UsedVehiclePricing/UsedPricedCarTable";
import PricedWidget from "@/modules/VehiclePricing/components/PricedWidget";
import api from "Util/api";
import { sanitize } from "Util/sanitize";
import lodashMap from "lodash/map";

export default {
    name: "UsedVehiclePricing",
    components: {
        UsedPricedCarTable,
        PricedWidget,
        StockTypePriceAdjustment,
        LowFixedPriceWarningModal,
        SearchForm,
    },
    data() {
        return {
            submitting: false,
            pricedCarRows: [],
            metricsRefreshKey: 0,
            adjustmentTypeOptions: [
                { text: "$", value: "DOLLAR" },
                {
                    text: "%",
                    value: "PERCENTAGE",
                },
            ],
            stockType: "used",
            tableLoading: true,
        };
    },
    computed: {
        dealerIds() {
            return this.$route.query.dealerIds;
        },
        loading: sync("vehiclePricing/loading"),
        dealer: get("vehiclePricing/dealer"),
        ymm: get("vehiclePricing/ymm"),
        searchForm: sync("usedVehiclePricing/searchForm"),
        enforcePricingRules: sync("usedVehiclePricing/enforcePricingRules"),
        stockTypePriceAdjustment: sync("usedVehiclePricing/stockTypePriceAdjustment"),
        vehicleSearchFailureMessage: sync("usedVehiclePricing/vehicleSearchFailureMessage"),
        vehicleSearchAttempted: sync("usedVehiclePricing/vehicleSearchAttempted"),
        vehicleSearchFoundVehicle: sync("usedVehiclePricing/vehicleSearchFoundVehicle"),
        pageNumber: sync("usedVehiclePricing/pageMetadata@number"),
        pageSize: sync("usedVehiclePricing/pageMetadata@pageSize"),
        totalPages: sync("usedVehiclePricing/pageMetadata@totalPages"),
        totalElements: sync("usedVehiclePricing/pageMetadata@totalElements"),
        page: sync("usedVehiclePricing/pageable@page"),
        sort: sync("usedVehiclePricing/pageable@sort"),
        pricingForm() {
            return {
                searchForm: this.searchForm,
                stockTypePriceAdjustment: this.stockTypePriceAdjustment,
                pricedCarRows: this.pricedCarRows,
                dealerIds: this.dealer.id,
            };
        },
        renderedPricedCars() {
            return lodashMap(this.pricedCarRows, (pricedCar) => {
                return this.renderPricedCar(pricedCar);
            });
        },
    },
    watch: {
        dealerIds(_val) {
            this.onLoad();
        },
    },
    created() {
        this.onLoad();
    },
    methods: {
        onLoad() {
            const payload = {
                dealerId: this.dealerIds,
                stockType: this.stockType,
            };
            this.loadPage(payload);
        },
        loadPage: call("vehiclePricing/loadPage"),
        handlePricingSuccessfullyDeleted() {
            this.loading = true;
            this.submitting = true;
            setTimeout(() => {
                window.location.reload();
            }, 5000);
        },

        renderPricedCar(pricedCar) {
            const { adjustment: carAdjustment, vehicle } = pricedCar;

            if (carAdjustment.enabled) {
                const fixedPriceEnabledAndValid =
                    carAdjustment.fixedPrice &&
                    carAdjustment.fixedPriceAmount &&
                    !isNaN(carAdjustment.fixedPriceAmount);
                if (fixedPriceEnabledAndValid) {
                    this.addFixedPriceAdjustmentAmount(vehicle, carAdjustment);
                } else {
                    this.addVehiclePriceAndEffectiveAdjustmentAmount(vehicle, carAdjustment, carAdjustment, "CAR");
                }
            } else if (this.stockTypePriceAdjustment.adjustment.enabled) {
                this.addVehiclePriceAndEffectiveAdjustmentAmount(
                    vehicle,
                    carAdjustment,
                    this.stockTypePriceAdjustment.adjustment,
                    "STOCK_TYPE"
                );
            } else {
                // neither individual adjustment or stockType adjustment is enabled
                carAdjustment.amount = null;
                this.addVehiclePriceAndEffectiveAdjustmentAmount(vehicle, carAdjustment, carAdjustment, null);
            }

            return pricedCar;
        },

        addFixedPriceAdjustmentAmount(vehicle, carAdjustment) {
            vehicle.price = carAdjustment.fixedPriceAmount;
            carAdjustment.amount = carAdjustment.fixedPriceAmount - vehicle.internetPrice;

            this.addActiveStatus(vehicle, carAdjustment, "FIXED_CAR");
        },

        addVehiclePriceAndEffectiveAdjustmentAmount(
            vehicle,
            carAdjustment,
            effectiveAdjustment,
            effectiveAdjustmentType
        ) {
            carAdjustment.fixedPriceAmount = null;
            carAdjustment.type = effectiveAdjustment.type;
            carAdjustment.amount = effectiveAdjustment.amount;

            let price;
            if (carAdjustment.amount != null) {
                price =
                    carAdjustment.type === "PERCENTAGE"
                        ? Math.round(vehicle.internetPrice + vehicle.internetPrice * (carAdjustment.amount / 100))
                        : vehicle.internetPrice + carAdjustment.amount;
            }

            carAdjustment.amount = !price || price < 0 ? null : carAdjustment.amount;
            vehicle.price = !price || price < 0 ? null : price;

            this.addActiveStatus(vehicle, carAdjustment, effectiveAdjustmentType);
        },

        addActiveStatus(vehicle, effectiveCarAdjustment, effectiveAdjustmentType) {
            if (this.enforcePricingRules) {
                this.addActiveStatusForEnforcePricingRulesIsTrue(
                    vehicle,
                    effectiveCarAdjustment,
                    effectiveAdjustmentType
                );
            } else {
                vehicle.activeStatus = true;
                this.$set(vehicle, "activeStatusDesc", "Enforce Pricing Rules is turned OFF");
            }
        },
        addActiveStatusForEnforcePricingRulesIsTrue(vehicle, effectiveCarAdjustment, effectiveAdjustmentType) {
            let activeStatus, activeStatusDesc;

            if (!effectiveAdjustmentType || effectiveCarAdjustment.amount == null) {
                activeStatus = false;
                activeStatusDesc = "No adjustment set";
            } else if (effectiveCarAdjustment.amount >= 0) {
                activeStatus = false;
                activeStatusDesc = "Adjustment does not bring price below Internet Price";
            } else {
                activeStatus = true;
                activeStatusDesc =
                    "Active via " + effectiveAdjustmentType + " adjustment since it brings price below Internet Price";
            }

            vehicle.activeStatus = activeStatus;
            this.$set(vehicle, "activeStatusDesc", activeStatusDesc);
        },
        savePricingAdjustments() {
            this.submitting = true;
            this.searchForm = sanitize(this.searchForm, { deep: true });
            this.stockTypePriceAdjustment = sanitize(this.stockTypePriceAdjustment, { deep: true });
            this.pricedCarRows = this.pricedCarRows.map((row) => sanitize(row));
            api.post(`/stratus/vehicle-pricing/${this.stockType}`, this.pricingForm)
                .then((response) => {
                    this.$toast(
                        "Used Vehicle Pricing Rules Updated. Please give us a couple of minutes to recalculate your used vehicle prices."
                    );
                    this.handleSearchCompleted(response.data);
                    this.refreshMetrics();
                })
                .catch((error) => {
                    this.$toast.error("There was an error with your submission, price adjustments were not saved");
                    if (error.response.status === 400 && error.response.data && error.response.data.errors) {
                        console.error("validation errors encountered", error.response.data.errors);
                    } else {
                        console.error(error.response);
                    }
                    // TODO handle validation errors
                    this.submitting = false;
                });
        },

        refreshMetrics() {
            setTimeout(() => {
                this.metricsRefreshKey++;
                this.submitting = false;
            }, 5000);
        },

        handleSearchCompleted(searchResults) {
            this.enforcePricingRules = searchResults.enforcePricingRules;
            this.stockTypePriceAdjustment = searchResults.stockTypePriceAdjustment;
            this.pricedCarRows = searchResults.pricedCarRowsPage.content;
            this.vehicleSearchFoundVehicle = searchResults.vehicleSearchFoundVehicle;
            this.vehicleSearchAttempted = searchResults.vehicleSearchAttempted;
            this.vehicleSearchFailureMessage = searchResults.vehicleSearchFailureMessage;
            this.totalPages = searchResults.pricedCarRowsPage.totalPages;
            this.totalElements = searchResults.pricedCarRowsPage.totalElements;
            this.pageSize = searchResults.pricedCarRowsPage?.size || 20;
            this.page = searchResults.pricedCarRowsPage.number + 1; // accounts for 0 based paging from server
            this.tableLoading = false;
        },

        carRowChanged(index, car) {
            this.pricedCarRows[index] = car;
        },
    },
};
</script>
<style lang="scss">
.pricing-container {
    background-color: $gray-200;
}
</style>
