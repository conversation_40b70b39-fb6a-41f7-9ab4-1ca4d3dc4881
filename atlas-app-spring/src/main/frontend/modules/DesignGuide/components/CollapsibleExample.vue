<template>
    <v-expansion-panels accordion multiple>
        <Collapsible>
            <template #title> {{ title }} </template>
            <template #description>
                {{ description }}
            </template>
        </Collapsible>
    </v-expansion-panels>
</template>

<script>
import { defineComponent } from "vue";
import Collapsible from "Components/CollapsibleTypes/Collapsible";
export default defineComponent({
    name: "CollapsibleExample",
    components: {
        Collapsible,
    },
    props: {
        title: {
            type: String,
            default: "SubTitle",
        },
        description: {
            type: String,
            default: "Description",
        },
    },
});
</script>
