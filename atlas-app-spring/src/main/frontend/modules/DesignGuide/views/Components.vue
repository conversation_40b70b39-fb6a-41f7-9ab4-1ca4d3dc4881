<template>
    <v-container id="design-guide" fluid class="grey lighten-3 h-100">
        <h1>Components</h1>
        <hr class="mb-3" />

        <collapsible-controller-wrapper title="Collapsible Controller Wrapper">
            <template #body>
                <section class="flex-col-gap-12">
                    <v-expansion-panels v-model="panels" accordion multiple>
                        <Collapsible>
                            <template #title> Layout Element </template>
                            <template #description> List of all the Layout elements </template>
                            <template #body>
                                <v-expansion-panels class="design-guide-layout-items">
                                    <SubCollapsible
                                        v-for="layoutItem in layoutConfig"
                                        :key="layoutItem.key"
                                        class="mb-4"
                                    >
                                        <template #title>
                                            <div class="flex-col-gap-12">
                                                <h4 class="muted-text">{{ layoutItem.name }}</h4>
                                            </div>
                                        </template>
                                        <template #body>
                                            <div class="flex-col-gap-12">
                                                <component
                                                    :is="layoutItem.component"
                                                    :title="layoutItem.title"
                                                    :description="layoutItem.description"
                                                    :video="layoutItem.video"
                                                />
                                            </div>
                                        </template>
                                    </SubCollapsible>
                                </v-expansion-panels>
                            </template>
                        </Collapsible>
                    </v-expansion-panels>

                    <v-expansion-panels v-model="panels" accordion multiple>
                        <Collapsible>
                            <template #title> Form Input Builder Elements</template>
                            <template #description> List of all the form input elements </template>
                            <template #body>
                                <v-expansion-panels class="design-guide-form-items">
                                    <SubCollapsible
                                        v-for="inputItem in formInputBuilderConfig"
                                        :key="inputItem.key"
                                        class="mb-4"
                                    >
                                        <template #title>
                                            <div class="flex-col-gap-4">
                                                <h4>Type: {{ inputItem.type }}</h4>
                                                <h5 class="muted-text">{{ inputItem.name }}</h5>
                                            </div>
                                        </template>
                                        <template #body>
                                            <div class="flex-col-gap-12">
                                                <FormInput
                                                    :component="formatData(inputItem.upperLevelConfigs)"
                                                    :input="inputItem"
                                                />
                                            </div>
                                        </template>
                                    </SubCollapsible>
                                </v-expansion-panels>
                            </template>
                        </Collapsible>
                    </v-expansion-panels>
                </section>
            </template>
        </collapsible-controller-wrapper>
    </v-container>
</template>

<script>
import BreadcrumbBar from "Components/BreadcrumbBar";
import UpdateBtn from "Components/UpdateBtn.vue";
import Collapsible from "Components/CollapsibleTypes/Collapsible";
import SubCollapsible from "Components/CollapsibleTypes/SubCollapsible";
import ConfigMainContext from "@/layouts/layoutwithbreadcrumbs/ConfigMainContext";
import CollapsibleControllerWrapper from "Components/CollapsibleTypes/CollapsibleControllerWrapper/index.vue";
import SubCollapsibleExample from "../components/SubCollapsibleExample";
import CollapsibleExample from "../components/CollapsibleExample";
import { FORM_INPUT_BUILDER_CONFIG, LAYOUT_CONFIG } from "../config.js";
import { sync } from "vuex-pathify";
import FormInput from "Components/ConfigFormBuilder/components/FormInput.vue";
import { FORMAT_COMPONENT_DATA } from "Util/ConfigManager/components";

export default {
    name: "DesignGuide",
    components: {
        FormInput,
        CollapsibleControllerWrapper,
        SubCollapsible,
        UpdateBtn,
        BreadcrumbBar,
        Collapsible,
        ConfigMainContext,
        SubCollapsibleExample,
        CollapsibleExample,
    },
    data() {
        return {
            formInputBuilderConfig: FORM_INPUT_BUILDER_CONFIG,
            layoutConfig: LAYOUT_CONFIG,
            panels: [0, 1],
        };
    },
    computed: {
        expandAll: sync("pageConfigs/expandAll"),
    },
    watch: {
        expandAll: {
            handler(val) {
                if (val) {
                    this.panels = [0, 1];
                } else {
                    this.panels = [];
                }
            },
            immediate: true,
        },
    },
    created() {
        this.expandAll = true;
    },
    methods: {
        formatData(data) {
            return FORMAT_COMPONENT_DATA(data);
        },
    },
};
</script>

<style lang="scss">
#design-guide {
    padding-bottom: 40px;
    .muted-text {
        color: var(--grey-grey-darken-2, #616161);
    }
    .flex-col-gap-12 {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }
    .flex-col-gap-4 {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }
    .design-guide-layout-items,
    .design-guide-form-items {
        > div {
            width: 100% !important;
        }
    }
}
</style>
