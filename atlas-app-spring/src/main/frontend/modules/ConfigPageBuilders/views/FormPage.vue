<template>
    <config-form-builder v-if="loaded" />
    <loading v-else />
</template>
<script>
import { call, get, sync } from "vuex-pathify";
import ConfigFormBuilder from "Components/ConfigFormBuilder/index.vue";
import Loading from "Components/ConfigFormBuilder/components/Loading.vue";

export default {
    name: "FormPage",
    components: { ConfigFormBuilder, Loading },
    props: {
        dealerIds: {
            type: String,
            required: false,
            default: null,
        },
        page: {
            type: String,
            required: true,
        },
    },
    computed: {
        userId: get("loggedInUser/userId"),
        selectedDealerId: get("loggedInUser/selectedDealer@id"),
        builderLoader: get("pageConfigs/loader"),
        defaultsLoader: get("pageConfigs/form@loader"),
        loaded() {
            return this.builderLoader.isComplete && this.defaultsLoader.isComplete;
        },
        dealerId() {
            return this.dealerIds || this.selectedDealerId;
        },
    },
    watch: {
        dealerId: {
            handler(value) {
                if (value) {
                    this.init();
                }
            },
            immediate: true,
        },
    },
    methods: {
        clearFormData: call("pageConfigs/clearFormData"),
        fetchPageBuilder: call("pageConfigs/fetchPageBuilder"),
        fetchDefaults: call("pageConfigs/fetchDefaults"),
        initialFormData(payload) {
            this.clearFormData();
            this.fetchPageBuilder(payload);
            this.fetchDefaults(payload);
        },
        init() {
            const payload = {
                page: this.page,
                dealerId: this.dealerId,
                userId: this.userId,
            };

            this.initialFormData(payload);
        },
    },
};
</script>
<style lang="scss" scoped></style>
