import Vue from "vue";
import vuetify from "@/plugins/vuetify";
import router from "Modules/Login/router";
import App from "@/App";
import Vuex from "vuex";
import { createStore } from "@/store/common";
import loginPageStore from "@/store/loginPageStore";

Vue.use(Vuex);

const store = createStore({
    loginPageStore,
});

/* eslint-disable no-new */
new Vue({
    el: "#app",
    vuetify,
    router,
    store,
    render: (h) => h(App),
});
