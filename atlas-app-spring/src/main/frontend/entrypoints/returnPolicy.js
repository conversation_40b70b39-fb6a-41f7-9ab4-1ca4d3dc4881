import Vue from "vue";
import Vuex from "vuex";
import vuetify from "@/plugins/vuetify";
import atlas from "@/plugins/atlas";
import "es6-promise/auto";
import App from "@/App";
import router from "Modules/ReturnPolicy/router";
import CarSaverFormatters from "@carsaver/filters";
import { createStore } from "@/store/common";
import returnPolicy from "Modules/ReturnPolicy/store/returnPolicy";

import "@/directives";

Vue.use(CarSaverFormatters);
Vue.use(Vuex);
Vue.use(atlas);

const store = createStore({
    returnPolicy,
});

/* eslint-disable no-new */
let app = new Vue({
    el: "#app",
    store,
    router,
    vuetify,
    render: (h) => h(App),
});
