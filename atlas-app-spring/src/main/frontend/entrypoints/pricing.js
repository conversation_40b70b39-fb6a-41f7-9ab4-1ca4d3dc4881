import Vue from "vue";
import vuetify from "@/plugins/vuetify";
import atlas from "@/plugins/atlas";
import "es6-promise/auto";
import App from "@/App";
import CarSaverFilters from "@carsaver/filters";
import router from "Modules/VehiclePricing/router";
import store from "Modules/VehiclePricing/store";

import "@/directives";

Vue.use(CarSaverFilters);
Vue.use(atlas);

/* eslint-disable no-new */
new Vue({
    el: "#app",
    store,
    router,
    vuetify,
    render: (h) => h(App),
});
