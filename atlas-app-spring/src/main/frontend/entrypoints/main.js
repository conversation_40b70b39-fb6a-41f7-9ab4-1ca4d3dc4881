import Vue from "vue";
import Vuex from "vuex";
import vuetify from "@/plugins/vuetify";
import App from "@/App";
import router from "Modules/Home/router";
import CarSaverFormatters from "@carsaver/filters";
import { createStore } from "@/store/common";

Vue.use(Vuex);
Vue.use(CarSaverFormatters);

const store = createStore();

/* eslint-disable no-new */
new Vue({
    el: "#app",
    store,
    router,
    vuetify,
    render: (h) => h(App),
});
