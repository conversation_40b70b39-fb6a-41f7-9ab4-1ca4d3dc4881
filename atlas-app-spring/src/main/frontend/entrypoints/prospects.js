import Vue from "vue";
import Vuex from "vuex";
import vuetify from "@/plugins/vuetify";
import atlas from "@/plugins/atlas";
import "es6-promise/auto";
import App from "@/App";
import router from "Modules/Prospects/router";
import CarSaverFormatters from "@carsaver/filters";

// Import Stores used by this module
import { createStore } from "@/store/common";
import userDetails from "@/modules/Customers/store/userDetails";
import userProspects from "Modules/Prospects/store/userProspects";

import "@/directives";

Vue.use(Vuex);
Vue.use(atlas);
Vue.use(CarSaverFormatters);

const store = createStore({
    userProspects,
    userDetails,
});

/* eslint-disable no-new */
new Vue({
    el: "#app",
    store,
    router,
    vuetify,
    render: (h) => h(App),
});
