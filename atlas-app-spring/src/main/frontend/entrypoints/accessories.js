import Vue from "vue";
import Vuex from "vuex";
import vuetify from "@/plugins/vuetify";
import atlas from "@/plugins/atlas";
import "es6-promise/auto";
import App from "@/App";
import router from "Modules/Accessories/router";
import CarSaverFormatters from "@carsaver/filters";
import { createStore } from "@/store/common";
import accessoriesDealerStore from "Modules/Accessories/store/dealer";

import "@/directives";

Vue.use(CarSaverFormatters);
Vue.use(Vuex);
Vue.use(atlas);

const store = createStore({
    accessoriesDealerStore,
});

/* eslint-disable no-new */
let app = new Vue({
    el: "#app",
    store,
    router,
    vuetify,
    render: (h) => h(App),
});
