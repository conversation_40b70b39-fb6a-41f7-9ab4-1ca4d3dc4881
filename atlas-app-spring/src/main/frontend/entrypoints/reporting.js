import Vue from "vue";
import Vuex from "vuex";
import vuetify from "@/plugins/vuetify";
import atlas from "@/plugins/atlas";
import "es6-promise/auto";
import App from "@/App";
import storeHelper from "@/util/storeHelper";
import router from "Modules/Reporting/router";
import VueCurrencyFilter from "vue-currency-filter";
import CarSaverFormatters from "@carsaver/filters";

// Import Stores used by this module
import { createStore } from "@/store/common";
import reportingStore from "Modules/Reporting/store/reporting";
import dealerSearch from "Modules/Reporting/store/dealerSearch";
import ApexCharts from "vue-apexcharts";

import "@/directives";

Vue.use(Vuex);
Vue.use(atlas);
Vue.use(CarSaverFormatters);
Vue.use(VueCurrencyFilter, {
    symbol: "$",
    thousandsSeparator: ",",
    fractionCount: 0,
    fractionSeparator: ".",
    symbolPosition: "front",
    symbolSpacing: false,
});

Vue.use(ApexCharts);
Vue.component("Apexchart", ApexCharts);

const chartThemes = () => {
    window.Apex = {
        colors: ["#C3002F", "#808080", "#A60027", "#111111", "#535353", "#d3d3d3"],
    };
};

if (document.readyState === "complete" || document.readyState !== "loading") {
    chartThemes();
} else {
    document.addEventListener("DOMContentLoaded", (event) => {
        chartThemes();
    });
}

// const plugins = storeHelper.plugins("reportingStore");

const store = createStore({
    reportingStore,
    dealerSearch,
});

/* eslint-disable no-new */
new Vue({
    el: "#app",
    store,
    router,
    vuetify,
    render: (h) => h(App),
});
