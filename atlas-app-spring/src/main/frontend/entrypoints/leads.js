import Vue from "vue";
import Vuex from "vuex";
import vuetify from "@/plugins/vuetify";
import atlas from "@/plugins/atlas";
import "es6-promise/auto";
import App from "@/App";
import storeHelper from "@/util/storeHelper";
import router from "Modules/Leads/router";
import CarSaverFormatters from "@carsaver/filters";

// Import Stores used by this module
import { createStore } from "@/store/common";
import leadSearch from "Modules/Leads/store/leadSearch";
import VueMoment from "vue-moment";

import "@/directives";

Vue.use(CarSaverFormatters);
Vue.use(Vuex);
Vue.use(atlas);
Vue.use(VueMoment);

const plugins = storeHelper.plugins("leadSearch");

const store = createStore(
    {
        leadSearch,
    },
    plugins
);

/* eslint-disable no-new */
new Vue({
    el: "#app",
    store,
    router,
    vuetify,
    render: (h) => h(App),
});
