import Vue from "vue";
import Vuex from "vuex";
import vuetify from "@/plugins/vuetify";
import atlas from "@/plugins/atlas";
import "es6-promise/auto";
import App from "@/App";
import storeHelper from "@/util/storeHelper";
import router from "Modules/UserSelfService/router";
import CarSaverFormatters from "@carsaver/filters";
import { required } from "vee-validate/dist/rules";
import { ValidationProvider, ValidationObserver, setInteractionMode, extend } from "vee-validate";
// Import Stores used by this module
import { createStore } from "@/store/common";
import userStore from "Modules/UserSelfService/store/userStore";

import "@/directives";

Vue.use(Vuex);
Vue.use(atlas);
Vue.use(CarSaverFormatters);
Vue.component("ValidationProvider", ValidationProvider);
Vue.component("ValidationObserver", ValidationObserver);

const plugins = storeHelper.plugins("userStore");

const store = createStore(
    {
        userStore,
    },
    plugins
);

/* eslint-disable no-new */
new Vue({
    el: "#app",
    store,
    router,
    vuetify,
    render: (h) => h(App),
});

setInteractionMode("eager");

extend("required", {
    ...required,
    message: "{_field_} can not be empty",
});

extend("password", {
    params: ["target"],
    validate(value, { target }) {
        return value === target;
    },
    message: "Password confirmation does not match",
});
