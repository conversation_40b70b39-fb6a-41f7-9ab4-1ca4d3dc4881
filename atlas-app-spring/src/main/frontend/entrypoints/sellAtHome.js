import Vue from "vue";
import Vuex from "vuex";
import vuetify from "@/plugins/vuetify";
import atlas from "@/plugins/atlas";
import "es6-promise/auto";
import App from "@/App";
import router from "Modules/SellAtHome/router";
import CarSaverFormatters from "@carsaver/filters";
import { createStore } from "@/store/common";
import sellAtHome from "Modules/SellAtHome/store/sellAtHome";

import "@/directives";

Vue.use(CarSaverFormatters);
Vue.use(Vuex);
Vue.use(atlas);

const store = createStore({
    sellAtHome,
});

/* eslint-disable no-new */
let app = new Vue({
    el: "#app",
    store,
    router,
    vuetify,
    render: (h) => h(App),
});
