import Vue from "vue";
import Vuex from "vuex";
import vuetify from "@/plugins/vuetify";
import atlas from "@/plugins/atlas";
import "es6-promise/auto";
import App from "@/App";
import storeHelper from "@/util/storeHelper";
import router from "Modules/Warranty/router";
import LoadScript from "vue-plugin-load-script";
import VuetifyGoogleAutocomplete from "vuetify-google-autocomplete";
import VueMask from "v-mask";
import CarSaverFormatters from "@carsaver/filters";

// Import Stores used by this module
import { createStore } from "@/store/common";
import customerSearch from "Modules/Warranty/store/customerSearch";
import eRating from "Modules/Warranty/store/eRating";
import "Modules/Warranty/filters";
import "@/directives";

Vue.use(VueMask);
Vue.use(VuetifyGoogleAutocomplete, {
    apiKey: "AIzaSyAmeMzc4Bx3ogH6OT4oNZ_hy2ufznNTcwY",
});
Vue.use(LoadScript);
Vue.use(Vuex);
Vue.use(atlas);
Vue.use(CarSaverFormatters);

const plugins = storeHelper.plugins("customerSearch");

const store = createStore(
    {
        customerSearch,
        eRating,
    },
    plugins
);

/* eslint-disable no-new */
new Vue({
    el: "#app",
    store,
    router,
    vuetify,
    render: (h) => h(App),
});
