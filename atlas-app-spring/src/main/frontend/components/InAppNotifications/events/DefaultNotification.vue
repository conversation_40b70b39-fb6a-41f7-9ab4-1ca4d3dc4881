<template>
    <div>
        {{ notification.message }}
    </div>
</template>

<script>
import { defineComponent } from "vue";

export default defineComponent({
    name: "DefaultNotification",
    props: {
        notification: {
            type: Object,
            required: true,
        },
    },
});
</script>

<style lang="scss" scoped>
@import "~vuetify/src/styles/settings/_variables";
.notification {
    @media #{map-get($display-breakpoints, 'sm-and-down')} {
        font-size: 12px;
    }
}
</style>
