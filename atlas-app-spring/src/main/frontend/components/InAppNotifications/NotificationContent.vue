<template>
    <component :is="notificationComponent" :notification="notification" @click="$emit('click', $event)" />
</template>

<script>
import { getNotificationComponent } from "Components/InAppNotifications/constants";
import DealershipNotification from "Components/InAppNotifications/events/DealershipNotification";
import WaterServiceNotification from "Components/InAppNotifications/events/WaterServiceNotification";
import CoffeeServiceNotification from "Components/InAppNotifications/events/CoffeeServiceNotification";
import DefaultNotification from "Components/InAppNotifications/events/DefaultNotification";

export default {
    name: "NotificationMessage",
    components: {
        DealershipNotification,
        WaterServiceNotification,
        CoffeeServiceNotification,
        DefaultNotification,
    },
    props: {
        notification: {
            type: Object,
            required: true,
        },
    },
    computed: {
        notificationComponent() {
            return getNotificationComponent(this.notification.beverage);
        },
    },
};
</script>
