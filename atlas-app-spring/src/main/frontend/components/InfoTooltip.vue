<template>
    <v-tooltip v-model="show" :color="backgroundColor" top :max-width="maxWidth">
        <template #activator="{ on, attrs }">
            <v-icon v-bind="attrs" :color="color" :size="size" x class="tooltip-icon" @click="handleClick" v-on="on">
                {{ icon }}
            </v-icon>
        </template>
        <span>
            <slot />
        </span>
    </v-tooltip>
</template>
<script>
export default {
    props: {
        clickModifier: {
            type: String,
            required: false,
            default: "stop",
        },
        size: {
            type: [String, Number],
            required: false,
            default: 20,
        },
        maxWidth: {
            type: [String, Number],
            required: false,
            default: "auto",
        },
        icon: {
            type: [String],
            required: false,
            default: "mdi-information-outline",
        },
        color: {
            type: String,
            required: false,
            default: "#9e9e9e",
        },
        backgroundColor: {
            type: String,
            required: false,
            default: "#9e9e9e",
        },
    },
    data() {
        return {
            show: false,
        };
    },
    methods: {
        toggle() {
            this.show = !this.show;
        },
        handleClick(event) {
            const clickModifier =
                typeof this.clickModifier === "string" ? this.clickModifier.toLowerCase() : this.clickModifier;

            if (clickModifier === "prevent") {
                event.preventDefault();
            } else {
                event.stopPropagation();
            }

            this.toggle();
        },
    },
};
</script>
<style lang="scss" scoped>
.tooltip-icon {
    margin-right: 4px;
    cursor: cell;
}
</style>
