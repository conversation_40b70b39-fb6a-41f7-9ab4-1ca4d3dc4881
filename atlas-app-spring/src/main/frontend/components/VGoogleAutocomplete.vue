<template>
    <v-text-field :id="id" ref="textField" :value="autocompleteText" v-bind="$attrs" @focus="onFocus" />
</template>

<script>
export default {
    name: "VGoogleAutocomplete",
    props: {
        id: {
            type: String,
            required: true,
        },
        enableGeoLocation: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            autocomplete: null,
            geoLocateSet: false,
            autocompleteText: "",
            lastSelectedPlace: "",
        };
    },
    computed: {},
    created() {
        this.autocompleteText = this.value ? this.value : "";
        this.lastSelectedPlace = this.autocompleteText;
    },
    mounted() {
        this.vgaMapState = window.vgaMapState;
        this.setupGmapApi(this.setupGoogle);
    },
    methods: {
        onFocus() {
            this.geoLocate();
            this.$emit("focus");
        },

        geoLocate() {
            if (this.enableGeoLocation && !this.geoLocateSet) {
                if (navigator.geolocation) {
                    navigator.geolocation.getCurrentPosition((position) => {
                        const geolocation = {
                            lat: position.coords.latitude,
                            lng: position.coords.longitude,
                        };

                        this.setupGmapApi(() => {
                            const circle = new window.google.maps.Circle({
                                center: geolocation,
                                radius: position.coords.accuracy,
                            });
                            this.autocomplete.setBounds(circle.getBounds());
                            this.geoLocateSet = true;
                        });
                    });
                }
            }
        },

        setupGmapApi(callback) {
            if (Object.prototype.hasOwnProperty.call(window, "google")) {
                callback();
            } else {
                console.error("Google Maps API not loaded.");
            }
        },

        setupGoogle() {
            const options = {};

            if (this.types) {
                options.types = [this.types];
            }
        },
    },
};
</script>
