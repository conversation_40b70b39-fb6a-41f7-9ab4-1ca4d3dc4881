<template>
    <div class="hello">
        <h1>{{ msg }}</h1>
        <h2>{{ dataMsg.greeting + " " + dataMsg.name }}</h2>
        <p>
            For a guide and recipes on how to configure / customize this project,<br />
            check out the
            <a href="https://cli.vuejs.org" target="_blank" rel="noopener">vue-cli documentation</a>.
        </p>
    </div>
</template>

<script lang="ts">
import Vue from "vue";

interface ComplexMsg {
    name: string;
    greeting: string;
}

export default Vue.extend({
    name: "SampleTypescript",
    props: {
        msg: {
            type: String,
            default: "Hi There",
            required: false,
        },
    },
    data() {
        return {
            dataMsg: {
                name: "<PERSON>",
                greeting: "Hello there",
            } as ComplexMsg,
        };
    },
});
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
.hello {
    min-height: 200px;
}
h3 {
    margin: 40px 0 0;
}
</style>
