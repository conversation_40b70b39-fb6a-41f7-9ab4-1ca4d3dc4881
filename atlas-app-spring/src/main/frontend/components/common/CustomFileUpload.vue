<template>
    <div class="file-upload-wrapper">
        <div class="file-upload-container">
            <div v-if="imageFound">
                <a v-if="isImage" :href="previewURL" target="_blank" rel="noopener">
                    <img :src="previewURL" alt="Preview" class="preview-image" />
                </a>
                <div class="preview-image-meta-data">
                    <p class="info-text">{{ file.name }}</p>
                    <p class="info-text">{{ formattedFileSize }}</p>
                </div>
            </div>
            <div v-else>
                <v-icon large>mdi-file-upload-outline</v-icon>
                <p class="info-text">{{ uploadInstructions }}</p>
            </div>

            <input
                ref="fileUploadInputRef"
                type="file"
                :accept="acceptedFormats"
                style="display: none"
                @change="handleFileChange"
            />

            <v-btn v-if="imageFound" outlined large @click="removeFile('image', id)">
                <v-icon class="mr-2 delete-icon">mdi-delete-outline</v-icon> Remove File
            </v-btn>
            <v-btn v-else large @click="triggerFileInput">Upload File</v-btn>
        </div>
        <p v-if="moreFileSizeInfoFooter">
            We recommend 2x or 3x button height for image max height. File size will impact load time.
        </p>
        <div class="label mt-3 mb-3">Default Color</div>
        <InputColor
            id="image-background-color"
            :value="imageBackgroundColor"
            label="Background Color"
            placeholder="#FFFFFF"
            @change="handleChange($event, 'imageBackgroundColor', id + 'image-background-color')"
        />
    </div>
</template>

<script>
import { defineComponent } from "vue";
import InputColor from "Components/FormInputs/InputColor.vue";
export default defineComponent({
    name: "CustomFileUpload",
    components: { InputColor },
    props: {
        id: {
            type: String,
            default: "",
            required: false,
        },
        initialFile: {
            type: Object,
            default: () => ({
                url: null,
                name: null,
                size: null,
            }),
            required: false,
        },
        maxSize: {
            type: Number,
            default: 0.5 * 1024 * 1024, // Default max size is 500KB
            required: false,
        },
        acceptedFormats: {
            type: String,
            default: "image/jpeg, image/png", // Default accepted formats
            required: false,
        },
        uploadInstructions: {
            type: String,
            default: "JPG, PNG file type 500KB max file size", // Default upload instructions
            required: false,
        },
        moreFileSizeInfoFooter: {
            type: Boolean,
            default: true,
            required: false,
        },
        baseKeyPath: {
            type: String,
            default: "",
            required: false,
        },
        initialImageBackgroundColor: {
            type: String,
            default: "#FFFFFF",
            required: false,
        },
    },
    emits: ["file-selected", "file-removed", "image-background-color-change"],
    data() {
        return {
            file: null,
            previewURL: null,
            isImage: false,
            imageBackgroundColor: this.initialImageBackgroundColor,
        };
    },
    computed: {
        formattedFileSize() {
            if (!this.file) return "";

            // Check if file size is a string that contains "kb" (case insensitive)
            if (typeof this.file.size === "string" && this.file.size.toLowerCase().includes("kb")) {
                return this.file.size;
            }

            const fileSize = this.file.size;
            if (fileSize >= 1024 * 1024) {
                return (fileSize / (1024 * 1024)).toFixed(2) + " MB";
            } else {
                return (fileSize / 1024).toFixed(2) + " KB";
            }
        },
        imageFound() {
            if (this.file instanceof File && this.file.type.includes("image")) {
                return true;
            } else if (this.file instanceof Object && this.file.url) {
                return true;
            }
            return false;
        },
    },
    watch: {
        initialFile: {
            handler() {
                this.initFileHandler();
            },
            immediate: true,
        },
    },
    methods: {
        initFileHandler() {
            if (this.initialFile) {
                this.file = this.initialFile;

                if (this.file instanceof File && this.file.type.includes("image")) {
                    this.isImage = true;
                    this.previewURL = URL.createObjectURL(this.file);
                } else if (this.file instanceof Object && this.file.url) {
                    this.isImage = true;
                    this.previewURL = this.file.url;
                }
            }
        },
        async handleFileChange(event) {
            const selectedFile = event.target.files[0];
            if (selectedFile) {
                if (selectedFile.size > this.maxSize) {
                    alert(`File size exceeds ${this.maxSize / (1024 * 1024)}MB limit.`);
                    return;
                }

                if (!this.isFormatAccepted(selectedFile.type)) {
                    alert("Invalid file format.");
                    return;
                }

                const isImageCorrupt = await this.checkImageCorruption(selectedFile);

                if (isImageCorrupt.isCorrupt) {
                    alert(isImageCorrupt.message);
                    return;
                }

                this.file = selectedFile;
                if (selectedFile.type.includes("image")) {
                    this.isImage = true;
                    this.previewURL = URL.createObjectURL(selectedFile);
                } else {
                    this.isImage = false;
                    this.previewURL = null;
                }
                this.$emit("file-selected", this.file);
                this.handleChange(selectedFile.name, "imageName", this.id + "image-name");
                this.handleChange(selectedFile.size, "imageSize", this.id + "image-size");
                this.handleChange(this.previewURL, "imageUrl", this.id + "image-url");
            }
            event.target.value = null;
        },
        handleChange(value, property, key) {
            this.$emit("change", { value, property, key });
        },
        checkImageCorruption(file) {
            return new Promise((resolve) => {
                const reader = new FileReader();
                reader.onload = function (event) {
                    const img = new Image();
                    img.onload = () => {
                        // Image loaded successfully, it's likely not corrupt in a basic sense
                        resolve({ isCorrupt: false, message: "Image is valid." });
                    };
                    img.onerror = () => {
                        // Error loading the image data, could be corrupt or unsupported format
                        resolve({ isCorrupt: true, message: "Image is corrupt or cannot be loaded." });
                    };
                    // Corrected line: Assign the loaded data URL to the image source
                    img.src = event.target.result;
                };

                reader.onerror = () => {
                    // Error during the file reading process itself
                    resolve({ isCorrupt: true, message: "Error reading the file." });
                };
                // Start reading the file as a Data URL
                reader.readAsDataURL(file);
            });
        },
        isFormatAccepted(fileType) {
            const acceptedFormats = this.acceptedFormats.split(",").map((format) => format.trim());
            return acceptedFormats.includes(fileType);
        },
        removeFile(property, key) {
            this.file = null;
            this.previewURL = null;
            this.isImage = false;
            this.$emit("file-removed", { value: this.file, property, key });
        },
        triggerFileInput() {
            this.$refs.fileUploadInputRef.click();
        },
    },
});
</script>

<style scoped lang="scss">
.file-upload-wrapper {
    .delete-icon {
        font-size: 18px;
    }
    .label {
        font-size: px2rem(14);
        font-weight: 600;
    }
    p {
        margin: 0;
    }
    .info-text {
        font-size: px2rem(14);
        line-height: 20px;
        color: #535353;
    }
    .file-upload-container {
        margin-top: 16px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 16px;

        .preview-image {
            max-width: 36px;
            max-height: 36px;
        }

        .preview-image-meta-data {
            display: flex;
            flex-direction: column;
        }

        & > div {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        & > i {
            width: 36px;
            height: 36px;
        }
    }
    & > p {
        margin-top: 16px;
        font-size: px2rem(14);
        font-style: italic;
        color: var(--grey-grey-darken-2, #616161);
    }
}
</style>
