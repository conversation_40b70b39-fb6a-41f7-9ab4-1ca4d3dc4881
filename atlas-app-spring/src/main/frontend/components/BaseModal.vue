<template>
    <v-dialog v-model="localValue" :max-width="maxWidth" :persistent="persistent">
        <v-card class="base-modal-card pa-6">
            <!-- Header -->
            <v-card-title class="header-container d-flex align-start pa-0 mb-0">
                <div class="title-container">
                    <slot name="title">
                        <h3 v-if="title" class="modal-title text-h6">{{ title }}</h3>
                    </slot>
                    <slot name="subtitle">
                        <p v-if="subtitle" class="modal-subtitle">{{ subtitle }}</p>
                    </slot>
                </div>
                <v-spacer />
                <v-btn v-if="!hideClose" class="close-btn" icon text small @click="localValue = false">
                    <v-icon>mdi-close</v-icon>
                </v-btn>
            </v-card-title>

            <!-- Content -->
            <v-card-text class="px-0">
                <slot />
            </v-card-text>

            <!-- Actions -->
            <v-card-actions v-if="$slots.actions" class="pa-0">
                <slot name="actions" />
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script lang="ts">
import Vue from "vue";

export default Vue.extend({
    name: "BaseModal",

    props: {
        value: {
            type: Boolean,
            required: true,
        },
        title: {
            type: String,
            default: "",
        },
        subtitle: {
            type: String,
            default: "",
        },
        maxWidth: {
            type: [String, Number],
            default: 600,
        },
        minHeight: {
            type: [String, Number],
            default: null,
        },
        persistent: {
            type: Boolean,
            default: false,
        },
        hideClose: {
            type: Boolean,
            default: false,
        },
    },

    computed: {
        localValue: {
            get(): boolean {
                return this.value;
            },
            set(value: boolean): void {
                this.$emit("input", value);
            },
        },
    },
});
</script>

<style lang="scss" scoped>
.close-btn {
    margin-right: -3px;
}

.title-container {
    line-height: 1.3;
    padding-bottom: 16px;
}

.modal-title {
    font-size: px2rem(20);
    margin-bottom: 3px;
}

.modal-subtitle {
    font-size: px2rem(14);
    font-weight: normal;
}
</style>
