<template>
    <div v-if="showMainImage" class="deal-image">
        <img :src="src" v-bind="$attrs" :alt="alt" @error="handleMainImageError" />
    </div>

    <div v-else class="deal-image">
        <img class="vcard-image" src="@/assets/errors/vehicle-img-fallback.svg" alt="" />
    </div>
</template>
<script>
import lodashIsNil from "lodash/isNil";
export default {
    name: "VehicleImage",
    inheritAttrs: false,

    props: {
        alt: {
            type: String,
            required: false,
            default: "vehicle image",
        },
        src: {
            type: String,
            required: false,
            default: null,
        },
    },

    data() {
        return {
            mainImageHasError: false,
        };
    },

    computed: {
        showMainImage() {
            return !lodashIsNil(this.src) && !this.mainImageHasError;
        },
    },

    methods: {
        handleMainImageError() {
            this.mainImageHasError = true;
        },
    },
};
</script>
<style lang="scss">
.deal-image {
    text-align: center;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;

    img {
        width: 100%;
    }
}
</style>
