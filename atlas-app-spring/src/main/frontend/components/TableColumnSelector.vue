<template>
    <v-dialog v-model="dialog" width="500">
        <template #activator="{ on, attrs }">
            <v-btn icon v-bind="attrs" v-on="on">
                <v-icon>mdi-apps</v-icon>
            </v-btn>
        </template>

        <v-card>
            <v-card-title class="headline grey lighten-2"> Show/Hide Fields </v-card-title>

            <v-list dense>
                <v-list-item-group v-model="selected" multiple>
                    <template v-for="field in toggleFields">
                        <v-list-item
                            :key="fieldKey(field)"
                            :value="fieldKey(field)"
                            active-class="blue--text text--accent-4"
                        >
                            <template #default="{ active }">
                                <v-list-item-action>
                                    <v-checkbox :input-value="active" />
                                </v-list-item-action>
                                <v-list-item-content>
                                    <v-list-item-title>
                                        {{ fieldName(field) }}
                                    </v-list-item-title>
                                </v-list-item-content>
                            </template>
                        </v-list-item>
                    </template>
                </v-list-item-group>
            </v-list>

            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn color="primary" text @click="dialog = false"> Ok </v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>
<script>
export default {
    name: "TableColumnSelector",
    props: {
        id: {
            type: String,
            default: "table-column-selector",
        },
        fields: {
            type: Array,
            required: true,
        },
        value: {
            type: Array,
            default() {
                return [];
            },
        },
    },
    data() {
        return {
            dialog: false,
            selected: [],
        };
    },
    computed: {
        toggleFields() {
            let toggleFields = [];

            _.forEach(this.fields, (field) => {
                // Prevents actions column from being hidden
                if (field.value !== "actions") {
                    toggleFields.push(field);
                }
            });

            return toggleFields;
        },
    },

    watch: {
        selected() {
            this.$emit("input", this.selected);
        },
    },

    mounted() {
        this.selected = this.value;
    },

    methods: {
        fieldKey(field) {
            if (_.isObject(field)) {
                return _.get(field, "value");
            }

            return field;
        },
        fieldName(field) {
            if (_.isObject(field)) {
                const label = _.get(field, "text", _.startCase(_.get(field, "value")));
                if (label !== "") {
                    return label;
                } else {
                    return _.startCase(_.get(field, "key"));
                }
            }

            return _.startCase(field);
        },
    },
};
</script>
