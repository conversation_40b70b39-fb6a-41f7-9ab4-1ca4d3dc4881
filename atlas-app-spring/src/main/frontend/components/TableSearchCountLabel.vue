<template>
    <v-card elevation="0">
        <v-card-subtitle>Showing {{ startNumber }} to {{ endNumber }} of {{ total }}</v-card-subtitle>
    </v-card>
</template>

<script>
export default {
    name: "TableSearchCountLabel",
    props: {
        pageNumber: {
            type: Number,
            required: true,
        },
        pageSize: {
            type: Number,
            required: true,
        },
        totalElements: {
            type: Number,
            required: true,
        },
    },
    computed: {
        startNumber() {
            if (this.totalElements <= 0) {
                return "0";
            }

            if (_.isNil(this.pageNumber)) {
                return "-";
            }
            return (this.pageNumber + 1) * this.pageSize + 1 - this.pageSize;
        },
        endNumber() {
            if (_.isNil(this.pageNumber)) {
                return "-";
            }
            const endNumber = (this.pageNumber + 1) * this.pageSize;

            if (endNumber > this.totalElements) {
                return this.totalElements;
            }

            return endNumber;
        },
        total() {
            const total = this.totalElements;
            if (total === null) {
                return 0;
            }
            return total;
        },
    },
};
</script>
