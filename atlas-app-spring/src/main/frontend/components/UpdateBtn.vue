<template>
    <v-btn color="primary" large class="white--text update-btn" :loading="loading" @click="$emit('clicked')">
        Update
    </v-btn>
</template>
<script>
export default {
    name: "UpdateBtn",
    props: {
        loading: {
            type: Boolean,
            required: false,
            default: false,
        },
    },
    emits: ["clicked"],
    computed: {},
    created() {},
    methods: {},
};
</script>
<style lang="scss" scoped>
.update-btn {
    width: 150px;
}
</style>
