<template>
    <v-card class="breadcrumb-bar d-flex justify-space-between align-center pr-3">
        <v-breadcrumbs :items="items" large></v-breadcrumbs>
        <slot name="actionArea" />
    </v-card>
</template>
<script>
export default {
    name: "BreadcrumbBar",
    props: {
        items: {
            type: Array,
            required: false,
            default: () => [
                {
                    text: "Dashboard",
                    disabled: false,
                    href: "breadcrumbs_dashboard",
                },
                {
                    text: "Link 1",
                    disabled: false,
                    href: "breadcrumbs_link_1",
                },
                {
                    text: "Link 2",
                    disabled: true,
                    href: "breadcrumbs_link_2",
                },
            ],
        },
    },
    data: () => ({
        testItems: [
            {
                text: "Dashboard",
                disabled: false,
                href: "breadcrumbs_dashboard",
            },
            {
                text: "Link 1",
                disabled: false,
                href: "breadcrumbs_link_1",
            },
            {
                text: "Link 2",
                disabled: true,
                href: "breadcrumbs_link_2",
            },
        ],
    }),
    computed: {},
    created() {},
    methods: {},
};
</script>
<style lang="scss">
@import "~vuetify/src/styles/settings/_variables";
.breadcrumb-bar {
    .v-breadcrumbs--large {
        li {
            @media #{map-get($display-breakpoints, 'sm-and-down')} {
                font-size: px2rem(14) !important;
            }
        }
    }
}
</style>
