<template>
    <v-list-item>
        <v-list-item-content>
            <v-list-item-title>
                <v-menu
                    v-model="isMenuOpen"
                    :close-on-content-click="false"
                    transition="scale-transition"
                    :nudge-left="150"
                    offset-y
                    min-width="auto"
                >
                    <template #activator="{ on, attrs }">
                        <v-text-field
                            :id="facetName"
                            :ref="facetName"
                            v-model="dateRangeFormatted"
                            hint="MM/DD/YYYY format"
                            class="facet-input"
                            :label="facetLabel"
                            prepend-icon="mdi-calendar"
                            readonly
                            clearable
                            v-bind="attrs"
                            v-on="on"
                            @click:clear="clearFilter(facetName)"
                        ></v-text-field>
                    </template>
                    <v-date-picker
                        v-model="dateRange"
                        :allowed-dates="allowedDates"
                        range
                        @input="isMenuOpen = !(dateRange && dateRange.length === 2)"
                    ></v-date-picker>
                </v-menu>
            </v-list-item-title>
        </v-list-item-content>
    </v-list-item>
</template>

<script>
import { FACET_TYPE } from "Util/searchUtils";
import moment from "moment";
import _ from "lodash";

export default {
    name: "FacetDatePickerRange",

    props: {
        store: {
            type: String,
            required: true,
        },
        facetLabel: {
            type: String,
            required: true,
        },
        facetName: {
            type: String,
            required: true,
        },
        filterName: {
            type: String,
            required: true,
        },
        facetType: {
            type: String,
            required: false,
            default: FACET_TYPE.MULTIPLE.toString(),
        },
    },
    data() {
        return {
            hover: {},
            isMenuOpen: false,
            dateRange: null,
            dateRangeFormatted: null,
        };
    },
    computed: {
        filters() {
            const getterName = `${this.store}/getFiltersByName`;
            const f = this.$store.getters[getterName](this.filterName);
            const type = this.facetType;

            if (_.isNil(f)) {
                if (type === FACET_TYPE.MULTIPLE.toString()) {
                    return [];
                }
                return null;
            }

            if (type === FACET_TYPE.MULTIPLE.toString() && !_.isArray(f)) {
                return [f];
            }

            return f;
        },
        clearDateBtn() {
            if (this.dateRangeFormatted === null || this.dateRangeFormatted.length < 1) {
                return false;
            } else {
                return true;
            }
        },
    },
    watch: {
        dateRange(val) {
            let newDateRange = [];
            const formatDateFunction = this.formatDate;

            _.forEach(val, function (value) {
                newDateRange.push(formatDateFunction(value));
            });

            this.dateRangeFormatted = newDateRange;
            if (this.dateRangeFormatted && this.dateRangeFormatted.length === 2) {
                const filter = {
                    filterName: this.facetName,
                    facetId: this.dateRangeFormatted,
                };
                this.addPositiveFilter(filter);
            }
        },
    },
    created() {
        this.setFacetFiltered();
    },
    methods: {
        allowedDates: (val) => moment(val).isBefore(moment()),
        setFacetFiltered() {
            if (this.filters && this.filters.length > 0) {
                this.dateRangeFormatted = this.filters;
            }
        },
        addPositiveFilter(filter) {
            return this.$store.dispatch(`${this.store}/addPositiveFilter`, filter);
        },
        addNegativeFilter(filter) {
            return this.$store.dispatch(`${this.store}/addNegativeFilter`, filter);
        },
        removeFilter() {
            const filter = {
                filterName: this.facetName,
                facetId: null,
            };
            return this.$store.dispatch(`${this.store}/removeFilter`, filter);
        },
        clearFilter(filter) {
            this.dateRangeFormatted = null;
            this.dateRange = null;
            return this.$store.dispatch(`${this.store}/clearFilter`, filter);
        },
        formatDate(date) {
            if (!date) return null;

            const [year, month, day] = date.split("-");
            return `${month}/${day}/${year}`;
        },
    },
};
</script>

<style lang="scss">
.facet-input {
    width: 100%;
}
.facet {
    width: 100%;
    font-size: 14px;
}
.facet-count {
    font-size: 12px;
}
.v-application--is-ltr .v-list-group--no-action > .v-list-group__items > .v-list-item {
    padding-left: 45px;
}
</style>
