<template>
    <v-checkbox
        :id="facetId(facet)"
        :ref="facetId(facet)"
        class="facet-input"
        dense
        :input-value="isChecked(facet)"
        @click.prevent.stop="onCheck(facet)"
    >
        <template #label :for="facetId(facet)">
            <a
                class="facet d-flex justify-content-between"
                @click.stop.prevent="positiveStart(facet)"
                @mouseover="showHoverText(facet)"
                @mouseleave="hideHoverText(facet)"
            >
                <div class="facet-name mr-auto text--primary text-capitalize">
                    {{ formattedFacetName(facet.name) }}
                </div>
                &nbsp;
                <div class="right">
                    <span v-if="hoverText(facet) !== null" class="hover-text">
                        {{ hoverText(facet) }}
                    </span>
                    <span v-if="showCount" class="facet-count">
                        <span>({{ determineCountToDisplay(facet) }})</span>
                    </span>
                </div>
            </a>
        </template>
    </v-checkbox>
</template>

<script>
import Vue, { defineComponent } from "vue";
import FacetListSubGroup from "Components/Facets/FacetListSubGroup";
import lodashGet from "lodash/get";
import lodashSize from "lodash/size";
import lodashIsNil from "lodash/isNil";
import lodashIndexOf from "lodash/indexOf";
import { FACET_TYPE, snakeToTitleCase } from "Util/searchUtils";
import { renameDisplayValue } from "@/util/renameUtils";
import lodashCapitalize from "lodash/capitalize";

export default defineComponent({
    name: "FacetCheckboxListItem",
    extends: FacetListSubGroup,
    props: {
        facet: {
            type: Object,
            required: false,
            default: () => ({}),
        },
        facetType: {
            type: String,
            required: false,
            default: FACET_TYPE.MULTIPLE.toString(),
        },
    },
    data() {
        return {
            hover: {},
        };
    },

    computed: {
        facets() {
            const getterName = `${this.store}/getFacetsByName`;
            console.log(getterName, this.facetName);
            return this.$store.getters[getterName](this.facetName);
        },
    },

    methods: {
        showHoverText(facet) {
            Vue.set(this.hover, facet.id, true);
        },
        hideHoverText(facet) {
            Vue.set(this.hover, facet.id, false);
        },
        hoverText(facet) {
            const showHover = lodashGet(this.hover, facet.id, false);

            if (!showHover) {
                return null;
            }

            if (this.isPositiveSearchMethod) {
                if (this.isChecked(facet) && lodashSize(this.filters) === 1) {
                    return "All";
                }
            }

            return "Only";
        },
        isChecked(facet) {
            if (lodashIsNil(this.filters) || this.filters.length === 0) {
                return true;
            }

            return (
                (this.isFacetFiltered(facet) && this.isPositiveSearchMethod) ||
                (!this.isFacetFiltered(facet) && this.isNegativeSearchMethod)
            );
        },

        isFacetFiltered(facet) {
            return lodashIndexOf(this.filters, facet.id) !== -1;
        },

        onCheck(facet) {
            if (this.isNegativeSearchMethod) {
                if (!this.isChecked(facet)) {
                    this.removeFilter({
                        filterName: this.filterName,
                        facetId: facet.id,
                    });
                } else {
                    this.addNegativeFilter({
                        filterName: this.filterName,
                        facetId: facet.id,
                        facetType: FACET_TYPE.MULTIPLE,
                    });
                }
            } else {
                if (this.isChecked(facet)) {
                    this.removeFilter({
                        filterName: this.filterName,
                        facetId: facet.id,
                    });
                } else {
                    this.addPositiveFilter({
                        filterName: this.filterName,
                        facetId: facet.id,
                        facetType: FACET_TYPE.MULTIPLE,
                    });
                }
            }
        },

        positiveStart(facet) {
            if (this.isPositiveSearchMethod) {
                if (this.isFacetFiltered(facet) && this.filters.length === 1) {
                    this.clearFilter(this.filterName);
                } else {
                    this.addPositiveFilter({
                        filterName: this.filterName,
                        facetId: facet.id,
                        facetType: FACET_TYPE.MULTIPLE,
                        clear: true,
                    });
                }
            } else {
                this.addPositiveFilter({
                    filterName: this.filterName,
                    facetId: facet.id,
                    facetType: FACET_TYPE.MULTIPLE,
                    clear: true,
                });
            }
        },
        formattedFacetName(name) {
            if (this.facetName === "tradePaymentTypes") {
                return snakeToTitleCase(name);
            }
            if (this.facetName === "status") {
                return lodashCapitalize(name);
            }
            return renameDisplayValue(name);
        },
    },
});
</script>

<style scoped lang="scss"></style>
