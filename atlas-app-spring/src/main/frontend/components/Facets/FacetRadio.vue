<template>
    <facet-list-sub-group v-bind="$props" class="facet-list-sub-group__facet-radio">
        <template slot-scope="{ facet }">
            <v-radio-group :value="checked(facet)" class="facet-radio-group" dense @change="onCheck(facet)">
                <v-radio :id="facetId(facet)" :name="filterName" :value="facet.id" :active-class="facetId(facet)">
                    <template #label>
                        <div class="facet d-flex justify-content-between">
                            <span class="facet-name mr-auto text--primary">
                                {{ facet.name }}
                            </span>
                            &nbsp;
                            <span v-if="showCount" class="facet-count">
                                <span>({{ facet.count }})</span>
                            </span>
                        </div>
                    </template>
                </v-radio>
            </v-radio-group>
        </template>
    </facet-list-sub-group>
</template>

<script>
import FacetListSubGroup from "Components/Facets/FacetListSubGroup";
import { FACET_TYPE } from "Util/searchUtils";

export default {
    name: "FacetRadio",
    components: { FacetListSubGroup },
    extends: FacetListSubGroup,
    props: {
        facetType: {
            type: String,
            required: false,
            default: FACET_TYPE.SINGLE.toString(),
        },
    },

    methods: {
        checked(facet) {
            if (this.filters === facet.id) {
                return facet.id;
            }
            return null;
        },
        onCheck(facet) {
            this.addPositiveFilter({
                filterName: this.filterName,
                facetId: facet.id,
                facetType: FACET_TYPE.SINGLE,
            });
        },
    },
};
</script>
<style lang="scss">
.facet-list-sub-group__facet-radio {
    .facet-radio-group {
        width: 100%;
        margin: 0;
    }
}
</style>
