<template>
    <facet-list-sub-group v-bind="$props">
        <slot slot="actions" name="actions" />
        <template slot-scope="{ facet }">
            <FacetCheckboxListItem :facet="facet" v-bind="$props" />
        </template>
    </facet-list-sub-group>
</template>
<script>
import FacetListSubGroup from "Components/Facets/FacetListSubGroup";
import FacetCheckboxListItem from "Components/Facets/FacetCheckboxListItem.vue";

export default {
    name: "FacetCheckbox",
    components: { FacetCheckboxListItem, FacetListSubGroup },
    extends: FacetListSubGroup,
};
</script>
