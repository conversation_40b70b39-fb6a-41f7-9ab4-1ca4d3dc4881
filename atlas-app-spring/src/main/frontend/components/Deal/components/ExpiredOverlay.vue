<template>
    <div class="expired-overlay d-flex justify-center align-center">
        <v-alert v-show="true" dense outlined type="error">{{ message }}</v-alert>
    </div>
</template>
<script>
export default {
    name: "ExpiredOverlay",
    props: {
        message: {
            type: String,
            required: true,
        },
    },
};
</script>
<style lang="scss">
.expired-overlay {
    width: 100%;
    height: 100%;
    position: absolute;
    background: #fff;
    opacity: 0.8;
    z-index: 2;
}
</style>
