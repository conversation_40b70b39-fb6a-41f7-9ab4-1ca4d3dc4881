<template>
    <v-card outlined class="mb-8">
        <v-card-title>Discrepancy</v-card-title>
        <v-card-text>
            <v-form ref="discrepancyEditorForm" v-model="discrepancyEditorValid" lazy-validation>
                <v-text-field
                    v-model="discrepancy.description"
                    label="Description"
                    required
                    counter
                    outlined
                    :rules="discrepancyValidationRules.description"
                />

                <v-textarea
                    v-model="discrepancy.notes"
                    label="Notes"
                    required
                    counter
                    outlined
                    :rules="discrepancyValidationRules.notes"
                />
            </v-form>
        </v-card-text>
        <v-card-actions>
            <v-btn color="blue darken-1" text @click="cancelEditing"> Cancel </v-btn>
            <v-btn color="primary" @click="saveDiscrepancy"> Save </v-btn>
        </v-card-actions>
    </v-card>
</template>

<script>
import api from "Util/api";

export default {
    name: "DiscrepancyEditor",
    props: {
        initialDiscrepancy: {
            type: Object,
            required: false,
            default: function () {
                return {
                    description: "",
                    notes: "",
                };
            },
        },
        tradeVehicleId: {
            type: String,
            required: true,
        },
        dealerId: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            discrepancy: { ...this.initialDiscrepancy },
            discrepancyEditorValid: true,
            discrepancyValidationRules: {
                description: [
                    (v) => !!v || "Description is required",
                    (v) => v.length <= 50 || "Maximum of 50 characters",
                ],
                notes: [(v) => !!v || "Notes are required", (v) => v.length <= 500 || "Maximum of 500 characters"],
            },
        };
    },
    methods: {
        cancelEditing() {
            this.$emit("cancelled");
        },
        persistDiscrepancy() {
            const apiEndpoint = `/dealer/${this.dealerId}/users/trades/${this.tradeVehicleId}/discrepancy`;

            return api.patch(apiEndpoint, this.discrepancy);
        },
        saveDiscrepancy() {
            const valid = this.$refs.discrepancyEditorForm.validate();
            if (valid) {
                this.persistDiscrepancy().then((response) => {
                    this.$emit("saved", response.data);
                });
            }
        },
    },
};
</script>

<style scoped></style>
