<template>
    <v-card class="d-flex pa-2" outlined tile>
        <div>
            <span class="font-weight-bold">{{ linkDescription }} </span>
            <br />
            <span class="font-weight-light">{{ linkValue }}</span>
        </div>
    </v-card>
</template>

<script>
export default {
    name: "ConsumerLink",
    props: {
        linkValue: {
            type: String,
            require: false,
            default: "http://localhost-nissan-upgrade:4000/deal/93930",
        },
    },
    data() {
        return {
            linkDescription: "To direct the consumer to this deal, the following link can be sent via text or email:",
        };
    },
};
</script>
