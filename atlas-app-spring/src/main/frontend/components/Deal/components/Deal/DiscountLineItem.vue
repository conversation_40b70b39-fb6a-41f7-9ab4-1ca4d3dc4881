<template>
    <div v-if="showDiscount" class="d-flex justify-space-between align-center">
        <div class="label">
            Discount
            <info-tooltip v-if="showDiscount">{{ tooltipDescription }}</info-tooltip>
        </div>
        <div class="value">- {{ discount | numeral("$0,0.00") }}</div>
    </div>
</template>

<script>
import InfoTooltip from "Components/InfoTooltip";

export default {
    name: "SalePriceLineItem",
    components: { InfoTooltip },
    props: {
        discount: {
            type: Number,
            require: true,
            default: 0,
        },
        showDiscount: {
            type: Boolean,
            require: false,
            default: true,
        },
    },
    data() {
        return {
            tooltipDescription: "Discount is the difference between the MSRP and Sale Price",
        };
    },
};
</script>
