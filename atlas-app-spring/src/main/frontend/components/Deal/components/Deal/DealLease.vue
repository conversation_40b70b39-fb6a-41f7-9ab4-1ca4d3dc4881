<template>
    <v-skeleton-loader v-if="isLoading" type="table"></v-skeleton-loader>
    <div v-else id="deal">
        <div class="pa-3 pt-md-4 pb-md-3 px-md-4 shade-box">
            <div v-if="selectedDeal.preQualificationDate" class="disclosure">Based on finance programs from:</div>
            <div class="title font-weight-bold mb-2">
                {{ selectedDeal.financierName }}
            </div>

            <v-row>
                <v-col cols="12" md="6">
                    <div class="d-flex justify-space-between align-center mb-1">
                        <div class="label">Status</div>
                        <div class="value">
                            {{ selectedDeal.status }}
                        </div>
                    </div>
                    <div class="d-flex justify-space-between align-center mb-1">
                        <div class="label">Finance Type</div>
                        <div class="value">
                            {{ selectedDeal.financeType }}
                        </div>
                    </div>
                    <div v-if="selectedDeal.creditEvaluator" class="d-flex justify-space-between align-center mb-1">
                        <div class="label">Credit Source</div>
                        <div class="value">
                            {{ source }}
                        </div>
                    </div>
                    <div v-if="selectedDeal.preApproval" class="d-flex justify-space-between align-baseline mb-1">
                        <div class="label">
                            <strong><u>Pre-Approval</u></strong>
                        </div>
                        <div class="value">
                            <strong>
                                <u>{{ preApprovalFinancierName }}</u>
                            </strong>
                        </div>
                    </div>
                </v-col>
                <v-col cols="12" md="6">
                    <div v-if="selectedDeal.applicationDate" class="d-flex justify-space-between align-center mb-1">
                        <div class="label">Application Date</div>
                        <div class="value">
                            {{ selectedDeal.applicationDate | formatEpochDate }}
                        </div>
                    </div>
                    <div
                        v-else-if="selectedDeal.preQualificationDate"
                        class="d-flex justify-space-between align-center mb-1"
                    >
                        <div class="label">Pre-qualification Date</div>
                        <div class="value">
                            {{ selectedDeal.preQualificationDate | formatEpochDate }}
                        </div>
                    </div>

                    <div
                        v-if="selectedDeal.lenderTier && hasCustomerReadCreditInfo"
                        class="d-flex justify-space-between align-center mb-1"
                    >
                        <div class="label">Lender Tier</div>
                        <div class="value">
                            {{ selectedDeal.lenderTier }}
                            <small v-if="selectedDeal.selfSelectedCreditTier">(Self-Selected)</small>
                        </div>
                    </div>

                    <div
                        v-if="hasCustomerReadCreditInfo && softPullFailureReason"
                        class="d-flex justify-space-between align-center mb-1"
                    >
                        <div class="label">Self-Select Credit Reason</div>
                        <div class="value">
                            {{ selfSelectedCreditReason }}
                        </div>
                    </div>

                    <div class="d-flex justify-space-between align-center">
                        <div class="label">Application Number</div>
                        <div class="value">
                            {{ selectedDeal.applicationNumber }}
                        </div>
                    </div>
                </v-col>
            </v-row>
        </div>

        <div class="pa-3 pa-md-4">
            <v-row class="mb-3">
                <v-col cols="12" :md="6" class="left-col">
                    <div v-if="showMsrpBlock">
                        <div class="d-flex justify-space-between align-center mb-1">
                            <div class="label">MSRP</div>
                            <div class="value">
                                {{ selectedDeal.msrp | numeral("$0,0.00") }}
                            </div>
                        </div>
                        <discount-line-item :discount="selectedDeal.discount"></discount-line-item>
                        <div class="divider mt-2" />
                    </div>

                    <div class="d-flex justify-space-between align-baseline mb-2">
                        <div class="sale-line-item d-flex justify-space-between align-center mb-2">
                            <div class="label max font-weight-bold">Purchase Price</div>
                        </div>
                        <div v-if="editingEnabled" class="value">
                            <v-text-field
                                v-model="selectedDeal.salePrice"
                                prefix="$"
                                required
                                dense
                                outlined
                                :rules="[rules.required, rules.validAmount]"
                            />
                        </div>
                        <div v-else class="label max font-weight-bold">
                            {{ selectedDeal.salePrice | numeral("$0,0.00") }}
                        </div>
                    </div>

                    <div class="d-flex flex-column gap-1 mb-2 mt-5">
                        <dealer-fees
                            v-if="hasCapCostDealerFee"
                            :dealer-fees="selectedDeal.capCostDealerFees"
                            :stock-type="stockType"
                            :editing-enabled="editingEnabled"
                            @addDealerFee="addDealerFee"
                            @removeDealerFee="removeFee"
                        />

                        <div v-if="selectedDeal.acquisitionFee" class="d-flex justify-space-between align-baseline">
                            <div class="label">Acquisition Fee</div>
                            <div class="value">+ {{ selectedDeal.acquisitionFee | numeral("$0,0.00") }}</div>
                        </div>

                        <deal-selected-accessories-details :selected-accessories-deal-details="selectedAccessories" />
                        <selected-protection-products :protection-products="selectedProtectionProducts" />

                        <div v-if="selectedDeal.netCashOffer < 0" class="d-flex justify-space-between align-baseline">
                            <div class="label">Trade Balance</div>

                            <div class="value">
                                +
                                {{ selectedDeal.netCashOffer | abs | numeral("$0,0.00") }}
                            </div>
                        </div>

                        <!--                        <div v-if="estimatedLeaseBalance > 0" class="d-flex justify-space-between align-center">-->
                        <!--                            <div class="label">Estimated Lease Balance Owed</div>-->
                        <!--                            <div class="value">+ {{ estimatedLeaseBalance | numeral("$0,0.00") }}</div>-->
                        <!--                        </div>-->

                        <div class="d-flex justify-space-between align-baseline">
                            <div class="label">Sales Tax</div>
                            <div class="value">+ {{ selectedDeal.taxOnCapCost | numeral("$0,0.00") }}</div>
                        </div>
                    </div>

                    <div class="divider" />

                    <div class="d-flex justify-space-between align-center mb-7">
                        <div class="label max font-weight-bold">Gross Capitalized Cost</div>
                        <div class="label max font-weight-bold">
                            {{ selectedDeal.grossCapCost | numeral("$0,0.00") }}
                        </div>
                    </div>

                    <div v-if="showMsrpBlock" class="d-flex flex-column gap-half mb-2">
                        <div class="d-flex justify-space-between align-center">
                            <div class="label">Rebates</div>
                            <div class="value">- {{ selectedDeal.rebates | numeral("$0,0.00") }}</div>
                        </div>

                        <external-offers-summary :external-offers="externalOffers" />
                    </div>

                    <div class="d-flex justify-space-between align-center mt-7 mb-3">
                        <div class="label max font-weight-bold">Adjusted Capitalized Cost</div>
                        <div class="label max font-weight-bold">
                            {{ adjustedTotalAmountFinanced | numeral("$0,0.00") }}
                        </div>
                    </div>

                    <div class="d-flex flex-column gap-1 mt-7">
                        <div class="d-flex justify-space-between align-baseline">
                            <div class="label">Monthly Payment</div>
                            <div class="value">
                                {{ selectedDeal.monthlyPaymentWithFeesAndTaxes | numeral("$0,0.00") }}
                            </div>
                        </div>
                        <div class="d-flex justify-space-between align-baseline">
                            <div class="label">Total Lease Payments</div>
                            <div class="value">{{ selectedDeal.totalPayments | numeral("$0,0.00") }}</div>
                        </div>
                        <div class="d-flex justify-space-between align-baseline">
                            <div class="label">Purchase Option at End of Term</div>
                            <div class="value">{{ endOfTermPurchaseOptionAmount }}</div>
                        </div>
                    </div>
                </v-col>
                <v-col cols="12" md="6">
                    <div class="d-flex flex-column gap-1">
                        <div class="d-flex justify-space-between align-baseline">
                            <div class="label">Length of Term</div>
                            <div v-if="editingEnabled" class="value">
                                <v-select
                                    v-model="selectedDeal.termLength"
                                    :items="terms"
                                    class="value"
                                    item-text="text"
                                    item-value="value"
                                    required
                                    dense
                                    outlined
                                />
                            </div>
                            <div v-else class="value">{{ selectedDeal.termLength }} Months</div>
                        </div>
                        <div class="d-flex justify-space-between align-baseline">
                            <div class="label">Annual mileage allowance</div>
                            <div class="value">
                                {{ selectedDeal.annualMileageAllowance | numeral("0,0") }}
                            </div>
                        </div>
                        <div class="d-flex justify-space-between align-baseline">
                            <div class="label">Money Factor</div>
                            <div class="value">
                                {{ selectedDeal.moneyFactor | numeral("0,0.0000") }}
                            </div>
                        </div>
                        <div class="d-flex justify-space-between align-baseline">
                            <div class="label">Residual</div>
                            <div class="value">{{ selectedDeal.residualPct | numeral("0,0") }}%</div>
                        </div>
                        <div class="d-flex justify-space-between align-center">
                            <div class="label">Monthly Tax</div>
                            <div class="value">
                                {{ selectedDeal.monthlyTaxes | numeral("$0,0.00") }}
                                <span>/ mo</span>
                            </div>
                        </div>
                        <div class="d-flex justify-space-between align-center">
                            <div class="label">Mileage Penalty</div>
                            <div class="value">{{ selectedDeal.mileagePenalty ?? 0 | numeral("$0,0.00") }} / mi</div>
                        </div>

                        <div class="d-flex justify-space-between align-center">
                            <div class="label">Security Deposit</div>
                            <div class="value">
                                {{ selectedDeal.securityDeposit | numeral("$0,0.00") }}
                            </div>
                        </div>

                        <div class="d-flex justify-space-between align-center">
                            <div class="label">Disposition Fee</div>
                            <div class="value">
                                {{ selectedDeal.dispositionFee ?? 0 | numeral("$0,0.00") }}
                            </div>
                        </div>
                    </div>

                    <div class="d-flex flex-column gap-1 mt-8">
                        <div class="d-flex justify-space-between align-baseline">
                            <div class="label">First Month Payment</div>
                            <div class="value">
                                {{ selectedDeal.monthlyPaymentWithFeesAndTaxes | numeral("$0,0.00") }}
                            </div>
                        </div>
                        <div class="d-flex justify-space-between align-center">
                            <div class="label">License/Registration</div>
                            <div class="value">{{ selectedDeal.registration | numeral("$0,0.00") }}</div>
                        </div>
                        <dealer-fees
                            :dealer-fees="selectedDeal.inceptionDealerFees"
                            :stock-type="stockType"
                            :editing-enabled="editingEnabled"
                            no-heading
                            @addDealerFee="addDealerFee"
                            @removeDealerFee="removeFee"
                        />

                        <div class="d-flex justify-space-between align-baseline">
                            <div class="label">Sales Tax</div>
                            <div class="value">{{ selectedDeal.taxOnInception | numeral("$0,0.00") }}</div>
                        </div>
                    </div>

                    <div class="divider" />

                    <div class="d-flex justify-space-between align-center">
                        <div class="label max font-weight-bold">Inception Fees</div>
                        <div class="label max font-weight-bold">
                            {{ selectedDeal.inceptionsTotal | numeral("$0,0.00") }}
                        </div>
                    </div>

                    <div class="d-flex flex-column gap-1 mt-8">
                        <div v-if="selectedDeal.netCashOffer > 0" class="d-flex justify-space-between align-baseline">
                            <div class="label">Positive Trade Equity</div>
                            <div class="value">${{ selectedDeal.netCashOffer | numeral("0,0") }}</div>
                        </div>

                        <div>
                            <reservation-down-payment
                                v-if="hasReservationDeposit"
                                key="lease_reservation-deposit-applied"
                                :down-payment="selectedDeal.downPayment"
                                :reservation-deposit="reservationDeposit"
                            />

                            <div
                                v-else
                                key="lease_down_payment_applied"
                                class="d-flex justify-space-between align-baseline"
                            >
                                <div class="label">Consumer Cash</div>
                                <div v-if="editingEnabled" key="lease-deal-editing-down-payment" class="value">
                                    <v-text-field
                                        v-model="selectedDeal.downPayment"
                                        prefix="$"
                                        required
                                        dense
                                        outlined
                                        :rules="[rules.required, rules.validAmount]"
                                    />
                                </div>
                                <div v-else key="lease-down-payment" class="value">
                                    {{ selectedDeal.downPayment | numeral("$0,0.00") }}
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-space-between align-baseline">
                            <div class="label">Total Due At Signing</div>
                            <div class="value">{{ selectedDeal.totalDueAtSigning | numeral("$0,0.00") }}</div>
                        </div>
                    </div>

                    <div v-if="selectedDeal.preApproval" class="d-flex justify-space-between align-baseline mt-16 pl-4">
                        <preapproval-info
                            :tier="preApprovalTierBump"
                            :amount="preApprovalPayment"
                            :source="preApprovalFinancierName"
                            :expiration="preApprovalExpirationDate"
                        />
                    </div>
                    <div class="d-flex justify-space-between align-baseline mt-16 pl-4">
                        <consumer-link v-if="editingEnabled" :link-value="selectedDeal.dealURL" />
                    </div>
                </v-col>
            </v-row>
            <v-row v-if="editingEnabled">
                <v-col cols="12">
                    <v-btn class="mr-4" :disabled="!dealChanged" @click="saveDeal"> Update Deal </v-btn>
                    <v-btn @click="resetDeal"> reset </v-btn>
                </v-col>
            </v-row>
            <v-row>
                <v-col cols="12">
                    <div class="disclosure">
                        Sale price subject to change without notice.
                        <span v-if="tradeExpirationDate">
                            Trade offer good through
                            {{ tradeExpirationDate }}.
                        </span>
                        Final terms subject to approval by the dealership.
                    </div>
                </v-col>
            </v-row>
        </div>
    </div>
</template>
<script>
import api from "@/util/api";
import _ from "lodash";
import ExternalOffersSummary from "Components/Deal/components/Deal/ExternalOffersSummary";
import DealerFees from "Components/Deal/components/Deal/DealerFees";
import DiscountLineItem from "Components/Deal/components/Deal/DiscountLineItem";
import EventBus from "Util/eventBus";
import ConsumerLink from "Components/Deal/components/Deal/ConsumerLink";
import formRules from "Util/formRules";
import PreapprovalInfo from "Components/Deal/components/Deal/PreapprovalInfo";
import DealSelectedAccessoriesDetails from "./DealSelectedAccessoriesDetails.vue";
import ReservationDownPayment from "Components/Deal/components/Deal/ReservationDownPayment";
import SelectedProtectionProducts from "Components/Deal/components/Deal/SelectedProtectionProducts.vue";

export default {
    name: "DealLease",
    components: {
        DealSelectedAccessoriesDetails,
        DealerFees,
        ExternalOffersSummary,
        DiscountLineItem,
        ConsumerLink,
        PreapprovalInfo,
        ReservationDownPayment,
        SelectedProtectionProducts,
    },
    props: {
        deal: {
            type: Object,
            required: true,
        },
        trade: {
            type: Object,
            required: false,
            default: null,
        },
        stockType: {
            type: String,
            required: true,
        },
        contractRequested: {
            type: Boolean,
            required: false,
            default: false,
        },
        selectedAccessories: {
            type: Object,
            required: false,
            default: null,
        },
        selectedProtectionProducts: {
            type: Object,
            required: false,
            default: null,
        },
    },
    data() {
        return {
            formatting_options: {
                style: "currency",
                currency: "USD",
                minimumFractionDigits: 2,
            },
            expirationData: null,
            selectedDeal: null,
            dealChanged: false,
            editingEnabled: true,
            isLoading: false,
            rules: formRules,
            terms: [
                { text: "12 Months", value: "12" },
                { text: "24 Months", value: "24" },
                { text: "36 Months", value: "36" },
                { text: "48 Months", value: "48" },
                { text: "60 Months", value: "60" },
                { text: "72 Months", value: "72" },
                { text: "84 Months", value: "84" },
            ],
        };
    },
    computed: {
        showMsrpBlock() {
            return this.stockType === "NEW";
        },
        endOfTermPurchaseOptionAmount() {
            const noneApplicable = "N/A";
            const endOfTermPurchaseOption = this.selectedDeal.endOfTermPurchaseOption;
            const endOfTermPurchaseOptionAmountNumeral = endOfTermPurchaseOption.toLocaleString(
                "en-US",
                this.formatting_options
            );
            if (endOfTermPurchaseOption === 0.0) {
                return noneApplicable;
            } else {
                return endOfTermPurchaseOptionAmountNumeral;
            }
        },

        tradeExpirationDate() {
            return _.get(this.expirationData, "tradeExpirationDate", null);
        },

        remainingPaymentsSum() {
            const payments = _.get(this.trade, "remainingPayments", 0);
            const payment = _.get(this.trade, "monthlyPayment", 0);

            return payment * payments;
        },

        estimatedLeaseBalance() {
            return this.remainingPaymentsSum;
        },

        leaseFirstMonthlyPayment() {
            const outOfPocket = _.get(this.selectedDeal, "outOfPocket", 0);
            const downPayment = _.get(this.selectedDeal, "downPayment", 0);

            return outOfPocket - downPayment;
        },
        downPayment() {
            const outOfPocket = _.get(this.selectedDeal, "outOfPocket", 0);

            return outOfPocket - this.leaseFirstMonthlyPayment;
        },
        externalOffers() {
            return _.get(this.selectedDeal, "externalOffers", null);
        },
        creditSource() {
            return _.get(this.selectedDeal, "creditEvaluator");
        },
        source() {
            if (this.creditSource === "CUSTOMER") {
                return "Customer provided";
            } else {
                return "Bureau provided";
            }
        },
        dealId() {
            return !_.get(this.selectedDeal, "certificateId")
                ? _.get(this.deal, "certificateId")
                : _.get(this.selectedDeal, "certificateId");
        },
        dealerId() {
            return this.$route.params.dealerId;
        },
        hasCustomerReadCreditInfo() {
            return this.$acl.hasDealerPermission(this.dealerId, "customer:soft-pull:read");
        },
        softPullFailureReason() {
            return _.get(this.selectedDeal, "softPullFailureReason");
        },
        selfSelectedCreditReason() {
            switch (this.softPullFailureReason) {
                case "REPORT_LOCKED_BY_CONSUMER":
                    return "Report locked by consumer";
                case "REPORT_NOT_FOUND":
                    return "Report not found";
                case "MAXIMUM_ATTEMPTS_EXCEEDED":
                    return "Max attempts exceeded";
                case "REPORT_FROZEN_BY_FED":
                    return "Report Frozen by Feds";
                case "INSUFFICIENT_TRADE_LINE":
                    return "No Credit History";
                case "INVALID_USER":
                    return "User Not Found";
                case "INVALID_SCORE":
                    return "Invalid credit score";
                default:
                    return "Unknown Error";
            }
        },
        preApprovalFinancierName() {
            return _.get(this.selectedDeal, "preApproval.financierName", null);
        },
        preApprovalTierBump() {
            return _.get(this.selectedDeal, "preApproval.tierBump", null);
        },
        preApprovalPayment() {
            return _.get(this.selectedDeal, "preApproval.monthlyPayment", null);
        },
        preApprovalExpirationDate() {
            return _.get(this.selectedDeal, "preApproval.preApprovalDate", null);
        },
        selectedAccessoriesTotal() {
            return _.get(this.selectedAccessories, "total", 0) || 0;
        },
        selectedProtectionProductsTotal() {
            const total = _.get(this.selectedProtectionProducts, "total", 0);
            return total;
        },
        adjustedPurchasePrice() {
            let purchasePrice = _.get(this.selectedDeal, "purchasePrice", 0) || 0;
            let adjustedPurchasePrice =
                purchasePrice + this.selectedAccessoriesTotal + this.selectedProtectionProductsTotal;

            return adjustedPurchasePrice;
        },
        adjustedTotalAmountFinanced() {
            let totalAmountFinanced = _.get(this.selectedDeal, "totalAmountFinance", 0) || 0;
            let adjustedTotalAmountFinanced = totalAmountFinanced + this.selectedAccessoriesTotal;

            return adjustedTotalAmountFinanced;
        },
        reservationDeposit() {
            return _.get(this.deal, "reservationDeposit", 0) || 0;
        },
        hasReservationDeposit() {
            return this.reservationDeposit > 0;
        },
        hasCapCostDealerFee() {
            return this.selectedDeal.capCostDealerFees?.length > 0;
        },
    },
    watch: {
        selectedDeal: {
            deep: true,
            handler(val) {
                this.dealChanged = !_.isEqual(val, this.deal);
            },
        },
    },
    created() {
        this.fetchExpirationData();
        this.cloneSelectedDeal();
        if (this.contractRequested === true || !this.selectedDeal.modifiedByDealer) {
            this.editingEnabled = false;
        }
    },
    methods: {
        saveDeal() {
            this.updateDeal();
        },
        resetDeal() {
            if (this.dealChanged) {
                this.cloneSelectedDeal();
            }
        },
        cloneSelectedDeal() {
            this.selectedDeal = _.cloneDeep(this.deal);
        },
        fetchExpirationData() {
            return api
                .get(`/deal/${this.dealId}/expiration`)
                .then((response) => {
                    this.expirationData = response.data;
                })
                .catch((error) => {
                    console.error(error);
                });
        },

        updateDeal() {
            this.isLoading = true;
            this.selectedDeal.downPayment = this.selectedDeal.outOfPocket;
            return api
                .patch(`/deal/${this.dealId}`, this.selectedDeal)
                .then((response) => {
                    this.selectedDeal = response.data.deal;
                    EventBus.$emit("refresh-deals");
                    this.$emit("open", this.dealId);
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
        addDealerFee(fee) {
            console.log(fee);
            this.selectedDeal.dealerFees.push(fee);
        },
        removeFee(fee) {
            this.selectedDeal.dealerFees.splice(this.selectedDeal.dealerFees.indexOf(fee), 1);
        },
        addDealerRebate(fee) {
            this.selectedDeal.dealerRebates.push(fee);
        },
        removeRebate(fee) {
            this.selectedDeal.dealerRebates.splice(this.selectedDeal.dealerRebates.indexOf(fee), 1);
        },
    },
};
</script>
<style lang="scss">
#deal {
    .v-text-field__slot {
        input {
            text-align: right;
        }
    }
    .value .v-input__slot {
        min-height: 32px;
    }
    .shade-box {
        background-color: $gray-200;
        .disclosure {
            color: $gray-700;
            font-size: 10px;
            font-style: oblique;
            line-height: 12px;
        }
    }
    .title {
        color: $gray-800;
        font-size: 20px;
    }
    .label {
        color: $gray-800;
        font-size: 14px;
    }
    .label.max {
        font-size: 16px;
    }
    .value {
        color: $gray-600;
        font-size: 14px;
    }
    .sale-line-item {
        margin-bottom: px2rem(35);
    }
    .divider {
        height: 1px;
        width: 100%;
        background-color: $gray-300;
        margin: px2rem(2) 0 px2rem(10) 0;
    }
    .left-col {
        border-right: 1px solid $gray-300;
    }
    .disclosure {
        color: $gray-600;
        font-size: 12px;
        font-style: oblique;
    }
    .gap-1 {
        gap: 4px;
    }
    .gap-half {
        gap: 2px;
    }
}
</style>
