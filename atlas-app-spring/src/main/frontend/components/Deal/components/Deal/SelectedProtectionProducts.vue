<template>
    <div v-if="hasProtectionProducts">
        <div class="d-flex justify-space-between align-center mb-1">
            <div class="label">Protection Products</div>
            <div v-if="!flag" class="value">+{{ total | numeral("$0,0.00") }}</div>
        </div>

        <div class="ml-2 d-flex justify-space-between align-center mb-1"></div>

        <div class="ml-2 label mb-1 text-decoration-underline accessories-flag-label" @click="flag = !flag">
            {{ flag ? "Hide protection products" : "Show protection products" }}
        </div>
        <div v-if="flag" class="transition-fast-in-fast-out mb-3">
            <div
                v-for="(item, index) in valueList"
                :key="index"
                class="ml-2 d-flex justify-space-between align-center mb-1"
            >
                <div class="label">{{ item.productName }}</div>
                <div class="value">+{{ item.premium | numeral("$0,0.00") }}</div>
            </div>
        </div>
    </div>
</template>

<script>
import _ from "lodash";
export default {
    name: "SelectedProtectionProducts",
    props: {
        protectionProducts: {
            type: Object,
            required: false,
            default: null,
        },
    },
    data() {
        return {
            selectedValues: null,
            flag: false,
        };
    },
    computed: {
        total() {
            const total = _.get(this.protectionProducts, "total", 0);
            return total;
        },
        valueList() {
            const list = _.get(this.protectionProducts, "insuranceWarranties", []);
            return list;
        },
        hasProtectionProducts() {
            return this.protectionProducts && this.valueList && this.valueList.length > 0 && this.total > 0;
        },
    },
};
</script>

<style scoped>
.accessories-flag-label {
    color: #007dc6 !important;
    cursor: pointer;
}
</style>
