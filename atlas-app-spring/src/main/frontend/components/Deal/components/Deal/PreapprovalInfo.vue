<template>
    <v-card class="pa-4 container shade-box" tile>
        <div>
            <small>The customer is pre-approved with</small>
            <br />
            <span class="font-weight-bold">{{ source }}</span>
        </div>
        <div class="pt-3">
            <div class="d-flex justify-space-between">
                <span>Credit Tier:</span>
                <span class="font-weight-light">Tier {{ tier }}</span>
            </div>
            <div class="d-flex justify-space-between">
                <span>Payment Amount:</span>
                <span class="font-weight-light">{{ amount | numeral("$0,0.00") }}</span>
            </div>
            <div class="d-flex justify-space-between">
                <span>Expiration Date:</span>
                <span class="font-weight-light">{{ expiration }}</span>
            </div>
        </div>
    </v-card>
</template>

<script>
export default {
    name: "ConsumerLink",
    props: {
        tier: {
            type: String,
            require: false,
            default: null,
        },
        amount: {
            type: Number,
            require: false,
            default: null,
        },
        source: {
            type: String,
            require: false,
            default: null,
        },
        expiration: {
            type: String,
            require: false,
            default: null,
        },
    },
};
</script>
<style lang="scss" scoped>
.container {
    max-width: 500px;
    box-shadow: none !important;
    text-align: center;
    z-index: 1;
}
</style>
