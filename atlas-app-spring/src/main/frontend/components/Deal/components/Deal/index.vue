<template>
    <section-template>
        <template #section-top> {{ deal.createdAt | formatEpochDate }} </template>
        <template #section-title> {{ deal.dealName }} </template>
        <template #section-content>
            <expired-overlay v-if="!isPreApprovalValid" message="Pre-approval Status has expired" />

            <deal-lease
                v-if="isLeaseDeal"
                :deal="deal"
                :trade="trade"
                :stock-type="stockType"
                :contract-requested="contractRequested"
                :selected-accessories="selectedAccessories"
                :selected-protection-products="selectedProtectionProducts"
                class="mb-3"
            />
            <deal-finance
                v-else-if="isFinanceDeal"
                :deal="deal"
                :stock-type="stockType"
                :contract-requested="contractRequested"
                :selected-accessories="selectedAccessories"
                :selected-protection-products="selectedProtectionProducts"
                class="mb-3"
            />

            <deal-cash
                v-else
                :deal="deal"
                :stock-type="stockType"
                :selected-accessories="selectedAccessories"
                :selected-protection-products="selectedProtectionProducts"
                class="mb-3"
            />
        </template>
    </section-template>
</template>
<script>
import _ from "lodash";
import DealCash from "./DealCash";
import DealLease from "./DealLease";
import DealFinance from "./DealFinance";
import SectionTemplate from "../SectionTemplate";
import ExpiredOverlay from "Components/Deal/components/ExpiredOverlay";
import api from "Util/api";

export default {
    name: "Deal",
    components: { ExpiredOverlay, SectionTemplate, DealFinance, DealLease, DealCash },
    props: {
        deal: {
            type: Object,
            required: true,
        },
        trade: {
            type: Object,
            required: false,
            default: null,
        },
        stockType: {
            type: String,
            required: false,
            default: null,
        },
        order: {
            type: Object,
            required: false,
            default: null,
        },
    },
    data() {
        return {
            selectedAccessories: null,
            selectedProtectionProducts: null,
        };
    },
    computed: {
        contractRequested() {
            return _.get(this.order, "contractRequested");
        },
        isLeaseDeal() {
            return _.get(this.deal, "dealType") === "LEASE";
        },
        isFinanceDeal() {
            return _.get(this.deal, "dealType") === "FINANCE";
        },
        isPreApprovalValid() {
            return _.get(this.deal, "preApproval.valid", true);
        },
        certificateId() {
            return _.get(this.deal, "certificateId", null);
        },
    },
    created() {
        this.fetchSelectedAccessoriesDealDetails();
        this.fetchProtectionProductsDealDetails();
    },
    methods: {
        fetchSelectedAccessoriesDealDetails() {
            return api
                .get(`/deal/accessories/${this.certificateId}`)
                .then((response) => {
                    this.selectedAccessories = response.data;
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        fetchProtectionProductsDealDetails() {
            return api
                .get(`/deal/protection-product/${this.certificateId}`)
                .then((response) => {
                    this.selectedProtectionProducts = response.data;
                })
                .catch((error) => {
                    console.error(error);
                });
        },
    },
};
</script>
