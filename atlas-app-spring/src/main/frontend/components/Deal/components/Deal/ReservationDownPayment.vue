<template>
    <div>
        <div class="d-flex justify-content-between align-center mb-1">
            <div class="label">Down Payment Summary</div>
        </div>
        <div class="subLineItem">
            <div class="ml-2 d-flex justify-space-between align-baseline mb-1">
                <div class="label">Down Payment Due</div>
                <div v-if="downPayment" key="deal_down_payment_due" class="value">
                    - {{ downPaymentDue | numeral("$0,0") }}
                </div>
                <div v-else key="deal_no_down_payment">$0.00</div>
            </div>
            <div class="ml-2 d-flex justify-space-between align-baseline mb-1">
                <div class="label">Reservation Deposit</div>
                <div class="value">- {{ reservationDeposit | numeral("$0,0") }}</div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "CertificateReservationDownPayment",
    props: {
        downPayment: {
            type: Number,
            required: false,
            default: null,
        },
        reservationDeposit: {
            type: Number,
            required: false,
            default: null,
        },
    },
    computed: {
        downPaymentDue() {
            let downPaymentDue = this.downPayment;
            return downPaymentDue;
        },
    },
};
</script>
<style lang="scss" scoped>
.subLineItem {
    font-size: px2rem(12);
}
</style>
