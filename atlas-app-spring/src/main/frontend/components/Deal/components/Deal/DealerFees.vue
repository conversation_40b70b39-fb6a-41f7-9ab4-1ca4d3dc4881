<template>
    <div>
        <div class="d-flex justify-space-between align-baseline">
            <div v-if="!noHeading" class="label text-decoration-underline">Taxes &amp; Fees</div>
            <div v-if="editingEnabled" class="d-flex justify-end mb-3">
                <div>
                    <v-btn x-small outlined color="warning" @click="addNewFee">
                        Add Fee
                        <v-icon x-small> mdi-plus </v-icon>
                    </v-btn>
                </div>
            </div>
        </div>
        <div class="label text-md-h5">
            <dealer-fee-editor v-if="feeEditorShowing" :initial-fee="newFee" @saved="addFee" @cancelled="resetDialog" />
        </div>
        <div v-for="(fee, index) in dealerFees" :key="index" class="d-flex justify-space-between align-baseline">
            <div class="label">
                {{ fee.name }}
                <info-tooltip v-if="fee.description" size="15">{{ fee.description }}</info-tooltip>
            </div>
            <div v-if="editingEnabled" class="d-flex justify-space-between align-baseline">
                <div class="value d-flex justify-end">
                    <v-text-field v-model="fee.amount" prefix="$" required dense outlined />
                </div>
                <div v-if="editingEnabled" class="d-flex justify-end pl-1">
                    <v-icon v-if="fee.adHoc" small @click="editFee(fee)"> mdi-pencil </v-icon>
                    <v-icon color="red darken-2" small @click="removeFee(fee)"> mdi-close-circle-outline </v-icon>
                </div>
            </div>

            <div v-else class="value">+ {{ fee.amount | numeral("$0,0.00") }}</div>
        </div>
    </div>
</template>
<script>
import InfoTooltip from "Components/InfoTooltip";
import DealerFeeEditor from "Components/Deal/components/Deal/DealerFeeEditor";

export default {
    name: "DealerFees",
    components: { InfoTooltip, DealerFeeEditor },
    props: {
        dealerFees: {
            type: Array,
            required: true,
        },
        stockType: {
            type: String,
            required: true,
        },
        editingEnabled: {
            type: Boolean,
            required: true,
        },
        noHeading: {
            type: Boolean,
            default: false,
            required: false,
        },
    },
    data() {
        return {
            localStateDealerFees: null,
            selectedFees: null,
            dialog: false,
            feeEditorShowing: false,
            newFee: {
                name: "",
                description: "",
                amount: null,
                adHoc: true,
            },
        };
    },
    computed: {
        newVehicleDealerFees() {
            let newDealerFeesArray = [];
            _.forEach(this.dealerFees, (fee) => {
                if (fee.stockType !== "USED") {
                    newDealerFeesArray.push(fee);
                }
            });

            return newDealerFeesArray;
        },
        usedVehicleDealerFees() {
            let usedDealerFeesArray = [];
            _.forEach(this.dealerFees, (fee) => {
                if (fee.stockType !== "NEW") {
                    usedDealerFeesArray.push(fee);
                }
            });

            return usedDealerFeesArray;
        },
    },
    created() {
        this.setFeatures();
        if (this.stockType === "NEW") {
            this.localStateDealerFees = this.newVehicleDealerFees;
        } else {
            this.localStateDealerFees = this.usedVehicleDealerFees;
        }
    },
    methods: {
        setFeatures() {
            // if (isEnabled('DealEditing')) {
            //     this.editingEnabled = true;
            // }
        },
        fetchFees() {
            this.selectedFees = this.dealerFees;
        },
        addNewFee() {
            this.feeEditorShowing = true;
        },
        addFee(evt) {
            this.$emit("addDealerFee", evt);
            this.resetDialog();
        },
        resetDialog() {
            this.feeEditorShowing = false;
            this.newFee = {
                name: "",
                description: "",
                amount: null,
                adHoc: true,
            };
        },
        editFee(fee) {
            this.newFee = fee;
            this.feeEditorShowing = true;
        },
        removeFee(fee) {
            this.$emit("removeDealerFee", fee);
        },
    },
};
</script>
<style lang="scss" scoped>
div {
    .section-content {
        border-radius: 2px;
        border: 1px solid $grey;
    }
}
.v-text-field__slot {
    input {
        text-align: right;
    }
}
.value .v-input__slot {
    min-height: 32px;
}
</style>
