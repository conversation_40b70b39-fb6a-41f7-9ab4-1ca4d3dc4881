<template>
    <div v-if="checkAccessories">
        <div class="d-flex justify-space-between align-center mb-1">
            <div class="label">Accessories</div>
            <div v-if="!accessoriesFlag" class="value">+{{ total | numeral("$0,0.00") }}</div>
        </div>

        <div class="ml-2 d-flex justify-space-between align-center mb-1"></div>

        <div
            class="ml-2 label mb-1 text-decoration-underline accessories-flag-label"
            @click="accessoriesFlag = !accessoriesFlag"
        >
            {{ accessoriesFlag ? "Hide accessories" : "Show accessories" }}
        </div>
        <div v-if="accessoriesFlag" class="transition-fast-in-fast-out mb-3">
            <div
                v-for="(accessoryItem, index) in selectedAccessoriesDealDetails.accessoriesItems"
                :key="index"
                class="ml-2 d-flex justify-space-between align-center mb-1"
            >
                <div class="label">{{ accessoryItem.name }}</div>
                <div class="value">+{{ accessoryItem.partsPrice | numeral("$0,0.00") }}</div>
            </div>
            <div class="ml-2 d-flex justify-space-between align-center mb-1">
                <div class="label">Labor</div>
                <div class="value">+{{ selectedAccessoriesDealDetails.totalLaborAmount | numeral("$0,0.00") }}</div>
            </div>
        </div>
    </div>
</template>

<script>
import _ from "lodash";
export default {
    name: "DealSelectedAccessoriesDetails",
    props: {
        selectedAccessoriesDealDetails: {
            type: Object,
            required: false,
            default: null,
        },
    },
    data() {
        return {
            accessoriesFlag: false,
        };
    },
    computed: {
        total() {
            return _.get(this.selectedAccessoriesDealDetails, "total", 0);
        },
        checkAccessories() {
            return (
                this.selectedAccessoriesDealDetails &&
                this.selectedAccessoriesDealDetails.accessoriesItems &&
                this.selectedAccessoriesDealDetails.accessoriesItems.length > 0 &&
                this.total > 0
            );
        },
    },
};
</script>

<style scoped>
.accessories-flag-label {
    color: #007dc6 !important;
    cursor: pointer;
}
</style>
