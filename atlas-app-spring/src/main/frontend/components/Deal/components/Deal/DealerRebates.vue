<template>
    <div>
        <div class="d-flex justify-space-between align-baseline">
            <div class="label text-decoration-underline">Dealer Rebates</div>
            <div v-if="editingEnabled" class="d-flex justify-end mb-3">
                <div>
                    <v-btn x-small outlined color="warning" @click="addNewRebate">
                        Add Rebate
                        <v-icon x-small> mdi-plus </v-icon>
                    </v-btn>
                </div>
            </div>
        </div>
        <div class="label text-md-h5">
            <dealer-rebate-editor
                v-if="rebateEditorShowing"
                :initial-rebate="newRebate"
                @saved="addRebate"
                @cancelled="resetDialog"
            />
        </div>
        <div v-for="(rebate, index) in dealerRebates" :key="index" class="d-flex justify-space-between align-baseline">
            <div class="label">
                {{ rebate.name }}
            </div>
            <div v-if="editingEnabled" class="d-flex justify-space-between align-baseline">
                <div class="value d-flex justify-end">
                    <v-text-field v-model="rebate.amount" prefix="$" required dense outlined />
                </div>
                <div v-if="editingEnabled" class="d-flex justify-end pl-1">
                    <v-icon v-if="rebate.adHoc" small @click="editRebate(rebate)"> mdi-pencil </v-icon>
                    <v-icon color="red darken-2" small @click="removeRebate(rebate)"> mdi-close-circle-outline </v-icon>
                </div>
            </div>

            <div v-else class="value">- {{ rebate.amount | numeral("$0,0.00") }}</div>
        </div>
    </div>
</template>
<script>
import DealerRebateEditor from "Components/Deal/components/Deal/DealerRebateEditor";

export default {
    name: "DealerRebates",
    components: { DealerRebateEditor },
    props: {
        dealerRebates: {
            type: Array,
            required: true,
            default: () => {
                return [];
            },
        },
        stockType: {
            type: String,
            required: true,
        },
        editingEnabled: {
            type: Boolean,
            required: true,
        },
    },
    data() {
        return {
            localStateDealerRebates: null,
            selectedRebates: null,
            dialog: false,
            rebateEditorShowing: false,
            newRebate: {
                name: "",
                amount: null,
                adHoc: true,
            },
        };
    },
    computed: {
        newVehicleDealerRebates() {
            let newDealerRebatesArray = [];
            _.forEach(this.dealerRebates, (rebate) => {
                if (rebate.stockType !== "USED") {
                    newDealerRebatesArray.push(rebate);
                }
            });

            return newDealerRebatesArray;
        },
        usedVehicleDealerRebates() {
            let usedDealerRebatesArray = [];
            _.forEach(this.dealerRebates, (rebate) => {
                if (rebate.stockType !== "NEW") {
                    usedDealerRebatesArray.push(rebate);
                }
            });

            return usedDealerRebatesArray;
        },
    },
    created() {
        this.setFeatures();
        if (this.stockType === "NEW") {
            this.localStateDealerRebates = this.newVehicleDealerRebates;
        } else {
            this.localStateDealerRebates = this.usedVehicleDealerRebates;
        }
    },
    methods: {
        setFeatures() {
            // if (isEnabled('DealEditing')) {
            //     this.editingEnabled = true;
            // }
        },
        fetchRebates() {
            this.selectedRebates = this.dealerRebates;
        },
        addNewRebate() {
            this.rebateEditorShowing = true;
        },
        addRebate(evt) {
            this.$emit("addDealerRebate", evt);
            this.resetDialog();
        },
        resetDialog() {
            this.rebateEditorShowing = false;
            this.newRebate = {
                name: "",
                amount: null,
                adHoc: true,
            };
        },
        editRebate(rebate) {
            this.newRebate = rebate;
            this.rebateEditorShowing = true;
        },
        removeRebate(rebate) {
            this.$emit("removeDealerRebate", rebate);
        },
    },
};
</script>
<style lang="scss" scoped>
div {
    .section-content {
        border-radius: 2px;
        border: 1px solid $grey;
    }
}
.v-text-field__slot {
    input {
        text-align: right;
    }
}
.value .v-input__slot {
    min-height: 32px;
}
</style>
