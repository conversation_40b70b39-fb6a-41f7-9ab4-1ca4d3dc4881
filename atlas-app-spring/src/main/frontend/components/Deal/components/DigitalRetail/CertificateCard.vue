<template>
    <div class="border bg-white border-radius-small certificate-card">
        <div class="row">
            <div class="col">
                <div class="d-flex align-items-center d-flex align-items-center marginx-20 marginy-12">
                    <div v-if="$slots.contentLeft" class="d-flex align-items-center cursor-pointer margine-12">
                        <slot name="contentLeft" />
                    </div>
                    <div class="d-flex flex-fill text-primary">
                        <h6 class="d-inline fw-semibold mb-0 lh-140 card-title">
                            {{ title }}
                        </h6>
                    </div>
                    <BaseLink v-if="$slots.content" class="d-flex text-decoration-none" @click="toggleCollapse">
                        <Icon v-if="isCollapsed" name="expand_less" />
                        <Icon v-if="!isCollapsed" name="expand_more" />
                    </BaseLink>
                </div>
            </div>
        </div>

        <div v-if="isCollapsed && $slots.content" class="row">
            <div class="col">
                <section class="paddingt-20 paddingx-20 paddingb-40">
                    <slot name="content" />
                </section>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    name: "CertificateCard",
    props: {
        title: {
            type: String,
            required: true,
        },
    },
};
</script>
<style lang="scss" scoped>
.border-radius-small {
    border-radius: var(--cs-border-radius__small);
}
.certificate-card {
    .lh-140 {
        line-height: 1.4;
    }
    .card-title {
        font-size: var(--cs-font-size__body-l);
    }
}
</style>
