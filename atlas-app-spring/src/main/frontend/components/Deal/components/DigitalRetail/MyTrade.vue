<template>
    <v-expansion-panels v-model="panel" multiple class="mb-2" :value="2">
        <v-expansion-panel :key="21" class="border bg-white border-radius-small certificate-card">
            <v-expansion-panel-header class="card-title">
                <div class="d-flex justify-space-between">
                    <span class="font-weight-bold font-xl">Trade-In</span>
                </div>
            </v-expansion-panel-header>
            <v-expansion-panel-content class="pt-2 fw-400 fs-14">
                <div class="">
                    <div class="d-flex flex-column">
                        <div class="d-flex flex-column mb-20">
                            <div class="p-2 p-md-0 mb-2">
                                <div class="row">
                                    <div class="col-md-6 trade-img">
                                        <img
                                            src="https://autonationoverlays.blob.core.windows.net/nophoneoverlay/2416_5_1N4AA6FV7PC503675-0_BG_640.jpg"
                                            alt=""
                                        />
                                    </div>
                                    <div class="col-md-6">
                                        <p class="font-weight-bold">
                                            {{ tradeInfo.year }} {{ tradeInfo.make }}
                                            {{ tradeInfo.model }}
                                        </p>
                                    </div>
                                </div>

                                <div class="my-trade pt-10">
                                    <div class="d-flex">
                                        <div class="d-flex flex-column col-md-6 description">
                                            <div class="row mr-3">
                                                <div class="col-md-6">External Color:</div>
                                                <div class="col-md-6 text-md-right">
                                                    {{ tradeInfo.externalColor }}
                                                </div>
                                            </div>
                                            <div class="row mr-3">
                                                <div class="col-md-6">Mileage:</div>
                                                <div class="col-md-6 text-md-right">{{ tradeInfo.mileage }}</div>
                                            </div>
                                            <div class="row mr-3">
                                                <div class="col-md-6">VIN:</div>
                                                <div class="col-md-6 text-md-right">{{ tradeInfo.vin }}</div>
                                            </div>
                                        </div>
                                        <div
                                            v-if="tradeInfo.type !== 'LEASE_TURN_IN'"
                                            class="d-flex flex-column col-md-6"
                                        >
                                            <div class="row ml-3">
                                                <div class="col-md-6">Trade Value:</div>
                                                <div class="col-md-6 text-md-right">
                                                    {{ convertToDollar(tradeInfo.valueOfVehicle) }}
                                                </div>
                                            </div>
                                            <div class="row ml-3">
                                                <div class="col-md-6">Loan Amount:</div>
                                                <div class="col-md-6 text-md-right">
                                                    {{ convertToDollar(tradeInfo.loanAmount) }}
                                                </div>
                                            </div>
                                            <div class="row divider">
                                                <div class="ml-2 col-md-12"><hr /></div>
                                            </div>
                                            <div class="row ml-3">
                                                <div class="col-md-6 font-weight-bold">Net Cash Offer:</div>
                                                <div class="col-md-6 text-md-right">
                                                    <div class="font-weight-bold">
                                                        {{ convertToDollar(tradeInfo.netCashOffer) }}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div v-else class="d-flex flex-column col-md-6">
                                            <div class="row ml-3">
                                                <div class="col-md-6">Remaining Payments:</div>
                                                <div class="col-md-6 text-md-right fw-bold">
                                                    ({{ tradeInfo.remainingPayments }}&nbsp; x &nbsp;{{
                                                        tradeInfo.monthlyPayments
                                                    }})
                                                </div>
                                            </div>
                                            <div class="row ml-3">
                                                <div class="col-md-6">Disposition Fee:</div>
                                                <div class="col-md-6 text-md-right">
                                                    {{ tradeInfo.dispositionFee }}
                                                </div>
                                            </div>
                                            <div class="row divider">
                                                <div class="col-md-12 ml-3"><hr /></div>
                                            </div>
                                            <div class="row ml-3">
                                                <div class="col-md-6 font-weight-bold">
                                                    Estimated Lease Balance Owed:
                                                </div>
                                                <div class="col-md-6 text-md-right">
                                                    <div class="font-weight-bold">
                                                        {{ convertToDollar(tradeInfo.loanAmount) }}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </v-expansion-panel-content>
        </v-expansion-panel>
    </v-expansion-panels>
</template>

<script>
export default {
    name: "MyTrade",
    components: {},
    props: {
        tradeInfo: {
            type: Object,
            required: true,
        },
    },
    data: () => ({
        panel: [0, 1, 2, 3, 4, 5, 6],
        disabled: false,
    }),
    computed: {},
    methods: {
        convertToDollar(value) {
            // Use Number.prototype.toLocaleString() to format the number as a currency
            return value.toLocaleString("en-US", { style: "currency", currency: "USD", minimumFractionDigits: 0 });
        },
    },
};
</script>

<style lang="scss">
@import "~vuetify/src/styles/settings/_variables";
.trade-img {
    margin-top: inherit;
    img {
        max-width: 70px;
        float: right;
    }
}
.my-trade {
    .description {
        height: 30px;
    }
    .divider {
        hr {
            border: 1px solid #d3d3d3;
        }
        .col-md-12 {
            padding: 3px;
        }
    }

    .col-md-6 {
        padding: 3px;
    }
}
.fst-italic {
    font-style: italic;
}
.fw-bold {
    font-weight: 700;
}
.font-large {
    font-size: 1rem;
}

.font-xl {
    font-size: 1.375rem;
}
.lh-40 {
    line-height: 0.4;
}
.lh-0 {
    line-height: 0;
}
.gap-12px {
    gap: 0.75rem;
}
.overflow-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    text-wrap: nowrap;
}
.border-radius-small {
    border-radius: 0.85em !important;
}
.certificate-card {
    .lh-40 {
        line-height: 0.4;
    }
    .card-title {
        border-width: 0em !important;
    }
    margin-left: 10px;
    padding-right: 12px;
    border-style: solid;
    border-color: lightgray;
    border-width: thin;
}
</style>
