<template>
    <v-container class="contract-icons">
        <v-row>
            <v-col cols="6" md="3">
                <div class="iconBlock d-flex align-center">
                    <div class="block d-flex align-center justify-center">
                        <v-icon large>mdi-car-side</v-icon>
                    </div>
                    <span class="iconLabel">{{ bodyStyle }}</span>
                </div>
            </v-col>
            <v-col cols="6" md="3">
                <div class="iconBlock d-flex align-center">
                    <div class="block d-flex align-center justify-center">
                        <v-icon large>mdi-seat</v-icon>
                    </div>
                    <span class="iconLabel">{{ passengerCapacity }} seats</span>
                </div>
            </v-col>
            <v-col cols="6" md="3">
                <div class="iconBlock d-flex align-center">
                    <div class="block d-flex align-center justify-center">
                        <v-icon large>mdi-steering</v-icon>
                    </div>
                    <span class="iconLabel">{{ transmission | lowerCaseString }}</span>
                </div>
            </v-col>
            <v-col cols="6" md="3">
                <div class="iconBlock d-flex align-center">
                    <div class="block d-flex align-center justify-center">
                        <v-icon large>mdi-steering</v-icon>
                    </div>

                    <span class="iconLabel">{{ driveTrainFormatted }}</span>
                </div>
            </v-col>
        </v-row>
        <v-row>
            <v-col cols="6" md="3">
                <div class="iconBlock d-flex align-center">
                    <div class="block d-flex align-center justify-center">
                        <v-icon large>mdi-gas-station</v-icon>
                    </div>
                    <span class="iconLabel">{{ fuelType | lowerCaseString }}</span>
                </div>
            </v-col>
            <v-col cols="6" md="3">
                <div class="iconBlock d-flex align-center">
                    <div class="block d-flex align-center justify-center">
                        <v-icon large>mdi-engine</v-icon>
                    </div>
                    <span class="iconLabel">{{ horsePower }} hp</span>
                </div>
            </v-col>
            <v-col cols="6" md="3">
                <div class="iconBlock d-flex align-center">
                    <div class="block d-flex align-center justify-center">
                        <v-icon large>mdi-engine-outline</v-icon>
                    </div>
                    <span class="iconLabel"> {{ engineCylinders }} cylinders </span>
                </div>
            </v-col>
            <v-col cols="6" md="3">
                <div class="iconBlock d-flex align-center">
                    <div class="block d-flex align-center justify-center">
                        <v-icon large>mdi-fuel</v-icon>
                    </div>
                    <span class="iconLabel">{{ engine }}</span>
                </div>
            </v-col>
        </v-row>
    </v-container>
</template>
<script>
export default {
    filters: {
        lowerCaseString(value) {
            if (!value) return "";
            value = value.toLowerCase();
            return value;
        },
    },
    props: {
        vehicle: {
            type: Object,
            required: true,
        },
    },
    computed: {
        engineCylinders() {
            return _.get(this.vehicle, "engineCylinders", null);
        },
        bodyStyle() {
            return _.get(this.vehicle, "bodyStyle", null);
        },
        horsePower() {
            return _.get(this.vehicle, "horsePower", null);
        },
        transmission() {
            return _.get(this.vehicle, "transmission", null);
        },
        passengerCapacity() {
            return _.get(this.vehicle, "passengerCapacity", null);
        },
        engine() {
            return _.get(this.vehicle, "engine", null);
        },
        fuelType() {
            return _.get(this.vehicle, "fuelType", null);
        },
        driveTrain() {
            return _.get(this.vehicle, "driveTrain", null);
        },
        driveTrainFormatted() {
            const driveTrain = this.driveTrain;

            switch (driveTrain) {
                case "FRONT_WHEEL_DRIVE":
                    return "Front Wheel Drive";
                case "ALL_WHEEL_DRIVE":
                    return "All Wheel Drive";
                case "REAR_WHEEL_DRIVE":
                    return "Rear Wheel Drive";
                case "FOUR_WHEEL_DRIVE":
                    return "Four Wheel Drive";
                default:
                    return driveTrain;
            }
        },
    },
};
</script>
<style lang="scss">
.contract-icons {
    margin-bottom: px2rem(12);

    .iconBlock {
        margin: 0 0 px2rem(8) 0;

        .block {
            width: 40px;
            height: auto;
            margin-right: 8px;
        }
        .iconLabel {
            text-transform: capitalize;
            color: $grey-500;
            width: 95px;
            font-size: px2rem(14);
        }
    }

    //@include media-breakpoint-up(md) {
    //    margin-bottom: px2rem(16);
    //}
}
</style>
