<template>
    <v-row id="payoff-data-block">
        <v-col cols="12" class="pb-0">
            <div class="d-flex justify-space-between">
                <div class="d-flex">
                    <span class="font-weight-bold mr-2"> Finance Company: </span>
                    <span class="font-weight-bold">
                        {{ payoffQuote.financeCompany }}
                    </span>
                </div>

                <v-btn v-if="editingTradePayOff" x-small outlined color="red" @click="toggleEditing">
                    Cancel
                    <v-icon x-small> mdi-close-circle-outline </v-icon>
                </v-btn>

                <v-btn
                    v-else-if="hasCustomerDealEditAccess"
                    :disabled="!active"
                    x-small
                    outlined
                    color="blue"
                    @click="toggleEditing"
                >
                    Edit
                    <v-icon x-small> mdi-pencil </v-icon>
                </v-btn>
            </div>
        </v-col>
        <v-col cols="12" :md="editingTradePayOff ? 6 : 12">
            <div>
                <div class="d-flex flex-column">
                    <div class="d-flex justify-space-between mb-2">
                        <div class="payoff-label">Finance Type:</div>
                        <div class="value">
                            {{ payoffQuote.financeType }}
                        </div>
                    </div>
                    <div v-if="isLeasePayOffEnabled" class="d-flex flex-column">
                        <div class="d-flex justify-space-between mb-2">
                            <div class="payoff-label">Trade Type:</div>
                            <div class="value">
                                {{ tradeType | formattedTradeType }}
                            </div>
                        </div>
                    </div>
                    <div class="d-flex justify-space-between mb-2">
                        <div class="payoff-label">Payoff Good Through:</div>
                        <div class="value">
                            {{ payoffQuote.payoffGoodThrough }}
                        </div>
                    </div>
                    <div class="d-flex justify-space-between mb-2">
                        <div class="payoff-label">Payoff Amount:</div>
                        <div class="value">
                            {{ payoffQuote.payoffAmount | numeral("$0,0") }}
                        </div>
                    </div>
                    <div class="d-flex justify-space-between mb-2">
                        <div class="payoff-label">Payment Amount:</div>
                        <div class="value">
                            {{ payoffQuote.monthlyPayment | numeral("$0,0.00") }}
                        </div>
                    </div>
                    <div class="d-flex justify-space-between mb-2">
                        <div class="payoff-label">Payments Remaining:</div>
                        <div class="value">
                            {{ payoffQuote.paymentsRemaining }}
                        </div>
                    </div>
                    <div v-if="payoffQuote.payoffMethod" class="d-flex justify-space-between mb-2">
                        <div class="payoff-label">Payoff Method:</div>
                        <div class="value">
                            {{ displayPayoffMethod(payoffQuote) }}
                        </div>
                    </div>
                    <div v-if="payoffQuote.payoffReferenceId" class="d-flex justify-space-between mb-2">
                        <div class="payoff-label">Payoff Reference Id:</div>
                        <div class="value">
                            {{ displayPayoffReferenceId(payoffQuote) }}
                        </div>
                    </div>
                </div>
            </div>
        </v-col>
        <v-col v-if="editingTradePayOff" cols="12" md="6">
            <pay-off-edit-form
                :payoff-quote="payoffQuote"
                :trade-vehicle-id="tradeVehicleId"
                :dealer-id="dealerId"
                @success="refreshData"
            />
        </v-col>
    </v-row>
</template>
<script>
import _ from "lodash";
import PayOffEditForm from "Components/Deal/components/PayOffEditForm";

export default {
    name: "PayOffData",
    components: { PayOffEditForm },
    filters: {
        formattedTradeType(tradeType) {
            let result = "";
            if (tradeType != null) {
                result = tradeType
                    .toLowerCase()
                    .split("_")
                    .map((word) => word.charAt(0).toUpperCase() + word.substring(1))
                    .join(" ");
            }
            return result === "Lease Turn In" ? "Lease Turn-In " : result;
        },
    },
    props: {
        payoffQuote: {
            type: Object,
            required: true,
        },
        tradeVehicleId: {
            type: String,
            required: true,
        },
        dealerId: {
            type: String,
            required: true,
        },
        active: {
            type: Boolean,
            required: true,
        },
        tradeType: {
            type: String,
            required: false,
            default: "",
        },
        isLeasePayOffEnabled: {
            type: Boolean,
            required: false,
            default: false,
        },
    },
    data() {
        return {
            editingTradePayOff: false,
        };
    },
    computed: {
        hasCustomerDealEditAccess() {
            return this.$acl.hasDealerPermission(this.dealerId, "customer:deal:edit");
        },
    },
    methods: {
        displayPayoffMethod(quote) {
            return _.get(quote, "payoffMethod", null);
        },
        displayPayoffReferenceId(quote) {
            return _.get(quote, "payoffReferenceId", null);
        },
        toggleEditing() {
            this.editingTradePayOff = !this.editingTradePayOff;
        },
        refreshData() {
            this.toggleEditing();
            this.$emit("refresh");
        },
    },
};
</script>
<style lang="scss">
#payoff-data-block {
    .payoff-label,
    .value {
        color: #343a40;
        font-size: 14px;
    }
}
</style>
