<template>
    <section-template v-if="hasDelivery">
        <template #section-title> Delivery Information </template>
        <template #section-content>
            <div class="pa-3 w-100">
                <div class="d-flex flex-column flex-md-row justify-md-space-between mb-2">
                    <div class="label mr-1">Vehicle Delivery Address:</div>
                    <div class="value address">
                        {{ address }}
                    </div>
                </div>

                <div class="d-flex flex-column flex-md-row justify-md-space-between mb-2">
                    <div class="label mr-1 fee">Delivery Fee:</div>
                    <div v-if="isFree" class="value">Free</div>
                    <div v-else class="value">
                        {{ amount | numeral("$0,0") }}
                    </div>
                </div>
            </div>
        </template>
    </section-template>
</template>
<script>
import SectionTemplate from "./SectionTemplate.vue";
import _ from "lodash";
export default {
    name: "RemoteDeliveryInfo",
    components: {
        SectionTemplate,
    },
    props: {
        delivery: {
            type: Object,
            required: true,
        },
    },
    computed: {
        address() {
            const address = _.get(this.delivery, "address", null);
            return address;
        },
        amount() {
            const amount = _.get(this.delivery, "amount", null);
            return amount;
        },
        isFree() {
            const value = this.amount === 0;
            return value;
        },
        hasDelivery() {
            const hasDelivery = !_.isNil(this.delivery) && !_.isNil(this.address) && !_.isNil(this.amount);
            return hasDelivery;
        },
    },
};
</script>
<style lang="scss">
.label {
    font-size: 14px;
}
.value.fee {
    width: 53px;
    font-size: 14px;
    margin-right: 6.7rem !important;
}
.value.address {
    font-size: 14px;
    font-style: oblique;
}
@media print {
    .directions {
        font-size: 14px;
        font-style: oblique;
        visibility: hidden;
    }
}
</style>
