<template>
    <section-template>
        <template #section-title>{{ !isSellAtHome ? "Trade-in" : "Sell Your Car" }}*</template>
        <template v-if="isSellAtHome" #section-subtitle>
            <div class="sell-at-home-disclaimer font-italic mx-auto px-2 mt-3">
                *By activating Sell Your Car, {{ dealerName }} assumes full responsibility for any Cash Offers that are
                extended to customers. CarSaver will not assume any responsibility for the Cash Offer. Cash Offers shall
                be finalized after a physical inspection of the vehicle. {{ dealerName }} shall honor any final Cash
                Offers made following such inspection.
            </div>
        </template>
        <template #section-content>
            <v-skeleton-loader v-if="loading" type="table" />
            <div v-else id="trade-in">
                <div class="px-3 pt-3 pb-1 pb-md-4 px-md-4 pt-md-4">
                    <div class="d-flex flex-column flex-md-row justify-md-space-between align-center mb-4">
                        <div class="trade-ymm text--grey text--darken-4 text-center font-weight-bold">
                            {{ vehicleYear }}
                            {{ vehicleMake }}
                            {{ vehicleModel }}
                            {{ vehicleTrim }}
                        </div>

                        <div>
                            <v-btn
                                v-if="
                                    hasCustomerDealEditAccess && !vehicleQuote.discrepancy && !discrepancyEditorShowing
                                "
                                x-small
                                outlined
                                color="warning"
                                class="mr-3"
                                :disabled="!active"
                                @click="showDiscrepancyEditor"
                            >
                                Add Discrepancy
                                <v-icon x-small> mdi-alert-box-outline </v-icon>
                            </v-btn>

                            <v-btn v-if="offerEditorShowing" x-small outlined color="red" @click="toggleEditing">
                                Cancel
                                <v-icon x-small> mdi-close-circle-outline </v-icon>
                            </v-btn>
                            <v-btn v-if="offerEditorShowing" x-small dark color="green" class="ml-3" @click="validate">
                                Save
                            </v-btn>
                            <v-btn
                                v-else-if="hasCustomerDealEditAccess"
                                x-small
                                outlined
                                color="blue"
                                :disabled="!active"
                                @click="toggleEditing"
                            >
                                Edit Offer
                                <v-icon x-small> mdi-pencil </v-icon>
                            </v-btn>
                        </div>
                    </div>
                    <div>
                        <discrepancy-viewer
                            v-if="vehicleQuote.discrepancy"
                            :initial-discrepancy="vehicleQuote.discrepancy"
                            :trade-vehicle-id="vehicleQuote.userVehicleId"
                            :dealer-id="dealerId"
                            @discrepancy-removed="handleRemovedDiscrepancy"
                        />
                        <div v-else>
                            <discrepancy-editor
                                v-if="discrepancyEditorShowing"
                                :dealer-id="dealerId"
                                :trade-vehicle-id="vehicleQuote.userVehicleId"
                                @saved="handleSavedDiscrepancy"
                                @cancelled="handleCancelDiscrepancyEditor"
                            />
                        </div>
                    </div>
                    <v-row>
                        <v-col cols="12" class="left-col">
                            <v-form ref="form" v-model="valid" lazy-validation>
                                <div class="d-flex justify-space-between mb-2">
                                    <div class="trade-label">Conditional Cash Offer:</div>

                                    <div class="value font-weight-bold">
                                        <v-text-field
                                            v-if="offerEditorShowing"
                                            v-model.number="editableOffer"
                                            class="pt-0 mt-0"
                                            :disabled="loading"
                                            :rules="quoteAmountRules"
                                        />
                                        <span v-else>{{ vehicleQuote.totalTradeValue | numeral("$0,0") }}</span>
                                    </div>
                                </div>
                                <div class="d-flex justify-space-between mb-2">
                                    <div class="trade-label">Expiration Date:</div>

                                    <div class="value font-weight-bold">
                                        <v-menu
                                            v-if="offerEditorShowing"
                                            v-model="expirationMenu"
                                            :close-on-content-click="false"
                                            :nudge-left="100"
                                            transition="scale-transition"
                                            offset-y
                                            origin="top center"
                                            min-width="auto"
                                        >
                                            <template #activator="{ on, attrs }">
                                                <v-text-field
                                                    v-model="editableExpirationDate"
                                                    class="pt-0 mt-0"
                                                    :disabled="loading"
                                                    :rules="expirationRules"
                                                    prepend-icon="mdi-calendar"
                                                    readonly
                                                    label="Expiration"
                                                    required
                                                    v-bind="attrs"
                                                    v-on="on"
                                                />
                                            </template>
                                            <v-date-picker
                                                v-model="editableExpirationDate"
                                                :allowed-dates="allowedDates"
                                                @input="expirationMenu = false"
                                            />
                                        </v-menu>
                                        <span v-else>{{ vehicleQuote.expirationDate }}</span>
                                    </div>
                                </div>
                            </v-form>

                            <div class="d-flex justify-space-between mb-2">
                                <div class="trade-label text--grey text--darken-4">Trim:</div>
                                <div class="value text--grey text--darken-3">
                                    {{ vehicleTrim }}
                                </div>
                            </div>
                            <div v-if="!isLeaseReturn" class="d-flex justify-space-between mb-2">
                                <div class="trade-label text--grey text--darken-4">Exterior Color:</div>
                                <div class="value text--grey text--darken-3">
                                    {{ vehicleQuote.color }}
                                </div>
                            </div>
                            <div class="d-flex justify-space-between mb-2">
                                <div class="trade-label text--grey text--darken-4">Mileage:</div>
                                <div class="value max text--grey text--darken-3">
                                    {{ vehicleQuote.mileage | numeral("0,0") }}
                                </div>
                            </div>
                            <div class="d-flex justify-space-between mb-2">
                                <div class="trade-label text--grey text--darken-4">Vin:</div>
                                <div class="value max text--grey text--darken-3">
                                    {{ vehicleQuote.vin }}
                                </div>
                            </div>
                            <div v-if="!isLeaseReturn" class="d-flex justify-space-between mb-2">
                                <div class="trade-label text--grey text--darken-4">Condition:</div>
                                <div class="value max text--grey text--darken-3">
                                    {{ vehicleQuote.physicalCondition }}
                                </div>
                            </div>
                            <div v-if="!isLeaseReturn" class="d-flex justify-space-between mb-2">
                                <div class="trade-label text--grey text--darken-4">Transmission:</div>
                                <div class="value max text--grey text--darken-3">
                                    {{ vehicleQuote.transmission }}
                                </div>
                            </div>
                            <div
                                v-if="selectedFeatures.length >= 1 && !isLeaseReturn"
                                class="d-flex justify-space-between mb-2"
                            >
                                <div class="trade-label text--grey text--darken-4">Selected Features:</div>
                                <div class="value max text--grey text--darken-3">
                                    <ul class="features text-right">
                                        <li v-for="(feature, index) in selectedFeatures" :key="index" class="pb-1">
                                            {{ feature.description }}
                                            <small>({{ feature.tradeInValue | numeral("$0,0") }})</small>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </v-col>
                    </v-row>
                    <v-row v-if="hasQuestions && !isLeaseReturn" class="mt-0">
                        <v-col cols="12">
                            <trade-in-vehicle-history :questions="questions" />
                        </v-col>
                    </v-row>
                </div>

                <div class="pa-3 pa-md-4 grey lighten-3">
                    <pay-off-data
                        v-if="vehicleQuote.payoffQuote && !isSellAtHome"
                        :payoff-quote="vehicleQuote.payoffQuote"
                        :trade-vehicle-id="vehicleQuote.userVehicleId"
                        :dealer-id="dealerId"
                        :active="active"
                        :is-lease-pay-off-enabled="isLeasePayoffEnabled"
                        :trade-type="tradeType"
                        @refresh="fetchTradeOfferDetails"
                    />
                </div>
            </div>
        </template>
    </section-template>
</template>
<script>
import SectionTemplate from "./SectionTemplate";
import api from "@/util/api";
import PayOffData from "./PayOffData";
import _ from "lodash";
import numeral from "numeral";
import TradeInVehicleHistory from "./TradeInVehicleHistory";
import DiscrepancyViewer from "./DiscrepancyViewer";
import DiscrepancyEditor from "./DiscrepancyEditor";
import moment from "moment";
import EventBus from "Util/eventBus";
import lodashGet from "lodash/get";
import { get } from "vuex-pathify";

export default {
    name: "TradeIn",
    components: {
        DiscrepancyViewer,
        DiscrepancyEditor,
        PayOffData,
        SectionTemplate,
        TradeInVehicleHistory,
    },
    props: {
        dealerId: {
            type: String,
            required: true,
        },
        userVehicleId: {
            type: String,
            required: true,
        },
        active: {
            type: Boolean,
            required: true,
        },
        isSellAtHome: {
            type: Boolean,
            required: false,
            default: false,
        },
    },
    data() {
        return {
            loading: true,
            valid: true,
            vehicleQuote: null,
            discrepancyEditorShowing: false,
            offerEditorShowing: false,
            editableOffer: null,
            editableExpirationDate: null,
            expirationMenu: false,
            expirationRules: [(v) => !!v || "Expiration date is required"],
            quoteAmountRules: [
                (v) => !!v || "Quote amount is required",
                (v) => {
                    if (!_.isNumber(v)) {
                        return "Quote must be a number";
                    }

                    if (v <= this.vehicleQuote.guaranteedOffer) {
                        const offer = numeral(this.vehicleQuote.guaranteedOffer).format("$0,0");
                        return `Quote must be higher than the CarSaver Guaranteed Offer of ${offer}`;
                    }

                    return true;
                },
            ],
        };
    },
    computed: {
        userDealerAccessList: get("loggedInUser/userDealerAccessList"),
        dealerName() {
            const locatedDealer = _.find(this.userDealerAccessList, ["id", this.dealerIds]);
            const result = !_.isNil(locatedDealer) ? locatedDealer.name : "";
            return result;
        },
        dealerIds() {
            const result = this.$route.query.dealerIds;
            return result;
        },
        selectedFeatures() {
            return _.get(this.vehicleQuote, "selectedFeatures") || null;
        },
        hasSelectedFeatures() {
            return !_.isEmpty(this.selectedFeatures);
        },
        questions() {
            return _.get(this.vehicleQuote, "questions") || null;
        },
        hasQuestions() {
            return !_.isNil(this.questions);
        },
        hasCustomerDealEditAccess() {
            return this.$acl.hasDealerPermission(this.dealerId, "customer:deal:edit");
        },
        vehicleYear() {
            return _.get(this.vehicleQuote, "vehicle.year");
        },
        vehicleMake() {
            return _.get(this.vehicleQuote, "vehicle.make");
        },
        vehicleModel() {
            return _.get(this.vehicleQuote, "vehicle.model");
        },
        vehicleTrim() {
            return _.get(this.vehicleQuote, "vehicle.trim");
        },
        isLeaseReturn() {
            return _.get(this.vehicleQuote, "purchaseType") === "LEASE";
        },
        tradeType() {
            return lodashGet(this.vehicleQuote, "tradeType");
        },
        isLeasePayoffEnabled() {
            return lodashGet(this.vehicleQuote, "isLeasePayoffEnabled", false) || false;
        },
    },
    created() {
        this.fetchTradeOfferDetails();
    },
    methods: {
        allowedDates: (val) => moment(val).isAfter(moment()),
        toggleEditing() {
            this.offerEditorShowing = !this.offerEditorShowing;
        },
        fetchTradeOfferDetails() {
            return api
                .get("/trade/offer", {
                    vehicleId: this.userVehicleId,
                    dealerId: this.dealerId,
                })
                .then((response) => {
                    this.vehicleQuote = response.data;
                    this.editableOffer = _.get(this.vehicleQuote, "totalTradeValue");
                    this.editableExpirationDate = _.get(this.vehicleQuote, "expirationDate");
                    this.loading = false;
                })
                .catch((error) => {
                    console.error("error =", error);
                    this.loading = false;
                });
        },
        validate() {
            if (this.$refs.form.validate()) {
                this.loading = true;
                api.post(`/dealer/${this.dealerId}/vehicle/quote`, {
                    dealerId: this.dealerId,
                    userId: this.vehicleQuote.userId,
                    expiration: this.editableExpirationDate,
                    vin: this.vehicleQuote.vin,
                    quoteAmount: this.editableOffer,
                    id: this.vehicleQuote.id,
                }).then((response) => {
                    this.vehicleQuote = response.data;
                    this.offerEditorShowing = false;
                    this.loading = false;
                    EventBus.$emit("refresh-deals");
                    this.$emit("quote-updated");
                });
            }
        },
        showDiscrepancyEditor() {
            this.discrepancyEditorShowing = true;
        },
        hideDiscrepancyEditor() {
            this.discrepancyEditorShowing = false;
        },
        handleSavedDiscrepancy() {
            this.loading = true;
            this.fetchTradeOfferDetails();
            this.$emit("quote-updated");
        },
        handleRemovedDiscrepancy() {
            this.hideDiscrepancyEditor();
            this.loading = true;
            this.fetchTradeOfferDetails();
            this.$emit("quote-updated");
        },
        handleCancelDiscrepancyEditor() {
            this.hideDiscrepancyEditor();
        },
    },
};
</script>
<style lang="scss">
@import "~vuetify/src/styles/settings/_variables";
.sell-at-home-disclaimer {
    font-size: 13px;
    color: grey;
}
#trade-in {
    .trade-ymm {
        font-size: 16px;
    }
    .trade-label {
        font-size: 14px;
    }
    .value {
        font-size: 14px;
    }
    .features {
        padding-left: 15px;
        list-style-type: none;
    }
}
</style>
