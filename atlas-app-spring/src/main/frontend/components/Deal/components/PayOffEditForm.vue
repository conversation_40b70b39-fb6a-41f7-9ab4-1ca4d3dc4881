<template>
    <v-form ref="payoffForm">
        <div>
            <v-text-field
                ref="financierName"
                v-model="form.financierName"
                label="Finance Company *"
                :rules="[rules.required]"
                outlined
                dense
            ></v-text-field>

            <v-select
                ref="purchaseType"
                v-model="form.purchaseType"
                :items="purchaseTypes"
                :rules="[rules.required]"
                label="Finance Type *"
                item-text="text"
                item-value="value"
                outlined
                dense
            ></v-select>

            <v-menu
                ref="menu"
                v-model="menu"
                :close-on-content-click="false"
                :return-value.sync="form.payoffExpiration"
                min-width="auto"
            >
                <template #activator="{ on, attrs }">
                    <v-text-field
                        ref="payoffExpiration"
                        v-model="form.payoffExpiration"
                        :rules="[rules.required]"
                        label="Payoff Good Through *"
                        append-icon="mdi-calendar"
                        readonly
                        v-bind="attrs"
                        outlined
                        dense
                        v-on="on"
                    ></v-text-field>
                </template>
                <v-date-picker v-model="form.payoffExpiration" no-title :min="minDate" :max="maxDate">
                    <v-spacer></v-spacer>
                    <v-btn text color="primary" @click="menu = false"> Cancel </v-btn>
                    <v-btn text color="primary" @click="$refs.menu.save(form.payoffExpiration)"> OK </v-btn>
                </v-date-picker>
            </v-menu>

            <v-text-field
                ref="balance"
                v-model="form.balance"
                label="Payoff Amount *"
                :rules="[rules.required, rules.numericDecimal]"
                outlined
                dense
            ></v-text-field>

            <v-text-field
                ref="monthlyPayment"
                v-model="form.monthlyPayment"
                label="Payment Amount *"
                :rules="[rules.required, rules.numericDecimal]"
                outlined
                dense
            ></v-text-field>

            <v-text-field
                ref="remainingPayments"
                v-model="form.remainingPayments"
                label="Payments Remaining"
                :rules="[rules.numeric]"
                outlined
                dense
            ></v-text-field>
        </div>
        <div class="d-flex justify-end">
            <v-btn dark color="green" small :loading="loading" @click="submitForm">Submit</v-btn>
        </div>
    </v-form>
</template>
<script>
import api from "Util/api";
import moment from "moment";

export default {
    name: "PayOffDataEditForm",
    props: {
        tradeVehicleId: {
            type: String,
            required: true,
        },
        payoffQuote: {
            type: Object,
            required: true,
        },
        dealerId: {
            type: String,
            required: true,
        },
    },
    data() {
        let today = moment();
        const minDate = today.add(1, "d").format("YYYY-MM-DD");
        const maxDate = today.add(60, "d").format("YYYY-MM-DD");

        return {
            form: {
                financierName: null,
                purchaseType: null,
                payoffExpiration: null,
                monthlyPayment: null,
                balance: null,
                remainingPayments: null,
                financierId: null,
            },
            loading: false,
            purchaseTypes: [
                { text: "Lease", value: "LEASE" },
                { text: "Finance", value: "FINANCE" },
            ],
            minDate: minDate,
            maxDate: maxDate,
            menu: false,
            formHasErrors: false,
            rules: {
                required: (value) => !!value || "This field is required.",
                numeric: (value) =>
                    new RegExp(/^\d*$/).test(value) || "Value must be an integer, no commas or negative values.",
                numericDecimal: (value) =>
                    new RegExp(/^[0-9]\d*(\.\d+)?$/).test(value) ||
                    "Value must be an integer or decimal, no commas or negative values.",
            },
        };
    },
    created() {
        if (this.payoffQuote) {
            this.form.financierName = _.get(this.payoffQuote, "financeCompany");
            this.form.payoffExpiration = _.get(this.payoffQuote, "payoffGoodThrough");
            this.form.monthlyPayment = _.get(this.payoffQuote, "monthlyPayment");
            this.form.balance = _.get(this.payoffQuote, "payoffAmount");
            this.form.purchaseType = _.get(this.payoffQuote, "financeType");
            this.form.remainingPayments = _.get(this.payoffQuote, "paymentsRemaining");
            this.form.financierId = _.get(this.payoffQuote, "financierId");
        }
    },
    methods: {
        submitForm() {
            this.loading = true;
            this.formHasErrors = !this.$refs.payoffForm.validate(true);

            if (this.formHasErrors) {
                this.loading = false;
                this.$toast.error("Check to make sure the form is correct.");
            } else {
                this.patchTradePayoff()
                    .then((response) => {
                        this.loading = false;
                        this.$toast.success("Successfully updated trade payoff!");
                        this.$emit("success");
                    })
                    .catch((error) => {
                        this.loading = false;
                        this.$toast.error("Error submitting trade payoff form!");
                    });
            }
        },
        patchTradePayoff() {
            const apiEndpoint = `/dealer/${this.dealerId}/users/trades/${this.tradeVehicleId}/payoff`;

            return api.patch(apiEndpoint, this.form);
        },
    },
};
</script>
