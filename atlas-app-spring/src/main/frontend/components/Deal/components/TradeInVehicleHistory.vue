<template>
    <div v-if="questions" class="d-flex flex-wrap mb-2 flex-column">
        <div class="trade-label mb-1">Vehicle History Questions:</div>
        <div class="d-flex flex-wrap justify-content-end flex-column">
            <div
                v-for="question in answeredQuestions"
                :key="question.questionId"
                class="d-flex justify-space-between value w-100 pb-1 border-row mb-1"
            >
                <span class="pr-1">
                    {{ question.questionText }}
                </span>
                <span class="font-weight-bold">
                    {{ selectedAnswerText(question) }}
                </span>
            </div>
        </div>
    </div>
</template>

<script>
import _ from "lodash";

export default {
    name: "TradeInVehicleHistory",

    props: {
        questions: {
            type: Array,
            required: true,
        },
    },
    computed: {
        /*
            in Upgrade/eCommerce the user is only asked a subset of the questions and they are all required,
            so only the questions with answers are relevant for display purposes
         */
        answeredQuestions() {
            if (_.isEmpty(this.questions)) {
                return [];
            }

            return _.filter(this.questions, (question) => {
                return (
                    !_.isNil(question) && !_.isNil(question.selectedAnswerCode) && question.selectedAnswerCode !== ""
                );
            });
        },
    },
    methods: {
        selectedAnswerText(question) {
            const selectedAnswer = _.chain(question.answers)
                .filter(["answerCode", question.selectedAnswerCode])
                .head()
                .value();

            return _.get(selectedAnswer, "answerText");
        },
    },
};
</script>
<style lang="scss">
.border-row {
    border-bottom: 1px solid $gray-300;
}
</style>
