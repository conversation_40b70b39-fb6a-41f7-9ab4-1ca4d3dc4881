<template>
    <div class="custom-timeline">
        <div
            v-for="(item, index) in items"
            :key="index"
            class="custom-timeline__item"
            :style="dense ? { marginBottom: '0px' } : {}"
        >
            <!-- Timeline Dot -->
            <div
                class="custom-timeline__dot"
                :style="{
                    width: dotSize + 'px',
                    height: dotSize + 'px',
                    backgroundColor: dotColor,
                }"
            ></div>

            <!-- Timeline Content -->
            <div class="custom-timeline__content">
                <slot name="content" :item="item" :index="index">
                    <div class="default-content">
                        <h4>{{ item.title }}</h4>
                        <p>{{ item.description }}</p>
                    </div>
                </slot>
            </div>

            <!-- Timeline Line -->
            <div class="custom-timeline__line" :style="{ backgroundColor: lineColor }"></div>
        </div>
    </div>
</template>

<script>
import { defineComponent } from "vue";

export default defineComponent({
    name: "CustomTimeline",
    props: {
        items: {
            type: Array,
            required: true,
            default: () => [],
        },
        dotSize: {
            type: [Number, String],
            default: 12,
        },
        dotColor: {
            type: String,
            default: "#4db6ac",
        },
        lineColor: {
            type: String,
            default: "#e0e0e0",
        },
        dense: {
            type: Boolean,
            default: false,
        },
    },
});
</script>

<style scoped lang="scss">
.custom-timeline {
    display: flex;
    flex-direction: column;
    position: relative;
    padding-left: 0px;
}

.custom-timeline__item {
    display: flex;
    position: relative;
}

.custom-timeline__dot {
    position: absolute;
    margin-top: 12px;
    border-radius: 50%;
    z-index: 1;
    left: -3px;
    transform: translateX(calc(50% - 1px));
}

.custom-timeline__content {
    padding: 6px 0;
    padding-left: 20px;
    flex: 1;
}

.custom-timeline__line {
    width: 2px;
    flex: 1;
    position: absolute;
    left: 0;
    left: 3px;
    top: 0;
    bottom: 0;
}

.custom-timeline__item:last-of-type .custom-timeline__line {
    bottom: unset;
    height: 40px;
}

.default-content {
    h4 {
        margin: 0;
        font-size: 16px;
        color: #212121;
    }
    p {
        margin: 0;
        font-size: 14px;
        color: #616161;
    }
}
</style>
