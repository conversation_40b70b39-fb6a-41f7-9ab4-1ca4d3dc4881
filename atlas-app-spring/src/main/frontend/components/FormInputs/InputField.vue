<template>
    <div>
        <v-text-field
            v-if="label"
            v-model="input"
            class="custom-input-field"
            :label="label"
            :placeholder="placeholder"
            :disabled="disabled"
            :hint="description || hint"
            :rules="[...formatRules, ...rules, isRequired]"
            :type="type"
            outlined
            dense
            :persistent-hint="persistentHint"
            hide-details="auto"
        ></v-text-field>

        <v-text-field
            v-else
            v-model="input"
            class="custom-input-field"
            :placeholder="placeholder"
            :disabled="disabled"
            :rules="[...formatRules, ...rules, isRequired]"
            :type="type"
            :hint="description || hint"
            outlined
            dense
            :persistent-hint="persistentHint"
            hide-details="auto"
        >
            <template #label>
                <slot />
            </template>
        </v-text-field>
    </div>
</template>
<script>
import { call } from "vuex-pathify";

export default {
    name: "InputField",
    props: {
        label: {
            type: String,
            required: false,
            default: "",
        },
        value: {
            type: [String, Number, null],
            required: false,
            default: "",
        },
        placeholder: {
            type: [String, Number, null],
            required: false,
            default: null,
        },
        hint: {
            type: String,
            required: false,
            default: "",
        },
        description: {
            type: String,
            required: false,
            default: "",
        },
        disabled: {
            type: Boolean,
            required: false,
            default: false,
        },
        id: {
            type: [String, Number, null],
            required: false,
            default: null,
        },
        type: {
            type: String,
            required: false,
            default: "text",
        },
        formatRules: {
            type: Array,
            default: () => [], // Array of functions
        },
        persistentHint: {
            type: Boolean,
            default: true,
        },
        required: {
            type: Boolean,
            required: false,
            default: false,
        },
        rules: {
            type: Array,
            required: false,
            default: () => [],
        },
    },
    data() {
        return {
            input: this.value,
        };
    },
    computed: {
        isRequired() {
            return this.required ? (v) => !!v || this.label + " is required" : () => true; // Return a function that always passes validation
        },
    },
    watch: {
        value(newValue) {
            this.input = newValue;
        },
        input(newValue) {
            this.$emit("input", newValue);
            this.$emit("update", { id: this.id, value: newValue });
            this.updateFormData({ key: this.id, value: newValue });
        },
    },
    methods: {
        updateFormData: call("pageConfigs/updateFormData"),
    },
};
</script>
<style lang="scss">
.custom-input-field {
    .v-input__control {
        .v-text-field__details {
            // Your styles here
            padding: 0 !important;
        }
    }
}
</style>
