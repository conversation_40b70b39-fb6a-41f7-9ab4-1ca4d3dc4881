<template>
    <div class="base-input-number">
        <v-text-field
            v-model="localValue"
            :label="label"
            :placeholder="placeholder"
            :disabled="disabled"
            :hint="hint"
            :rules="computedRules"
            :prepend-icon="prependIcon"
            :prepend-inner-icon="prependInnerIcon"
            :append-icon="appendIcon"
            :append-inner-icon="appendInnerIcon"
            type="number"
            outlined
            dense
            hide-details="auto"
            :persistent-hint="!!hint"
            @input="handleInput"
            @blur="handleBlur"
        />
    </div>
</template>

<script>
export default {
    name: "BaseInputNumber",
    props: {
        value: {
            type: [String, Number, null],
            required: false,
            default: null,
        },
        label: {
            type: String,
            required: false,
            default: "",
        },
        placeholder: {
            type: [String, Number, null],
            required: false,
            default: null,
        },
        hint: {
            type: String,
            required: false,
            default: "",
        },
        disabled: {
            type: Boolean,
            required: false,
            default: false,
        },
        required: {
            type: Boolean,
            required: false,
            default: false,
        },
        min: {
            type: [Number, null],
            required: false,
            default: null,
        },
        max: {
            type: [Number, null],
            required: false,
            default: null,
        },
        step: {
            type: [Number, String],
            required: false,
            default: 1,
        },
        rules: {
            type: Array,
            required: false,
            default: () => [],
        },
        prependInnerIcon: {
            type: String,
            required: false,
            default: null,
        },
        prependIcon: {
            type: String,
            required: false,
            default: null,
        },
        appendIcon: {
            type: String,
            required: false,
            default: null,
        },
        appendInnerIcon: {
            type: String,
            required: false,
            default: null,
        },
    },
    data() {
        return {
            localValue: this.value,
        };
    },
    computed: {
        computedRules() {
            const rules = [...this.rules];

            // Add required validation
            if (this.required) {
                rules.push((v) => {
                    if (v === null || v === undefined || v === "") {
                        return `${this.label || "Field"} is required`;
                    }
                    return true;
                });
            }

            // Add number validation
            rules.push((v) => {
                if (v !== null && v !== undefined && v !== "" && isNaN(Number(v))) {
                    return "Must be a valid number";
                }
                return true;
            });

            // Add min validation
            if (this.min !== null) {
                rules.push((v) => {
                    if (v !== null && v !== undefined && v !== "" && Number(v) < this.min) {
                        return `Must be at least ${this.min}`;
                    }
                    return true;
                });
            }

            // Add max validation
            if (this.max !== null) {
                rules.push((v) => {
                    if (v !== null && v !== undefined && v !== "" && Number(v) > this.max) {
                        return `Must be at most ${this.max}`;
                    }
                    return true;
                });
            }

            return rules;
        },
    },
    watch: {
        value(newValue) {
            this.localValue = newValue;
        },
    },
    methods: {
        handleInput(value) {
            this.localValue = value;
            this.$emit("input", value);
        },
        handleBlur() {
            this.$emit("blur", this.localValue);
        },
    },
};
</script>

<style lang="scss" scoped>
.base-input-number {
    .v-text-field {
        .v-input__control {
            .v-text-field__details {
                padding: 0 !important;
            }
        }
    }
}
</style>
