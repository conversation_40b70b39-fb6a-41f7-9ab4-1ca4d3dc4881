<template>
    <v-data-table :headers="getHeaders" hide-default-footer :items="displayItems" class="actionable-table pa-3">
        <template #top>
            <div class="custom-header d-flex justify-space-between align-center">
                <div class="d-flex align-center gap-10px">
                    <v-toolbar-title>{{ componentTitle }}</v-toolbar-title>
                    <v-tooltip v-if="componentTitleTooltip" bottom>
                        <template #activator="{ on }">
                            <v-icon color="#9E9E9E" size="20" v-on="on">mdi-information-outline</v-icon>
                        </template>
                        <span>{{ componentTitleTooltip }}</span>
                    </v-tooltip>
                </div>

                <slot name="table-header-action">
                    <v-btn outlined @click.stop="handleAdd">
                        <v-icon left small>mdi-plus</v-icon>
                        Add
                    </v-btn>
                </slot>
            </div>
        </template>
        <template #item.actions="{ item }">
            <v-icon
                v-for="actionItem in getActionItemList"
                :key="actionItem.type"
                small
                class="mr-2"
                @click="handleAction(actionItem.type, item.id)"
            >
                {{ actionItem.icon }}
            </v-icon>
        </template>
    </v-data-table>
</template>
<script>
import { MAP_TABLE_ACTION_TYPE_TO_ICONS } from "Util/ConfigManager/icons";
import { COMPONENTS_UTILITY } from "Util/ConfigManager/components";

export default {
    name: "ActionableTable",
    props: {
        columns: {
            type: Array,
            default: () => [],
        },
        // tableTitle: {
        //     type: String,
        //     required: false,
        //     default: "Table",
        // },
        componentTitle: {
            type: String,
            required: false,
            default: "Table",
        },
        componentTitleTooltip: {
            type: String,
            required: false,
            default: "",
        },
        tableBody: {
            type: Array,
            default: () => [],
        },
        actions: {
            type: Object,
            default: () => ({}),
        },
    },
    emits: ["add-new-item", "action"],
    computed: {
        getHeaders() {
            const getCols = this.columns.map((col) => ({
                text: col.header,
                sortable: col.sortEnabled,
                value: col.attribute,
                function: col.function,
                attribute: col.attribute,
            }));
            const getActionCol = {
                text: this.actions.header,
                sortable: false,
                value: "actions",
                width: 100,
            };
            const idCol = {
                text: "ID",
                sortable: false,
                value: "id",
                attribute: "id",
                align: " d-none",
            };
            return [idCol, ...getCols, getActionCol];
        },
        displayItems() {
            return this.tableBody;
        },
        getActionItemList() {
            return (
                this.actions?.functions?.map((action) => ({
                    type: action.type,
                    icon: MAP_TABLE_ACTION_TYPE_TO_ICONS[action.type] ?? "",
                    value: action.value,
                })) || []
            );
        },
    },
    methods: {
        handleAdd() {
            this.handleAction(COMPONENTS_UTILITY.ACTIONABLE_TABLE.ACTIONS.ADD);
        },
        handleAction(actionType, itemId = null) {
            this.$emit("action", actionType, itemId);
        },
    },
};
</script>

<style lang="scss" scoped>
.actionable-table {
    .v-toolbar__title {
        font-size: px2rem(16);
        font-weight: 500;
    }
    .gap-10px {
        gap: 10px;
    }
    .custom-header {
        margin: 10px 14px 16px 14px;
    }
}
</style>
