<template>
    <div v-if="isMultiple" class="config-form-builder-radio-button">
        <v-radio-group v-model="input" class="custom-radio-group">
            <v-row>
                <v-col v-for="(item, index) in items" :key="index + item.text" :sm="layoutBasedOnItems" cols="12">
                    <v-radio :label="item.text" :value="item.value" class="custom-radio-button"></v-radio>
                    <p v-if="item.description" class="description">{{ item.description }}</p>
                </v-col>
            </v-row>
        </v-radio-group>
    </div>
    <div v-else>
        <v-radio
            v-if="label"
            v-model="input"
            :label="label"
            :disabled="disabled"
            dense
            class="custom-radio-button"
        ></v-radio>
        <v-radio
            v-else
            v-model="input"
            :placeholder="placeholder"
            :disabled="disabled"
            dense
            class="custom-radio-button"
        >
            <template #label>
                <slot />
            </template>
        </v-radio>
    </div>
</template>
<script>
import { call } from "vuex-pathify";

export default {
    name: "RadioBtn",
    props: {
        label: {
            type: String,
            required: false,
            default: "",
        },
        value: {
            type: [Boolean, String, Number, null],
            required: false,
            default: "",
        },
        placeholder: {
            type: [String, Number, null],
            required: false,
            default: null,
        },
        hint: {
            type: String,
            required: false,
            default: "",
        },
        disabled: {
            type: Boolean,
            required: false,
            default: false,
        },
        id: {
            type: [String, Number, null],
            required: false,
            default: null,
        },
        type: {
            type: String,
            required: false,
            default: "text",
        },
        items: {
            type: Array,
            required: false,
            default: () => [],
        },
    },
    data() {
        return {
            input: this.value,
        };
    },
    computed: {
        isMultiple() {
            return this.items?.length > 0;
        },
        layoutBasedOnItems() {
            if (this.items?.length <= 2) {
                return 6;
            }
            return true;
        },
    },
    watch: {
        value(newValue) {
            this.input = newValue;
        },
        input(newValue) {
            this.$emit("input", newValue);
            this.updateFormData({ key: this.id, value: newValue });
        },
    },
    methods: {
        updateFormData: call("pageConfigs/updateFormData"),
    },
};
</script>

<style lang="scss">
.config-form-builder-radio-button {
    .description {
        font-size: px2rem(14);
        margin-left: 32px;
        color: var(--grey-grey-darken-2, #616161);
    }
    .custom-radio-button {
        .v-label {
            color: #424242 !important;
        }
    }
}
</style>
