<template>
    <div class="toggle-btn-input">
        <v-switch
            v-if="label"
            v-model="toggle"
            :dense="dense"
            inset
            :disabled="disabled"
            class="mt-0"
            :label="label"
            hide-details
        >
        </v-switch>
        <v-switch v-else v-model="toggle" inset :disabled="disabled" class="mt-0" hide-details>
            <template #label>
                <slot />
            </template>
        </v-switch>
        <p v-if="structuredDescription" class="structured-description">
            {{ structuredDescription }}
        </p>
    </div>
</template>
<script>
import { call } from "vuex-pathify";

export default {
    name: "ToggleBtn",
    props: {
        id: {
            type: String,
            required: false,
            default: "",
        },
        label: {
            type: String,
            required: false,
            default: "",
        },
        value: {
            type: [Boolean, String],
            required: false,
            default: true,
        },
        disabled: {
            type: Boolean,
            required: false,
            default: false,
        },
        description: {
            type: String,
            required: false,
            default: "",
        },
        dense: {
            type: Boolean,
            required: false,
            default: false,
        },
    },
    data() {
        return {
            toggle: this.value,
        };
    },
    computed: {
        structuredDescription() {
            if (this.description.includes("\\n")) {
                return this.description.split("\\n");
            }
            return this.description;
        },
    },
    watch: {
        value(newValue) {
            this.toggle = newValue;
        },
        toggle(newValue) {
            this.$emit("input", newValue);
            this.updateFormData({ key: this.id, value: newValue });
        },
    },
    methods: {
        updateFormData: call("pageConfigs/updateFormData"),
    },
};
</script>
<style lang="scss">
@import "~vuetify/src/styles/settings/_variables";

.toggle-btn-input {
    display: flex;
    flex-direction: column;
    gap: 4px;
    .structured-description {
        margin-left: 56px;
        max-width: 870px;
        margin-bottom: 0;
        color: var(--grey-grey-darken-3, #424242);
        @media #{map-get($display-breakpoints, 'sm-and-down')} {
            margin-left: 56px;
            max-width: 100%;
            margin-bottom: 0;
        }
    }
    .v-input__slot {
        width: fit-content;
    }
}
</style>
