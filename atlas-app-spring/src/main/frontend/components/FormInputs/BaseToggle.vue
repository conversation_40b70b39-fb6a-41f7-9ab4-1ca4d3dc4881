<template>
    <div class="base-toggle">
        <v-switch
            v-if="label"
            v-model="internalValue"
            :dense="dense"
            :inset="inset"
            :disabled="disabled"
            :loading="loading"
            class="mt-0"
            :label="label"
            :hide-details="hideDetails"
            :color="color"
            :class="{ 'cursor-not-allowed': disabled }"
        >
        </v-switch>
        <v-switch
            v-else
            v-model="internalValue"
            :inset="inset"
            :disabled="disabled"
            :loading="loading"
            class="mt-0"
            :hide-details="hideDetails"
            :color="color"
            :class="{ 'cursor-not-allowed': disabled }"
        >
            <template #label>
                <slot />
            </template>
        </v-switch>
        <p v-if="description" class="description">
            {{ description }}
        </p>
    </div>
</template>

<script>
export default {
    name: "BaseToggle",
    props: {
        value: {
            type: [Boolean, String],
            required: false,
            default: false,
        },
        label: {
            type: String,
            required: false,
            default: "",
        },
        disabled: {
            type: Boolean,
            required: false,
            default: false,
        },
        loading: {
            type: Boolean,
            required: false,
            default: false,
        },
        description: {
            type: String,
            required: false,
            default: "",
        },
        dense: {
            type: Boolean,
            required: false,
            default: false,
        },
        inset: {
            type: Boolean,
            required: false,
            default: true,
        },
        hideDetails: {
            type: Boolean,
            required: false,
            default: true,
        },
        color: {
            type: String,
            required: false,
            default: "primary",
        },
    },
    computed: {
        internalValue: {
            get() {
                return this.value;
            },
            set(newValue) {
                this.$emit("input", newValue);
            },
        },
    },
};
</script>

<style lang="scss" scoped>
@import "~vuetify/src/styles/settings/_variables";

.base-toggle {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .description {
        margin-left: 56px;
        max-width: 870px;
        margin-bottom: 0;
        color: var(--grey-grey-darken-3, #424242);
        font-size: 14px;

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
            margin-left: 56px;
            max-width: 100%;
            margin-bottom: 0;
        }
    }

    .cursor-not-allowed {
        cursor: not-allowed;
    }

    :deep(.v-input__slot) {
        width: fit-content;
    }

    // When disabled, update cursor for the entire switch area
    &:has(.v-switch.v-input--is-disabled) {
        cursor: not-allowed;
    }

    // Loading state styling
    :deep(.v-switch.v-input--is-loading) {
        .v-input--switch__track {
            opacity: 0.6;
        }
    }
}
</style>
