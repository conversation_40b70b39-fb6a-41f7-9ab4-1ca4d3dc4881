<template>
    <v-row>
        <v-col sm="6" cols="12">
            <FormInput class="mt-3" :input="group[0]" :base-id="id" @input="typeInputHandler" />
        </v-col>
        <v-col sm="6" cols="12">
            <FormInput
                class="mt-3"
                :input="group[1]"
                :disabled="disableTerm"
                :base-id="id"
                :value="termOverrideValue"
                @input="termInputHandler"
            />
        </v-col>
    </v-row>
</template>

<script>
import { defineComponent } from "vue";
import { call } from "vuex-pathify";
import FormInput from "Components/ConfigFormBuilder/components/FormInput.vue";

export default defineComponent({
    name: "GroupedTerm",
    components: {
        FormInput,
    },
    props: {
        id: {
            type: String,
            required: false,
            default: "",
        },
        label: {
            type: String,
            default: "",
        },
        // Deprecated
        default: {
            type: String,
            default: "false",
        },
        disabled: {
            type: Boolean,
            required: false,
            default: false,
        },
        group: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            termEnabledTypes: ["FINANCE", "LEASE"],
            defaultTerm: "-1",
            termInput: null,
            typeInput: null,
            initState: {
                term: {
                    value: null,
                    set: false,
                },
                type: {
                    value: null,
                    set: false,
                },
            },
            termOverrideValue: null,
        };
    },
    computed: {
        allowTerm() {
            return this.termEnabledTypes.includes(this.typeInput);
        },
        disableTerm() {
            return !this.allowTerm;
        },
    },
    watch: {
        allowTerm(newValue) {
            if (!newValue) {
                this.termOverrideValue = this.defaultTerm;
            }
        },
        typeInput: {
            handler(newVal) {
                if (!this.initState.type.set) {
                    this.initState.type.set = true;
                    this.initState.type.value = newVal;
                } else if (newVal === this.initState.type.value) {
                    this.termOverrideValue = this.initState.term.value;
                } else {
                    this.termOverrideValue = this.defaultTerm;
                }
            },
            immediate: false,
        },
        termInput: {
            handler(newVal) {
                if (!this.initState.term.set) {
                    this.initState.term.set = true;
                    this.initState.term.value = newVal;
                }

                this.termOverrideValue = newVal;
            },
            immediate: false,
        },
    },
    methods: {
        updateFormData: call("pageConfigs/updateFormData"),
        termInputHandler(value) {
            this.termInput = value;
        },
        typeInputHandler(value) {
            this.typeInput = value;
        },
    },
});
</script>
