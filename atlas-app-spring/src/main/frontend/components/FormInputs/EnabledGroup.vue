<template>
    <v-row>
        <v-col :sm="controllerColLayout" cols="12">
            <ToggleBtn
                v-model="toggle"
                :label="label"
                :disabled="disabled"
                :rules="rules"
                :required="required"
                @update="handleUpdateInput"
            />
        </v-col>
        <v-col
            v-for="input in group"
            :key="input.key"
            :class="{ 'enabled-group-column': formColLayout === 12 }"
            :sm="formColLayout"
            cols="12"
        >
            <FormInput
                :input="input"
                :disabled="enactive"
                :required="active"
                :persistent-hint="input.persistentHint"
                :form-name="formName"
            />
        </v-col>
    </v-row>
</template>

<script>
import { defineComponent } from "vue";
import FormInput from "Components/ConfigFormBuilder/components/FormInput.vue";
import ToggleBtn from "Components/FormInputs/ToggleBtn.vue";
import { call, get } from "vuex-pathify";

export default defineComponent({
    name: "EnabledGroup",
    components: {
        ToggleBtn,
        FormInput,
    },
    props: {
        id: {
            type: String,
            required: false,
            default: "",
        },
        label: {
            type: String,
            default: "",
        },
        default: {
            type: String,
            default: "false",
        },
        disabled: {
            type: Boolean,
            required: false,
            default: false,
        },
        group: {
            type: Array,
            default: () => [],
        },
        value: {
            type: [Boolean, String],
            required: false,
            default: null,
        },
        rules: {
            type: Array,
            default: () => [],
        },
        required: {
            type: Boolean,
            default: false,
        },
        singleRow: {
            // If true, the group will be displayed in a single row with
            // the toggle at col-sm-6 and the input wrapped in a col-sm-6
            type: Boolean,
            default: false,
        },
        formName: {
            type: String,
            default: null,
        },
    },
    data() {
        return {
            toggle: false,
        };
    },
    computed: {
        getValidateFormByName: get("pageConfigs/getValidateFormByName"),
        active() {
            return this.disabled === false && this.toggle === true;
        },
        enactive() {
            return this.disabled === true || this.toggle === false;
        },
        doesGroupHaveTypeInput() {
            return this.group.some((input) => input.type === "INPUT");
        },
        controllerColLayout() {
            if (this.group.length === 1 || this.singleRow) return 6;
            return 12;
        },
        formColLayout() {
            if (this.singleRow) return 3;
            if (this.controllerColLayout === 6) return 6;
            if (this.doesGroupHaveTypeInput) return 12;
            return 6;
        },
        isFormValid() {
            return this.getValidateFormByName(this.formName);
        },
    },
    watch: {
        toggle: {
            handler(newValue) {
                if (newValue === false) {
                    this.clearValidation();
                } else if (this.formName && this.$refs[this.formName]) {
                    this.$refs[this.formName].validate();
                }
                if (this.id) this.updateFormData({ key: this.id, value: newValue });
            },
            deep: true,
        },
    },
    mounted() {
        this.initToggle();
    },
    methods: {
        updateFormData: call("pageConfigs/updateFormData"),
        removeValidateForm: call("pageConfigs/removeValidateForm"),
        initToggle() {
            if (typeof this.value === "boolean") {
                this.toggle = this.value;
            } else {
                this.toggle = this.default === "true";
            }
        },
        handleUpdateInput(value) {
            this.toggle = value;
            if (value === false) {
                this.clearValidation();
            }
            if (this.id) this.$emit("input", value);
        },
        clearValidation() {
            if (this.formName && this.$refs[this.formName]) {
                this.removeValidateForm(this.formName);
                this.$refs[this.formName].resetValidation();
            }
        },
    },
});
</script>

<style scoped>
.enabled-group-column {
    padding-top: 4px !important;
    padding-bottom: 4px !important;
}
</style>
