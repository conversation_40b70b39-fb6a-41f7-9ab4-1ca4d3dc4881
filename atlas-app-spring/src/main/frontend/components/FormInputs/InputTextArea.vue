<template>
    <v-textarea
        :id="id"
        v-model="input"
        :rules="rules"
        :label="label"
        :rows="rows"
        :disabled="disabled"
        outlined
        dense
        :hide-details="hideDetails"
    />
</template>

<script>
import { defineComponent } from "vue";
import { call } from "vuex-pathify";

export default defineComponent({
    name: "InputTextarea",
    props: {
        id: {
            type: String,
            required: true,
        },
        value: {
            type: String,
            required: false,
            default: "",
        },
        hideDetails: {
            type: [Boolean, String],
            default: false,
        },
        rules: {
            type: Array,
            default: () => [],
        },
        label: {
            type: String,
            required: false,
            default: "",
        },
        rows: {
            type: [Number, String],
            default: 4,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            input: "",
        };
    },
    watch: {
        value: {
            handler(newValue) {
                this.input = newValue;
            },
            immediate: true,
        },
        input(newValue) {
            this.$emit("input", newValue);
            this.$emit("update", { id: this.id, value: newValue });
            this.updateFormData({ key: this.id, value: newValue });
        },
    },
    methods: {
        updateFormData: call("pageConfigs/updateFormData"),
    },
});
</script>
