<template>
    <v-select
        v-if="label"
        v-model="select"
        :items="items"
        :label="label"
        :disabled="disabled"
        :item-text="itemText"
        outlined
        dense
        class="custom-dropdown"
        :hide-details="hideDetails"
    >
    </v-select>

    <v-select v-else v-model="select" :items="items" :disabled="disabled" outlined dense class="custom-dropdown">
        <template #label>
            <slot />
        </template>
    </v-select>
</template>
<script>
import { call } from "vuex-pathify";

export default {
    name: "Dropdown",
    props: {
        id: {
            type: String,
            required: true,
        },
        label: {
            type: String,
            required: false,
            default: "",
        },
        items: {
            type: Array,
            required: true,
            default: () => [],
        },
        value: {
            type: [String, Number, null],
            default: null,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        clearValue: {
            type: Boolean,
            default: null,
        },
        itemText: {
            type: String,
            default: "text",
        },
        hideDetails: {
            type: <PERSON>olean,
            default: false,
        },
    },
    data() {
        return {
            select: this.value,
        };
    },
    computed: {
        normalizedValue() {
            const value = this.value;
            return this.normalizeValue(value);
        },
    },
    watch: {
        value(newValue) {
            this.select = newValue;
        },
        select(newValue) {
            let value = this.clearValue ? null : newValue;

            this.$emit("input", value);
            this.updateFormData({ key: this.id, value: value });
        },
        clearValue(newValue) {
            if (newValue) {
                this.select = null;
            }
        },
    },
    created() {
        this.init();
    },
    methods: {
        updateFormData: call("pageConfigs/updateFormData"),
        init() {
            this.$emit("input", this.select);
            this.select = this.normalizedValue;
            this.updateFormData({ key: this.id, value: this.select });
        },
        normalizeValue(value) {
            let normValue = value;

            if (typeof normValue === "number") {
                normValue = normValue.toString();
            }
            if (typeof normValue === "string") {
                normValue = normValue.trim();
            }

            return normValue;
        },
    },
};
</script>
<style lang="scss">
.custom-dropdown {
    .v-input__control {
        .v-text-field__details {
            // Your styles here
            padding: 0 !important;
        }
    }
}
</style>
