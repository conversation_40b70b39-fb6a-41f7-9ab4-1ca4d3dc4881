<template>
    <div class="checkbox-group d-flex flex-wrap">
        <Checkbox
            v-for="item in items"
            :key="item.text"
            class="checkbox-wrapper"
            :label="item.text"
            :value="selected?.includes(item.value)"
            :value-text="item.value"
            :disabled="item.disabled || false"
            :hint="item.description"
            :persistent-hint="!!item.description"
            @change="handleInputValueChange"
        />
        <Checkbox
            key="All"
            class="checkbox-wrapper"
            label="All"
            :value="checkIfAllSelected"
            @change="handleSelectAllToggle"
        />
    </div>
</template>
<script>
import Checkbox from "Components/FormInputs/Checkbox.vue";
import { call } from "vuex-pathify";

export default {
    name: "CheckboxGroup",
    components: {
        Checkbox,
    },
    props: {
        id: {
            type: String,
            required: true,
        },
        items: {
            type: Array,
            required: true,
        },
        value: {
            type: Array,
            required: true,
        },
    },
    data() {
        return {
            selected: [],
        };
    },
    computed: {
        enabledItems() {
            return this.items.filter((item) => !item.disabled);
        },
        checkIfAllSelected() {
            return (
                this.enabledItems.length ===
                    this.selected.filter((value) => this.enabledItems.some((item) => item.value === value)).length &&
                this.enabledItems.length > 0
            );
        },
    },
    watch: {
        selected: {
            handler() {
                this.$emit("input", this.selected);
                this.updateFormData({ key: this.id, value: this.selected });
            },
            deep: true,
        },
        value: {
            handler() {
                if (Array.isArray(this.value)) {
                    this.selected = this.value;
                }
            },
            immediate: true,
            deep: true,
        },
    },
    created() {
        // this.selected = this.items.filter((item) => item.value).map((mapItem) => mapItem.value);
    },
    methods: {
        updateFormData: call("pageConfigs/updateFormData"),
        selectAll() {
            // Only select enabled items, keep any existing disabled items that were already selected
            const enabledValues = this.enabledItems.map((mapItem) => mapItem.value);
            const disabledSelectedValues = this.selected.filter((value) =>
                this.items.some((item) => item.value === value && item.disabled)
            );
            this.selected = [...enabledValues, ...disabledSelectedValues];
        },
        deselectAll() {
            // Only deselect enabled items, keep any disabled items that were selected
            const disabledSelectedValues = this.selected.filter((value) =>
                this.items.some((item) => item.value === value && item.disabled)
            );
            this.selected = disabledSelectedValues;
        },
        handleSelectAllToggle({ state }) {
            if (state) this.selectAll();
            else this.deselectAll();
        },
        handleInputValueChange({ value }) {
            // Check if the item is disabled - if so, don't allow changes
            const item = this.items.find((item) => item.value === value);
            if (item && item.disabled) {
                return;
            }

            if (this.selected.includes(value)) {
                this.selected = this.selected.filter((item) => item !== value);
            } else {
                this.selected = [...this.selected, value];
            }
        },
    },
};
</script>
<style lang="scss" scoped>
@import "~vuetify/src/styles/settings/_variables";

.checkbox-group {
    gap: 12px;
    margin-top: -16px;
}
.checkbox-wrapper {
    width: 100%;
    :deep(.v-messages__message) {
        white-space: normal;
    }

    @media #{map-get($display-breakpoints, 'sm-and-up')} {
        width: calc(25% - 9px);
    }
}
</style>
