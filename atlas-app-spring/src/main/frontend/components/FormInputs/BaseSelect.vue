<template>
    <div class="base-select">
        <v-select
            v-model="localValue"
            :items="items"
            :label="label"
            :placeholder="placeholder"
            :disabled="disabled"
            :hint="hint"
            :rules="computedRules"
            :multiple="multiple"
            :clearable="clearable"
            :item-text="itemText"
            :item-value="itemValue"
            outlined
            dense
            hide-details="auto"
            :persistent-hint="!!hint"
            @input="handleInput"
            @blur="handleBlur"
        />
    </div>
</template>

<script>
export default {
    name: "BaseSelect",
    props: {
        value: {
            type: [String, Number, Array, null],
            required: false,
            default: null,
        },
        items: {
            type: Array,
            required: true,
            default: () => [],
        },
        label: {
            type: String,
            required: false,
            default: "",
        },
        placeholder: {
            type: String,
            required: false,
            default: "",
        },
        hint: {
            type: String,
            required: false,
            default: "",
        },
        disabled: {
            type: Boolean,
            required: false,
            default: false,
        },
        required: {
            type: Boolean,
            required: false,
            default: false,
        },
        multiple: {
            type: Boolean,
            required: false,
            default: false,
        },
        clearable: {
            type: Boolean,
            required: false,
            default: false,
        },
        itemText: {
            type: [String, Function],
            required: false,
            default: "text",
        },
        itemValue: {
            type: [String, Function],
            required: false,
            default: "value",
        },
        rules: {
            type: Array,
            required: false,
            default: () => [],
        },
    },
    data() {
        return {
            localValue: this.value,
        };
    },
    computed: {
        computedRules() {
            const rules = [...this.rules];

            // Add required validation
            if (this.required) {
                rules.push((v) => {
                    if (this.multiple) {
                        return (v && v.length > 0) || `${this.label || "Field"} is required`;
                    } else {
                        return (v !== null && v !== undefined && v !== "") || `${this.label || "Field"} is required`;
                    }
                });
            }

            return rules;
        },
    },
    watch: {
        value: {
            handler(newValue) {
                this.localValue = this.normalizeValue(newValue);
            },
            immediate: true,
        },
        items: {
            handler() {
                this.localValue = this.normalizeValue(this.value);
            },
            deep: true, // If items can be an array of objects
        },
    },
    methods: {
        handleInput(value) {
            this.localValue = value;
            this.$emit("input", value);
        },
        handleBlur() {
            this.$emit("blur", this.localValue);
        },
        normalizeValue(value) {
            // Handle type mismatches between value and items
            if (value === null || value === undefined) {
                return value;
            }

            // If we have items and a value, try to match the value type to the items
            if (this.items && this.items.length > 0) {
                const firstItemValue = this.items[0][this.itemValue];
                const firstItemValueType = typeof firstItemValue;
                const valueType = typeof value;

                // Convert string to number if items expect numbers
                if (valueType === "string" && firstItemValueType === "number") {
                    const numValue = Number(value);
                    if (!isNaN(numValue)) {
                        return numValue;
                    }
                }

                // Convert number to string if items expect strings
                if (valueType === "number" && firstItemValueType === "string") {
                    return String(value);
                }
            }

            return value;
        },
    },
};
</script>

<style lang="scss" scoped>
.base-select {
    .v-select {
        .v-input__control {
            .v-text-field__details {
                padding: 0 !important;
            }
        }
    }
}
</style>
