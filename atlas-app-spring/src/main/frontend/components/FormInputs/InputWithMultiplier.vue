<template>
    <!--    <v-container fluid></v-container>-->
    <v-row>
        <v-col class="d-flex align-baseline">
            <InputField
                :id="id"
                class="input-field"
                :label="label"
                :value.sync="input"
                :placeholder="placeholder"
                :disabled="disabled"
                type="Number"
                @update="handleUpdate"
            >
            </InputField>
            <div class="pointer-icon px-4">
                <v-icon>mdi-arrow-right</v-icon>
            </div>
            <InputField class="input-field" :label="resultLabel" :value="multiplierResult" disabled> </InputField>
        </v-col>
    </v-row>
</template>
<script>
import InputField from "Components/FormInputs/InputField";
export default {
    name: "InputWithMultiplier",
    components: {
        InputField,
    },
    props: {
        label: {
            type: String,
            required: false,
            default: "",
        },
        resultLabel: {
            type: String,
            required: false,
            default: "",
        },
        value: {
            type: [String, Number, null],
            required: false,
            default: null,
        },
        placeholder: {
            type: [String, Number, null],
            required: false,
            default: null,
        },
        hint: {
            type: String,
            required: false,
            default: "",
        },
        multiplier: {
            type: [Number, String],
            required: false,
            default: 1,
        },
        id: {
            type: String,
            required: true,
        },
        disabled: {
            type: Boolean,
            required: false,
            default: false,
        },
    },
    data() {
        return {
            input: this.value,
        };
    },
    computed: {
        multiplierResult() {
            const valueToNumber = Number(this.input);

            if (isNaN(valueToNumber)) return "unable to calculate";

            return +(this.input * this.multiplier).toFixed(4) + "%";
        },
    },
    watch: {
        value(newValue) {
            this.input = newValue;
        },
    },
    methods: {
        handleUpdate(payload) {
            if (payload.id === this.id) {
                this.input = payload.value;
            }
        },
    },
};
</script>
<style lang="scss" scoped>
.input-field {
    width: 100%;
    max-width: 50%;
}
</style>
