<template>
    <v-sheet class="program-support py-0 mb-3" elevation="2">
        <v-container>
            <v-row class="pb-0">
                <v-col>
                    <p class="caption font-weight-bold">
                        {{ program.name }}
                        <v-chip x-small class="float-right">{{ program.status }}</v-chip>
                    </p>
                </v-col>
            </v-row>

            <v-divider v-if="program.successManager">inset</v-divider>
            <v-row v-if="program.successManager" class="py-4">
                <v-col cols="3" class="body-2">Success Manager</v-col>
                <v-col cols="9" class="body-2">
                    <span v-if="program.successManager">{{ program.successManager.name }}</span>
                </v-col>
            </v-row>

            <v-divider v-if="program.accountManager">inset</v-divider>
            <v-row v-if="program.accountManager" class="py-4">
                <v-col cols="3" class="body-2">Account Manager</v-col>
                <v-col cols="9" class="body-2">
                    <span v-if="program.accountManager">{{ program.accountManager.name }}</span>
                </v-col>
            </v-row>

            <v-divider v-if="program.supportEmail || program.supportPhone">inset</v-divider>
            <v-row v-if="program.supportEmail || program.supportPhone" class="py-4">
                <v-col cols="2" class="body-2">Support</v-col>
                <v-col cols="4" class="body-2">
                    <a :href="`mailto:${program.supportEmail}`">{{ program.supportEmail }}</a>
                </v-col>
                <v-col cols="4" class="body-2">
                    <a :href="`tel:${program.supportPhone}`">{{ program.supportPhone | phoneFormatter }}</a>
                </v-col>
            </v-row>

            <div v-if="showCarsaverFAndI && !isBoostProgram">
                <v-divider>inset</v-divider>
                <v-row class="py-4">
                    <v-col cols="auto" class="body-2">CarSaver Protection Products</v-col>
                    <div class="d-flex justify-space-between align-center ml-auto p-2 px-3">
                        <v-switch
                            v-model="isCarsaverFAndIEnabled"
                            :disabled="!isAdminUser"
                            @change="carsaverFAndIChanged"
                        ></v-switch>
                    </div>
                </v-row>
            </div>

            <div v-if="isApplicationChatFeatureToggleEnabled">
                <!-- New chat with multiple providers -->
                <div>
                    <v-divider>inset</v-divider>
                    <chat :program="program" :dealer-id="dealerId" />
                </div>
            </div>

            <div v-if="isLibertyMutualFeatureEnabled">
                <v-divider>inset</v-divider>
                <v-row class="py-4">
                    <v-col cols="auto" class="body-2">Liberty Mutual</v-col>
                    <div class="d-flex justify-space-between align-center ml-auto p-2 px-3">
                        <v-switch v-model="isLibertyMutualEnabled" :disabled="!isAdminUser"></v-switch>
                    </div>
                </v-row>
            </div>

            <div v-if="isBuyAtHomeProgram">
                <div v-if="isFAndIProtectionFeatureEnabled">
                    <v-divider>inset</v-divider>
                    <v-row class="py-4">
                        <v-col cols="auto" class="body-2">F&I Protection Products</v-col>
                        <div class="d-flex justify-space-between align-center ml-auto p-2 px-3">
                            <v-switch
                                v-model="protectionProduct"
                                @change="handleProtectionProductGlobalToggle"
                            ></v-switch>
                        </div>
                        <v-col v-if="protectionProduct" cols="12" class="fandi-protection-products">
                            <div class="mb-3 protection-label">
                                <span>* </span>
                                Select your protection products provider:
                            </div>
                            <v-radio-group
                                v-model="protectionProductSelects"
                                class="protection-group"
                                @change="protectionProductChanged"
                            >
                                <v-radio label="NESNA" value="nesna" color="#212121"></v-radio>
                                <div
                                    v-if="protectionProductSelects === 'nesna' && !isNesnaProductsConfigured"
                                    class="nesna-description"
                                >
                                    <v-icon dense color="red">mdi-alert</v-icon>
                                    <a :href="`/protection-products?dealerIds=${dealerIds}`"
                                        >Please configure your protection product</a
                                    >
                                </div>
                                <v-radio value="routeOne" color="#212121">
                                    <template #label>
                                        <div class="d-flex align-center">
                                            <p>Route One</p>
                                            <v-tooltip top>
                                                <template #activator="{ on, attrs }">
                                                    <v-btn icon v-bind="attrs" v-on="on">
                                                        <v-icon dense> mdi-information-outline </v-icon>
                                                    </v-btn>
                                                </template>
                                                <span>
                                                    Protection products through Route One requires <br />
                                                    a monthly subscription. Please reach out to your <br />
                                                    performance manager for more information.
                                                </span>
                                            </v-tooltip>
                                        </div>
                                    </template>
                                </v-radio>
                            </v-radio-group>
                        </v-col>
                    </v-row>
                </div>

                <div v-else-if="isRouteOneFAndIFeatureEnabled">
                    <v-divider>inset</v-divider>
                    <v-row class="py-4">
                        <v-col cols="auto" class="body-2">RouteOne F & I Menu</v-col>
                        <div class="d-flex justify-space-between align-center ml-auto p-2 px-3">
                            <v-switch v-model="isRouteOneFAndIEnabled"></v-switch>
                        </div>
                    </v-row>
                </div>

                <div v-if="isSellAtHomeFeatureEnabled">
                    <v-divider>inset</v-divider>
                    <v-row class="py-4">
                        <v-col cols="auto" class="body-2">Sell@Home</v-col>
                        <div class="d-flex justify-space-between align-center ml-auto p-2 px-3">
                            <v-switch v-model="isSellAtHomeEnabled"></v-switch>
                        </div>
                        <div class="sell-at-home-disclaimer font-italic px-3 mt-1">
                            By activating Sell@Home, {{ dealerName }} assumes full responsibility for any Cash Offers
                            that are extended to customers. Nissan North America will not assume any responsibility for
                            the Cash Offer. Cash Offers shall be finalized after a physical inspection of the vehicle.
                            {{ dealerName }} shall honor any final Cash Offers made following such inspection.
                        </div>
                    </v-row>
                </div>

                <boost-features-toggle v-if="isBoostFeaturesFeatureToggleEnabled" :program="program" />
            </div>
            <protection-products-toggles v-if="isBoostProgram" :program="program" />
            <dealer-track-toggle v-if="isBoostProgram && isDealerTrackToggleEnabled" :program="program" />
            <garage-alert-toggles v-if="isBoostProgram || isBuyAtHomeProgram" :program="program" />
            <spanish-toggle v-if="isBoostProgram || isBuyAtHomeProgram" :program="program" />
        </v-container>
    </v-sheet>
</template>

<script>
import { call, get, sync } from "vuex-pathify";
import lodashGet from "lodash/get";
import find from "lodash/find";
import isNil from "lodash/isNil";
import Chat from "Components/ProgramSupportCard/components/Chat.vue";
import SpanishToggle from "Components/ProgramSupportCard/components/SpanishToggle.vue";
import GarageAlertToggles from "Components/ProgramSupportCard/components/GarageAlertToggles.vue";
import DealerTrackToggle from "Components/ProgramSupportCard/components/DealerTrackToggle.vue";
import ProtectionProductsToggles from "Components/ProgramSupportCard/components/ProtectionProductsToggles.vue";
import BoostFeaturesToggle from "Components/ProgramSupportCard/components/BoostFeaturesToggle.vue";

export default {
    name: "ProgramSupport",
    components: {
        BoostFeaturesToggle,
        ProtectionProductsToggles,
        GarageAlertToggles,
        SpanishToggle,
        Chat,
        DealerTrackToggle,
    },
    props: {
        program: {
            type: Object,
            required: true,
        },
        dealerId: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            chatSupportEmail: "<EMAIL>",
            chatSupportPhone: "8008880924",

            // protection products
            protectionProduct: false,
            protectionProductSelects: "",
            isFAndIProtectionEnabled: false,

            // carsaver f&i
            isCarsaverFAndIEnabled: false,
            isCarsaverFAndIFeatureEnabled: false,

            // alerts
            isGarageAlertsToggleSelected: false,
            isSmsAlertsEnabled: false,
            isEmailEnabled: false,
            isInAppEnabled: true,

            isInAppToggleDisabled: true,
        };
    },
    computed: {
        featureFlags: get("loggedInUser/featureFlags"),
        userDealerAccessList: get("loggedInUser/userDealerAccessList"),
        nesnaFeatureSubscriptionEnabled: sync("loggedInUser/nesnaFeatureSubscriptionEnabled"),
        userId: get("loggedInUser/userId"),
        programNesnaFeatureSubscriptionEnabled() {
            return lodashGet(this.program, "isNesnaFAndIEnabled", false);
        },
        enableCarsaverFAndIFeatureFlag() {
            return lodashGet(this.featureFlags, "ENABLE_CARSAVER_F_AND_I", false);
        },
        showCarsaverFAndI() {
            return this.enableCarsaverFAndIFeatureFlag && this.isCarsaverFAndIFeatureEnabled;
        },
        dealerIds() {
            const result = this.$route.query.dealerIds;
            return result;
        },
        dealerName() {
            const locatedDealer = find(this.userDealerAccessList, ["id", this.dealerIds]);
            const result = !isNil(locatedDealer) ? locatedDealer.name : "";
            return result;
        },
        isApplicationChatFeatureToggleEnabled() {
            let chatEnabled = lodashGet(this.program, "isChatFeatureEnabled", false);
            return chatEnabled;
        },
        isLibertyMutualFeatureEnabled() {
            let libertyMutualEnabled = lodashGet(this.program, "isLibertyMutualFeatureEnabled", false);
            return libertyMutualEnabled;
        },
        isRouteOneFAndIFeatureEnabled() {
            let isRouteOneFAndIFeatureEnabled = lodashGet(this.program, "isRouteOneFAndIFeatureEnabled", false);
            return isRouteOneFAndIFeatureEnabled;
        },
        isSellAtHomeFeatureEnabled() {
            const isSellAtHomeFeatureEnabled = lodashGet(this.featureFlags, "SELL_AT_HOME_FEATURE", false) || false;
            return isSellAtHomeFeatureEnabled;
        },
        isBoostFeaturesFeatureToggleEnabled() {
            const isBoostPlusFeaturesEnabled =
                lodashGet(this.featureFlags, "BOOST_PLUS_FEATURES_ENABLED", false) || false;
            return isBoostPlusFeaturesEnabled;
        },
        isGarageAlertsFeatureEnabled() {
            return this.program?.isGarageAlertsFeatureEnabled || false;
        },
        isEmailAlertsFeatureEnabled() {
            return this.program?.isEmailAlertsFeatureEnabled || false;
        },
        isSmsAlertsFeatureEnabled() {
            return this.program?.isSmsAlertsFeatureEnabled || false;
        },
        isInAppAlertsFeatureEnabled() {
            return this.program?.isInAppAlertsFeatureEnabled || false;
        },
        programId() {
            let id = lodashGet(this.program, "programId", null);
            return id;
        },
        isAdminUser() {
            return this.$acl.hasAuthority("ROLE_ADMIN");
        },
        isChatEnabled: {
            get() {
                let isChatEnabled = lodashGet(this.program, "isChatEnabled", false);
                return isChatEnabled;
            },
            set(value) {
                this.toggleDealerProgramFeature({
                    programId: this.programId,
                    configType: "chat",
                    isEnabled: value,
                    userId: this.userId,
                });
            },
        },
        isLibertyMutualEnabled: {
            get() {
                let isLibertyMutualEnabled = lodashGet(this.program, "isLibertyMutualEnabled", false);
                return isLibertyMutualEnabled;
            },
            set(value) {
                this.toggleDealerProgramFeature({
                    programId: this.programId,
                    configType: "liberty_mutual",
                    isEnabled: value,
                    userId: this.userId,
                });
            },
        },
        isRouteOneFAndIEnabled: {
            get() {
                return lodashGet(this.program, "isRouteOneFAndIEnabled", false);
            },
            set(value) {
                this.toggleDealerProgramFeature({
                    programId: this.programId,
                    configType: "route_one_finance_and_insurance",
                    isEnabled: value,
                    userId: this.userId,
                });
            },
        },
        getProtectionProducts() {
            const routeOne = lodashGet(this.program, "isRouteOneFAndIEnabled", false);
            const nesna = lodashGet(this.program, "isNesnaFAndIEnabled", false);
            return { routeOne, nesna };
        },
        isProtectionProductSelected() {
            return this.getProtectionProducts.routeOne || this.getProtectionProducts.nesna;
        },
        isSellAtHomeEnabled: {
            get() {
                let isSellAtHomeEnabled = lodashGet(this.program, "isSellAtHomeEnabled", false);
                return isSellAtHomeEnabled;
            },
            set(value) {
                this.toggleDealerProgramFeature({
                    programId: this.programId,
                    configType: "sell_at_home",
                    isEnabled: value,
                    userId: this.userId,
                });
            },
        },
        isDealerTrackToggleEnabled() {
            return lodashGet(this.featureFlags, "ATLAS_DEALER_TRACK_TOGGLE", false);
        },
        isFAndIProtectionFeatureEnabled() {
            const result = lodashGet(this.featureFlags, "ENABLE_NESNA_F_AND_I", false) || false;
            return result;
        },
        isNesnaProductsConfigured() {
            const result = lodashGet(this.program, "isNesnaConfigured", false) || false;
            return result;
        },
        isBoostProgram() {
            const result = lodashGet(this.program, "isBoostProgram", false);
            return result;
        },
        isBuyAtHomeProgram() {
            const result = lodashGet(this.program, "isNissanBuyAtHome", false);
            return result;
        },
    },
    watch: {
        isProtectionProductSelected: {
            handler(val) {
                if (val) {
                    this.protectionProduct = true;
                    this.protectionProductSelects = this.getProtectionProducts.routeOne ? "routeOne" : "nesna";
                } else {
                    this.protectionProductSelects = "";
                }
            },
            immediate: true,
        },
        programNesnaFeatureSubscriptionEnabled: {
            handler(newVal) {
                if (this.protectionProductSelects) {
                    this.protectionProductSelects = newVal ? "nesna" : "routeOne";
                }
            },
            immediate: true,
        },
    },
    mounted() {
        this.isGarageAlertsToggleSelected = this.program?.isGarageAlertsEnabled || false;
        this.isSmsAlertsEnabled = this.program?.isSmsAlertsEnabled || false;
        this.isEmailEnabled = this.program?.isEmailAlertsEnabled || false;
        this.isInAppEnabled = this.program?.isInAppAlertsEnabled || false;
        this.isCarsaverFAndIEnabled = this.program?.isCarsaverFAndIEnabled || false;
        this.isCarsaverFAndIFeatureEnabled = this.program?.isCarsaverFAndIFeatureEnabled || false;
    },
    methods: {
        toggleDealerProgramFeature: call("dealerStore/toggleDealerProgramFeature"),
        carsaverFAndIChanged(value) {
            this.toggleDealerProgramFeature({
                programId: this.programId,
                configType: "carsaver_f_and_i_feature",
                isEnabled: value,
                userId: this.userId,
            });
        },
        protectionProductChanged(activeProduct) {
            const protectionProductsConfigTypeMap = {
                routeOne: "route_one_finance_and_insurance",
                nesna: "nesna_finance_and_insurance",
            };

            // Determine the protection product to enable and disable
            const enableConfigType = protectionProductsConfigTypeMap[activeProduct];
            // Enable the active product (this will automatically disable the other one on the backend)
            this.toggleDealerProgramFeature({
                programId: this.programId,
                configType: enableConfigType,
                isEnabled: true,
                userId: this.userId,
            }).then(() => {
                this.nesnaFeatureSubscriptionEnabled = enableConfigType === "nesna_finance_and_insurance";
                this.protectionProductSelects = this.nesnaFeatureSubscriptionEnabled ? "nesna" : "routeOne";
            });
        },
        handleProtectionProductGlobalToggle(value) {
            if (!value) {
                this.nesnaFeatureSubscriptionEnabled = false;
                this.toggleDealerProgramFeature({
                    programId: this.programId,
                    configType: "disable_protection_products_global_toggle",
                    isEnabled: value,
                    userId: this.userId,
                });
            }
        },
        handleAlertChange(value, alertType) {
            this.toggleDealerProgramFeature({
                programId: this.programId,
                configType: alertType,
                isEnabled: value,
                userId: this.userId,
            });
        },
    },
};
</script>

<style lang="scss">
.program-support {
    .cursor-pointer {
        cursor: pointer;
    }

    .v-input--selection-controls {
        margin-top: 0;
        margin-bottom: 0;
        padding-top: 0;
        height: 20px;
    }

    .sell-at-home-disclaimer {
        font-size: 13px;
        color: grey;
    }

    .fandi-protection-products {
        height: 110px;
        padding: 4px 16px;

        .protection-label {
            span {
                color: red;
            }
        }

        .v-label {
            font-size: 14px;
        }

        .nesna-description {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            margin-left: 40px;
            margin-bottom: 6px;
        }

        p {
            margin: 0;
        }
    }

    .garage-alerts {
        .garage-alerts-cols {
            display: flex;
            flex-direction: column;
            gap: 16px;

            > div {
                display: flex;
                gap: 6px;

                p {
                    margin: 0;
                    font-weight: 700;
                }
            }

            .text-disabled {
                color: #666 !important;
            }
        }

        .disclaimer {
            font-size: px2rem(12);
            color: #757575;
            font-style: italic;
            line-height: normal;
        }
    }
}
</style>
