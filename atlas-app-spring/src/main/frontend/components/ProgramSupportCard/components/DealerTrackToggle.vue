<template>
    <div class="dealer-track">
        <v-divider class="">inset</v-divider>
        <v-row class="dealer-track-toggle-row">
            <v-col cols="auto" class="body-2 pa-0 py-4">DealerTrack</v-col>
            <div class="d-flex justify-space-between align-center ml-auto">
                <div
                    :class="{
                        'cursor-pointer': !boostProgramButNotAdmin && !isLoading,
                        'cursor-not-allowed': boostProgramButNotAdmin || isLoading,
                    }"
                    @click.stop="handleDealerTrackSwitchWrapperClicked"
                >
                    <v-switch
                        v-model="dealerTrackEnabled"
                        :disabled="boostProgramButNotAdmin || isLoading"
                        :loading="isLoading"
                    ></v-switch>
                </div>
            </div>
        </v-row>

        <div v-if="isDealerTrackInputErrored" class="dealer-track-error mt-2">
            Please confirm that you have the authority to activate this feature on behalf of
            {{ dealerName }}.
        </div>
    </div>
</template>
<script>
import lodashGet from "lodash/get";
import find from "lodash/find";
import isNil from "lodash/isNil";
import { call, get } from "vuex-pathify";

export default {
    name: "DealerTrackToggle",
    props: {
        program: {
            type: Object,
            required: true,
        },
    },
    data() {
        return {
            isLoading: false,
            isDealerTrackInputErrored: false,
        };
    },
    computed: {
        userId: get("loggedInUser/userId"),
        userDealerAccessList: get("loggedInUser/userDealerAccessList"),
        dealerTrackEnabled: {
            get() {
                return lodashGet(this.program, "isDealerTrackEnabled", false);
            },
            set(value) {
                if (this.boostProgramButNotAdmin || this.isLoading) {
                    return;
                }

                const originalValue = this.dealerTrackEnabled;
                this.isLoading = true;

                this.toggleDealerProgramFeature({
                    programId: this.programId,
                    configType: "dealer_track",
                    isEnabled: value,
                    userId: this.userId,
                })
                    .then(() => {
                        this.$set(this.program, "isDealerTrackEnabled", value);
                        this.isDealerTrackInputErrored = false;
                    })
                    .catch(() => {
                        this.$set(this.program, "isDealerTrackEnabled", originalValue);
                        this.isDealerTrackInputErrored = true;
                        this.$nextTick(() => {
                            this.$forceUpdate();
                        });
                    })
                    .finally(() => {
                        this.isLoading = false;
                    });
            },
        },
        dealerIds() {
            const result = this.$route.query.dealerIds;
            return result;
        },
        dealerName() {
            const locatedDealer = find(this.userDealerAccessList, ["id", this.dealerIds]);
            const result = !isNil(locatedDealer) ? locatedDealer.name : "";
            return result;
        },
        programId() {
            let id = lodashGet(this.program, "programId", null);
            return id;
        },
        isAdminUser() {
            return this.$acl.hasAuthority("ROLE_ADMIN");
        },
        isBoostProgram() {
            return lodashGet(this.program, "isBoostProgram", false);
        },
        boostProgramButNotAdmin() {
            return this.isBoostProgram && !this.isAdminUser;
        },
    },
    mounted() {},
    methods: {
        toggleDealerProgramFeature: call("dealerStore/toggleDealerProgramFeature"),
        resetDealerTrackErrorState() {
            this.isDealerTrackInputErrored = false;
        },
        handleDealerTrackSwitchWrapperClicked() {
            if (this.boostProgramButNotAdmin || this.isLoading) {
                return;
            }
        },
    },
};
</script>
<style lang="scss">
.dealer-track {
    .dealer-track-toggle-row {
        margin: 0;
    }

    .dealer-track-label-error {
        color: #b00020;
    }
    .dealer-track-error {
        font-size: px2rem(12);
        color: #b00020;
        line-height: normal;
        font-weight: 500;
    }
}
</style>
