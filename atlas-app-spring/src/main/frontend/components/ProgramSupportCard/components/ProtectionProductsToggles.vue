<template>
    <div>
        <v-divider>inset</v-divider>
        <v-row class="py-4">
            <v-col cols="auto" class="body-2">Protection Products</v-col>
            <div class="d-flex justify-space-between align-center ml-auto p-2 px-3">
                <v-switch
                    v-model="protectionProduct"
                    :disabled="!isAdminUser"
                    @change="handleProtectionProductGlobalToggle"
                ></v-switch>
            </div>
            <v-col v-if="protectionProduct" cols="12" class="boost-fandi-protection-products">
                <v-radio-group
                    v-model="protectionProductSelects"
                    class="protection-group"
                    row
                    hide-details
                    :disabled="!isAdminUser"
                    @change="protectionProductChanged"
                >
                    <v-radio label="CarSaver LTW/VSC" value="carsaver" color="#212121"></v-radio>
                    <v-radio value="routeOne" color="#212121">
                        <template #label>
                            <div class="d-flex align-center">
                                <p>Route One</p>
                                <v-tooltip top>
                                    <template #activator="{ on, attrs }">
                                        <v-btn icon v-bind="attrs" v-on="on">
                                            <v-icon dense> mdi-information-outline </v-icon>
                                        </v-btn>
                                    </template>
                                    <span>
                                        Protection products through Route One requires <br />
                                        a monthly subscription. Please reach out to your <br />
                                        performance manager for more information.
                                    </span>
                                </v-tooltip>
                            </div>
                        </template>
                    </v-radio>
                </v-radio-group>
            </v-col>
        </v-row>
    </div>
</template>
<script>
import lodashGet from "lodash/get";
import { call, get } from "vuex-pathify";

export default {
    name: "ProtectionProductsToggles",
    props: {
        program: {
            type: Object,
            required: true,
        },
    },
    data() {
        return {
            protectionProduct: false,
            protectionProductSelects: "",
            isCarsaverFAndIEnabled: false,
            isRouteOneFAndIEnabled: false,
        };
    },
    computed: {
        featureFlags: get("loggedInUser/featureFlags"),
        userId: get("loggedInUser/userId"),
        programId() {
            return lodashGet(this.program, "programId", "");
        },
        dealerIds() {
            const result = this.$route.query.dealerIds;
            return result;
        },
        getProtectionProducts() {
            const routeOne = lodashGet(this.program, "isRouteOneFAndIEnabled", false);
            const carsaver = lodashGet(this.program, "isCarsaverFAndIEnabled", false);
            return { routeOne, carsaver };
        },
        isProtectionProductSelected() {
            return this.getProtectionProducts.routeOne || this.getProtectionProducts.carsaver;
        },
        isAdminUser() {
            return this.$acl.hasAuthority("ROLE_ADMIN");
        },
    },
    watch: {
        isProtectionProductSelected: {
            handler(val) {
                if (val) {
                    this.protectionProduct = true;
                    this.protectionProductSelects = this.getProtectionProducts.routeOne ? "routeOne" : "carsaver";
                } else {
                    this.protectionProductSelects = "";
                }
            },
            immediate: true,
        },
    },
    mounted() {
        this.isCarsaverFAndIEnabled = this.program?.isCarsaverFAndIEnabled || false;
        this.isRouteOneFAndIEnabled = this.program?.isRouteOneFAndIEnabled || false;
    },
    methods: {
        toggleDealerProgramFeature: call("dealerStore/toggleDealerProgramFeature"),
        handleProtectionProductGlobalToggle(value) {
            // Only allow admins to toggle protection products
            if (!this.isAdminUser) {
                return;
            }

            if (!value) {
                // Reset selection when turning off the global toggle
                this.protectionProductSelects = "";
                this.isCarsaverFAndIEnabled = false;
                this.isRouteOneFAndIEnabled = false;

                this.toggleDealerProgramFeature({
                    programId: this.programId,
                    configType: "disable_boost_protection_products_global_toggle",
                    isEnabled: value,
                    userId: this.userId,
                });
            }
        },
        protectionProductChanged(activeProduct) {
            // Only allow admins to change protection product selection
            if (!this.isAdminUser) {
                return;
            }

            const protectionProductsConfigTypeMap = {
                carsaver: "boost_carsaver_f_and_i_feature",
                routeOne: "boost_route_one_finance_and_insurance",
            };
            // Determine the protection product to enable and disable
            const enableConfigType = protectionProductsConfigTypeMap[activeProduct];
            // Enable the active product (the backend will automatically disable the other one)
            this.toggleDealerProgramFeature({
                programId: this.programId,
                configType: enableConfigType,
                isEnabled: true,
                userId: this.userId,
            }).then(() => {
                this.isCarsaverFAndIEnabled = enableConfigType === "boost_carsaver_f_and_i_feature";
                this.isRouteOneFAndIEnabled = enableConfigType === "boost_route_one_finance_and_insurance";
                this.protectionProductSelects = this.isCarsaverFAndIEnabled ? "carsaver" : "routeOne";
            });
        },
    },
};
</script>
<style lang="css" scoped>
.boost-fandi-protection-products {
    height: 50px;
    padding: 4px 16px;

    p {
        margin: 0;
    }
}
</style>
