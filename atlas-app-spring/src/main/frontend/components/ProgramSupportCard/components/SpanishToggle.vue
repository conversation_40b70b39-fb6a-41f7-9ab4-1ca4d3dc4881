<template>
    <div class="spanish-translation">
        <v-divider class="mt-1">inset</v-divider>
        <v-row class="spanish-translation-toggle-row mt-3">
            <v-col cols="auto" class="body-2 pa-0">Spanish Translation</v-col>
            <div class="d-flex justify-space-between align-center ml-auto">
                <div class="cursor-pointer" @click.stop="handleSpanishSwitchWrapperClicked">
                    <v-switch
                        v-model="spanishTranslationEnabled"
                        :disabled="!certifiedForSpanishTranslation || boostProgramButNotAdmin"
                    ></v-switch>
                </div>
            </div>

            <v-col cols="12" class="d-flex pa-0 pt-3">
                <v-checkbox
                    v-model="certifiedForSpanishTranslation"
                    class="mr-2"
                    :disabled="(spanishTranslationEnabled && certifiedForSpanishTranslation) || boostProgramButNotAdmin"
                    :error="isSpanishTranslationInputErrored"
                    @change="resetSpanishTranslationErrorState"
                ></v-checkbox>
                <span :class="{ 'spanish-translation-label-error': isSpanishTranslationInputErrored }">
                    I certify that I have the authority to activate this feature on behalf of {{ dealerName }}.
                </span>
            </v-col>
        </v-row>

        <div v-if="isSpanishTranslationInputErrored" class="spanish-translation-error mt-2">
            Please confirm that you have the authority to activate this feature on behalf of
            {{ dealerName }}.
        </div>

        <SpanishDisclaimer :program="program" />
    </div>
</template>
<script>
import lodashGet from "lodash/get";
import SpanishDisclaimer from "Components/ProgramSupportCard/components/SpanishDisclaimer.vue";
import find from "lodash/find";
import isNil from "lodash/isNil";
import { call, get } from "vuex-pathify";

export default {
    name: "SpanishToggle",
    components: { SpanishDisclaimer },
    props: {
        program: {
            type: Object,
            required: true,
        },
    },
    data() {
        return {
            // translation
            certifiedForSpanishTranslation: false,
            isSpanishTranslationInputErrored: false,
        };
    },
    computed: {
        userId: get("loggedInUser/userId"),
        userDealerAccessList: get("loggedInUser/userDealerAccessList"),
        spanishTranslationEnabled: {
            get() {
                return lodashGet(this.program, "isSpanishTranslationEnabled", false);
            },
            set(value) {
                if (this.boostProgramButNotAdmin) {
                    return;
                }
                if (!value) this.certifiedForSpanishTranslation = false;
                this.isSpanishTranslationInputErrored = false;
                this.toggleDealerProgramFeature({
                    programId: this.programId,
                    configType: "spanish_translation",
                    isEnabled: value,
                    userId: this.userId,
                });
            },
        },
        dealerIds() {
            const result = this.$route.query.dealerIds;
            return result;
        },
        dealerName() {
            const locatedDealer = find(this.userDealerAccessList, ["id", this.dealerIds]);
            const result = !isNil(locatedDealer) ? locatedDealer.name : "";
            return result;
        },
        programId() {
            let id = lodashGet(this.program, "programId", null);
            return id;
        },
        isAdminUser() {
            return this.$acl.hasAuthority("ROLE_ADMIN");
        },
        isBoostProgram() {
            return lodashGet(this.program, "isBoostProgram", false);
        },
        boostProgramButNotAdmin() {
            return this.isBoostProgram && !this.isAdminUser;
        },
    },
    mounted() {
        this.certifiedForSpanishTranslation = this.program?.isSpanishTranslationEnabled || false;
    },
    methods: {
        toggleDealerProgramFeature: call("dealerStore/toggleDealerProgramFeature"),
        handleSpanishSwitchWrapperClicked() {
            if (this.boostProgramButNotAdmin) {
                return;
            }

            if (!this.certifiedForSpanishTranslation && !this.spanishTranslationEnabled) {
                this.isSpanishTranslationInputErrored = true;
            }
        },
        resetSpanishTranslationErrorState() {
            this.isSpanishTranslationInputErrored = false;
        },
    },
};
</script>
<style lang="scss">
.spanish-translation {
    .spanish-translation-toggle-row {
        margin: 0;
    }

    .spanish-translation-label-error {
        color: #b00020;
    }
    .spanish-translation-error {
        font-size: px2rem(12);
        color: #b00020;
        line-height: normal;
        font-weight: 500;
    }
}
</style>
