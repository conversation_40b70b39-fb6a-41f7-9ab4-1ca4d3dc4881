<template>
    <div class="d-flex flex-column">
        <div class="spanish-translation-disclaimer mt-3">
            {{ spanishDisclaimer }}
        </div>
        <div v-if="isBuyAtHomeProgram" class="spanish-translation-disclaimer mt-3 mb-1">
            <span class="font-weight-bold">Monthly Charge:</span>
            You, the Dealer, will be charged $50.00 per month for enabling Spanish translation. Such charges will be
            billed to you separately by Nissan North America, Inc. not by CarSaver, and are subject to change. Charges
            will cease should you disable the translation service.
        </div>
    </div>
</template>
<script>
import lodashGet from "lodash/get";

export default {
    name: "SpanishDisclaimer",
    props: {
        program: {
            type: Object,
            required: true,
        },
    },
    computed: {
        isBuyAtHomeProgram() {
            return lodashGet(this.program, "isNissanBuyAtHome", false);
        },
        spanishDisclaimer() {
            if (this.isBuyAtHomeProgram) {
                return (
                    "By enabling Spanish translation you also agree to provide the consumer any and all " +
                    "Spanish-language transaction documents, buyers guides, or other materials as may be " +
                    "required by applicable federal or state law. CarSaver disclaims any responsibility for " +
                    "providing such Spanish-language materials. Please consult with your legal counsel about the " +
                    "requirements that may be applicable to your dealership."
                );
            }

            return (
                "By enabling Spanish translation you, the Dealer, agree to provide the consumer any and all " +
                "Spanish-language transaction documents, buyers guides, or other materials as may be required by " +
                "applicable federal or state law. CarSaver disclaims any responsibility for providing such " +
                "Spanish-language materials. Please consult with your legal counsel about the requirements that " +
                "may be applicable to your dealership."
            );
        },
    },
};
</script>
<style lang="scss">
.spanish-translation-disclaimer {
    font-size: px2rem(12);
    color: $gray-600;
    line-height: normal;
    font-weight: 500;
}
</style>
