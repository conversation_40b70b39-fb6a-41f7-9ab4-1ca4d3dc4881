<template>
    <div v-if="isGarageAlertsFeatureEnabled">
        <v-divider></v-divider>
        <v-row class="garage-alerts py-4">
            <v-col cols="auto" class="body-2">Garage Alerts</v-col>
            <div class="d-flex justify-space-between align-center ml-auto p-2 px-3">
                <v-switch
                    v-model="isGarageAlertsToggleSelected"
                    @change="handleAlertChange($event, 'garage_alerts')"
                ></v-switch>
            </div>
            <v-col cols="12" class="garage-alerts-cols">
                <div v-if="isSmsAlertsFeatureEnabled">
                    <v-switch
                        v-model="isSmsAlertsEnabled"
                        :readonly="!isGarageAlertsToggleSelected"
                        :disabled="!isGarageAlertsToggleSelected"
                        @change="handleAlertChange($event, 'sms_alerts')"
                    ></v-switch>
                    <p :class="{ 'text-disabled': !isGarageAlertsToggleSelected }">SMS Alerts*</p>
                </div>
                <div v-if="isEmailAlertsFeatureEnabled">
                    <v-switch
                        v-model="isEmailEnabled"
                        :readonly="!isGarageAlertsToggleSelected"
                        :disabled="!isGarageAlertsToggleSelected"
                        @change="handleAlertChange($event, 'email_alerts')"
                    ></v-switch>
                    <p :class="{ 'text-disabled': !isGarageAlertsToggleSelected }">Email Alerts*</p>
                </div>
                <div v-if="isInAppAlertsFeatureEnabled">
                    <v-switch
                        v-model="isInAppEnabled"
                        :readonly="isInAppToggleDisabled"
                        :disabled="isInAppToggleDisabled"
                        @change="handleAlertChange($event, 'inapp_alerts')"
                    ></v-switch>
                    <p :class="{ 'text-disabled': isInAppToggleDisabled }">Inapp Alerts</p>
                </div>
            </v-col>
            <v-col v-if="isBuyAtHomeProgram" cols="12">
                <p class="disclaimer">
                    *Charges may apply for all SMS Alerts and Email Alerts. Such charges will be billed to you
                    separately by Nissan North America, not by CarSaver, and are subject to change. Please contact your
                    Nissan North America representative for specific details.
                </p>
            </v-col>
        </v-row>
    </div>
</template>
<script>
import { call, get } from "vuex-pathify";
import lodashGet from "lodash/get";

export default {
    name: "GarageAlertToggles",
    props: {
        program: {
            type: Object,
            required: true,
        },
    },
    data() {
        return {
            isGarageAlertsToggleSelected: false,
            isSmsAlertsEnabled: false,
            isEmailEnabled: false,
            isInAppEnabled: true,
            isInAppToggleDisabled: true,
        };
    },
    computed: {
        userId: get("loggedInUser/userId"),
        isGarageAlertsFeatureEnabled() {
            return this.program?.isGarageAlertsFeatureEnabled || false;
        },
        isEmailAlertsFeatureEnabled() {
            return this.program?.isEmailAlertsFeatureEnabled || false;
        },
        isSmsAlertsFeatureEnabled() {
            return this.program?.isSmsAlertsFeatureEnabled || false;
        },
        isInAppAlertsFeatureEnabled() {
            return this.program?.isInAppAlertsFeatureEnabled || false;
        },
        programId() {
            let id = lodashGet(this.program, "programId", null);
            return id;
        },
        isBuyAtHomeProgram() {
            const result = lodashGet(this.program, "isNissanBuyAtHome", false);
            return result;
        },
    },
    mounted() {
        this.isGarageAlertsToggleSelected = this.program?.isGarageAlertsEnabled || false;
        this.isSmsAlertsEnabled = this.program?.isSmsAlertsEnabled || false;
        this.isEmailEnabled = this.program?.isEmailAlertsEnabled || false;
        this.isInAppEnabled = this.program?.isInAppAlertsEnabled || false;
    },
    methods: {
        toggleDealerProgramFeature: call("dealerStore/toggleDealerProgramFeature"),
        handleAlertChange(value, alertType) {
            this.toggleDealerProgramFeature({
                programId: this.programId,
                configType: alertType,
                isEnabled: value,
                userId: this.userId,
            });
        },
    },
};
</script>
<style lang="scss" scoped>
.garage-alerts {
    .garage-alerts-cols {
        display: flex;
        flex-direction: column;
        gap: 16px;

        > div {
            display: flex;
            gap: 6px;

            p {
                margin: 0;
                font-weight: 700;
            }
        }

        .text-disabled {
            color: #666 !important;
        }
    }

    .disclaimer {
        font-size: px2rem(12);
        color: #757575;
        font-style: italic;
        line-height: normal;
    }
}
</style>
