<template>
    <v-chip :style="{ background: colorHex, color: textColor }" small>
        <slot />
    </v-chip>
</template>

<script>
export default {
    name: "ColorChip",

    props: {
        color: {
            type: String,
            required: true,
        },
    },

    computed: {
        colorHex() {
            if (this.color.indexOf("#") === -1) {
                return `#${this.color}`;
            }

            return this.color;
        },
        textColor() {
            const rgb = this.hexToRgb(this.color);
            if (rgb == null) {
                return "black";
            }

            // const brightness = Math.round((rgb.red * 299) + (rgb.green * 587) + (rgb.blue * 114));
            const brightness = Math.round(rgb.red * 299 + rgb.green * 587 + rgb.blue * 114) / 1000;

            return brightness > 125 ? "black" : "white";
        },
    },

    methods: {
        hexToRgb(hex) {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result
                ? {
                      red: parseInt(result[1], 16),
                      green: parseInt(result[2], 16),
                      blue: parseInt(result[3], 16),
                  }
                : null;
        },
    },
};
</script>
