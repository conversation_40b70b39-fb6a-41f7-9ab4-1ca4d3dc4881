<template>
    <a href="#" class="ak-trigger announcekit-btn">
        <AnnounceKit
            catch-click=".ak-trigger"
            widget="https://announcekit.co/widgets/v2/w5dNm"
            :user="user"
            :data="segmentationData"
            :drawer="configs.drawer"
            :widget-style="configs.drawer"
        />
    </a>
</template>

<script>
import AnnounceKit from "announcekit-vue";
import { get } from "vuex-pathify";
export default {
    name: "AnnounceKitWidget",
    components: {
        AnnounceKit,
    },
    data() {
        return {
            configs: {
                drawer: {
                    style: {
                        fontFamily: "Roboto, sans-serif",
                        color: "red",
                    },
                },
            },
        };
    },
    computed: {
        userEmail: get("loggedInUser/email"),
        userId: get("loggedInUser/userId"),
        userFirstName: get("loggedInUser/firstName"),
        userlastName: get("loggedInUser/lastName"),
        programAccessList: get("loggedInUser/userProgramAccessList"),
        programs() {
            let programs = [];

            programs = this.programAccessList.map((program) => program.name);
            return programs;
        },
        user() {
            return {
                id: this.userId,
                email: this.userEmail,
                name: `${this.userFirstName} ${this.userlastName}`,
            };
        },
        segmentationData() {
            return {
                Programs: this.joinByComma(this.programs),
            };
        },
    },
    methods: {
        joinByComma(list) {
            const joinedList = list.join(",");
            return joinedList;
        },
    },
};
</script>
<style lang="scss" scoped>
.announcekit-btn {
    text-decoration: none;
    color: white;
    padding: 0 13px;
}
</style>
<style>
.announcekit-widget-badge {
    margin-bottom: 3px;
}
.announcekit-widget-drawer-badge {
    font-family: "Roboto", sans-serif;
}
</style>
