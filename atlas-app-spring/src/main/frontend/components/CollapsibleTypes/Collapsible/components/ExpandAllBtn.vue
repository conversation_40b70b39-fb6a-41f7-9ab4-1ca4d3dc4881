<template>
    <v-switch
        v-model="expandPanels"
        class="expandbtn mt-0 pt-0"
        label="Expand All"
        hide-details
        inset
        @click.stop
    ></v-switch>
</template>
<script>
import { get, sync, call } from "vuex-pathify";
export default {
    name: "ExpandAllBtn",
    props: {
        selectedSubGroup: {
            type: String,
            default: "",
        },
        open: {
            type: Boolean,
            required: false,
            default: false,
        },
    },
    data() {
        return {
            expandPanels: false,
        };
    },
    computed: {
        expandAll: sync("pageConfigs/expandAll"),
        subPanels: get("pageConfigs/subPanels[:selectedSubGroup]"),
        sectionIndex: get("pageConfigs/sectionIndex"),
        areAllPanelsOpen: get("pageConfigs/areAllPanelsOpen"),
        areAllPanelsClosed: get("pageConfigs/areAllPanelsClosed"),
    },
    watch: {
        areAllPanelsOpen: {
            handler(value) {
                if (value === true) {
                    this.expandPanels = true;
                }
            },
            immediate: true,
        },
        areAllPanelsClosed: {
            handler(value) {
                if (value === true) {
                    this.expandPanels = false;
                }
            },
            immediate: true,
        },
        open: {
            handler(value) {
                if (value === false) {
                    this.expandPanels = false;
                }
            },
            immediate: true,
        },
        expandPanels: {
            handler(value) {
                if (value) {
                    this.openPanels();
                } else {
                    this.closePanels();
                }

                this.$emit("update:expandAll", value);
            },
        },
        expandAll: {
            handler(value) {
                this.expandPanels = value;
            },
            immediate: true,
        },
    },
    methods: {
        openPanel: call("pageConfigs/openPanel"),
        closePanel: call("pageConfigs/closePanel"),
        openSubPanel: call("pageConfigs/openSubPanel"),
        closeSubPanel: call("pageConfigs/closeSubPanel"),

        openParentPanel() {
            this.openPanel({ title: this.selectedSubGroup });
        },
        closeParentPanel() {
            this.closePanel({ title: this.selectedSubGroup });
        },
        openAllPanels() {
            this.expandAll = true;
            this.$emit("update:expandAll", this.expandAll);
        },
        closeAllPanels() {
            this.expandAll = false;
            this.$emit("update:expandAll", this.expandAll);
        },
        openPanels() {
            if (this.selectedSubGroup) {
                this.openSubPanel({ title: this.selectedSubGroup });
                this.openParentPanel();
            } else {
                this.openAllPanels();
            }
        },
        closePanels() {
            if (this.selectedSubGroup) {
                this.closeSubPanel({ title: this.selectedSubGroup });
                this.closeParentPanel();
            } else {
                this.closeAllPanels();
            }
        },
    },
};
</script>
<style lang="scss">
button.v-expansion-panel-header:focus::before {
    background-color: transparent !important;
}
</style>
