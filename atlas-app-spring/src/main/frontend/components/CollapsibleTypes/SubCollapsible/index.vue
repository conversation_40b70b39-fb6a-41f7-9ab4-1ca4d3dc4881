<template>
    <div>
        <v-expansion-panel v-if="collapsible" class="no-box-shadow border">
            <v-expansion-panel-header class="align-start">
                <div class="header-wrapper mr-3">
                    <div class="title d-flex justify-space-between align-center">
                        <template v-if="title">
                            <div>
                                {{ title }} <span class="font-weight-regular">{{ postText }}</span>
                            </div>
                        </template>
                        <template v-else>
                            <slot name="title" />
                        </template>
                        <div v-if="video" class="watch-video">
                            <a :href="videoUrl">
                                <v-icon class="watch-video-icon">mdi-video-outline</v-icon> Watch Video
                            </a>
                        </div>
                    </div>
                    <div class="description">
                        <template v-if="description">{{ description }}</template>
                        <template v-else>
                            <slot name="description" />
                        </template>
                    </div>
                </div>
            </v-expansion-panel-header>

            <v-expansion-panel-content>
                <div class="content pt-4 pb-2">
                    <slot name="body" />
                </div>
                <div v-if="$slots.footer" class="pt-4">
                    <slot name="footer" />
                </div>
            </v-expansion-panel-content>
        </v-expansion-panel>

        <div v-else>
            <slot name="body" />
        </div>
    </div>
</template>

<script>
import { get } from "vuex-pathify";
export default {
    name: "SubCollapsible",
    props: {
        title: {
            type: String,
            required: false,
            default: "",
        },
        postText: {
            type: String,
            required: false,
            default: "",
        },
        description: {
            type: String,
            required: false,
            default: "",
        },
        video: {
            type: String,
            required: false,
            default: "",
        },
        expand: {
            type: [Boolean, Array],
            required: false,
            default: false,
        },
        collapsible: {
            type: Boolean,
            required: false,
            default: true,
        },
    },
    data() {
        return {
            panels: null,
        };
    },
    computed: {
        expandAll: get("pageConfigs/expandAll"),
        videoUrl() {
            const protocol = "https://";
            const video = this.video;

            if (video && video.startsWith("http")) {
                return video;
            }

            return protocol + video;
        },
    },
    watch: {
        expandAll: {
            handler(val) {
                if (val) {
                    this.panels = 0;
                } else {
                    this.panels = null;
                }
            },
            immediate: true,
        },
        expand: {
            handler(val) {
                this.panels = val ? 0 : null;
            },
            immediate: true,
        },
    },
};
</script>

<style lang="scss" scoped>
@import "~vuetify/src/styles/settings/_variables";

.title {
    font-size: 1rem !important;
    line-height: 1.75 !important;
}
.content {
    border-top: 1px solid #e0e0e0;
}

.no-box-shadow,
.no-box-shadow::before,
.no-box-shadow::after {
    box-shadow: none !important;
}

.border {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
}

.border::after {
    border: none;
}
.description {
    line-height: 1.4 !important;
}
.watch-video,
.description {
    color: var(--grey-grey-darken-2, #616161);
    font-size: px2rem(14) !important;
}

.watch-video {
    line-height: 1;

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
        margin-top: 6px;
    }

    &-icon {
        font-size: px2rem(20) !important;
        margin-top: -3px;
    }

    & a {
        color: inherit;
        text-decoration: none;
    }

    a {
        display: flex;
        gap: 4px;
    }
}

.no-box-shadow,
.no-box-shadow::before {
    box-shadow: none !important;
}

.v-expansion-panel-content__wrap {
    padding-bottom: 24px !important;
}
</style>
