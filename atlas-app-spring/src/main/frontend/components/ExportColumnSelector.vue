<template>
    <v-dialog v-model="dialog" width="500">
        <template #activator="{ on, attrs }">
            <div class="d-flex">
                <v-btn icon v-bind="attrs" v-on="on">
                    <v-icon>mdi-file-export</v-icon>
                </v-btn>
                <info-tooltip v-if="!selectedDealer" size="16px"
                    >Do not navigate away until the file export is completed.</info-tooltip
                >
            </div>
        </template>

        <v-card>
            <v-card-title class="headline grey lighten-2"> Export </v-card-title>
            <v-card-text class="pt-2 pb-0">
                <v-checkbox :label="'Select/Unselect All'" @click="selectUnselectAll"></v-checkbox>
                <v-divider />
                <v-list>
                    <v-list-item-group v-model="selected" multiple>
                        <template v-for="field in exportableFields">
                            <v-list-item
                                :key="fieldKey(field)"
                                :value="fieldKey(field)"
                                active-class="blue--text text--accent-4"
                                class="px-0"
                            >
                                <template #default="{ active }">
                                    <v-list-item-action>
                                        <v-checkbox :input-value="active" />
                                    </v-list-item-action>
                                    <v-list-item-content>
                                        <v-list-item-title>
                                            {{ fieldName(field) }}
                                        </v-list-item-title>
                                    </v-list-item-content>
                                </template>
                            </v-list-item>
                        </template>
                    </v-list-item-group>
                </v-list>
            </v-card-text>
            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn color="primary" text @click="doExport()"> Export </v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script>
import { get } from "vuex-pathify";
import InfoTooltip from "Components/InfoTooltip.vue";

export default {
    name: "ExportColumnSelector",
    components: { InfoTooltip },
    props: {
        id: {
            type: String,
            default: "table-column-selector",
        },
        fields: {
            type: Array,
            required: true,
        },
        displayFields: {
            type: Array,
            required: false,
            default() {
                return [];
            },
        },
    },
    data() {
        return {
            dialog: false,
            selected: [],
        };
    },
    computed: {
        selectedDealer: get("loggedInUser/selectedDealer"),
        exportableFields() {
            return _.filter(this.fields, (field) => {
                return _.get(field, "exportable", true);
            });
        },
    },

    watch: {
        selected(value) {
            this.$emit("input", this.selected);
        },
    },
    mounted() {
        this.syncExportFields();
    },

    methods: {
        doExport() {
            this.$emit("doExport");
            this.dialog = false;
        },
        selectUnselectAll() {
            if (this.exportableFields.length === this.selected.length) {
                this.selected = [];
            } else {
                const allSelected = _.map(this.exportableFields, (field) => this.fieldKey(field));
                this.selected = allSelected;
            }
        },
        syncExportFields() {
            const fields = this.fields;
            this.selected = _.filter(this.displayFields, (field) => {
                const fieldDefinition = _.find(fields, ["key", field]);

                if (_.isNil(fieldDefinition)) {
                    return fields.indexOf(field) !== -1;
                }

                return _.get(fieldDefinition, "exportable", true);
            });
        },
        fieldKey(field) {
            if (_.isObject(field)) {
                return _.get(field, "value");
            }

            return field;
        },
        fieldName(field) {
            if (_.isObject(field)) {
                const label = _.get(field, "text", _.startCase(_.get(field, "value")));
                if (label !== "") {
                    return label;
                } else {
                    return _.startCase(_.get(field, "key"));
                }
            }

            return _.startCase(field);
        },
    },
};
</script>

<style scoped></style>
