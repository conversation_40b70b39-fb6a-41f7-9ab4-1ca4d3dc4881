<template>
    <i :class="standardClass">{{ parsed.id }}</i>
</template>

<script>
export default {
    props: {
        name: {
            type: String,
            required: true,
        },
    },
    computed: {
        parsed() {
            const check = (customSuffixes, standardSuffix) => {
                for (let suffix of customSuffixes) {
                    suffix = `_${suffix}`;
                    if (this.name.endsWith(suffix)) {
                        return {
                            suffix: standardSuffix,
                            id: this.name.substring(0, this.name.indexOf(suffix)),
                        };
                    }
                }

                return false;
            };

            return (
                check(["fill", "filled"], "") || {
                    suffix: "",
                    id: this.name,
                }
            );
        },
        standardClass() {
            if (this.parsed.suffix) {
                return `material-icons-${this.parsed.suffix}`;
            }

            return "material-icons";
        },
    },
};
</script>
