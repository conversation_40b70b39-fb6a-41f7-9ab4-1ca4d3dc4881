<template>
    <v-btn-toggle v-model="inputVal" class="custom-toggle-group" active-class="custom-toggle-group--active" dense>
        <v-btn
            v-for="(item, idx) in list"
            :key="idx + item.label"
            class="custom-toggle-group-button"
            :value="item.value"
        >
            {{ item.label }}
        </v-btn>
    </v-btn-toggle>
</template>

<script>
import { defineComponent } from "vue";

export default defineComponent({
    name: "CustomToggleGroup",
    props: {
        value: {
            type: [String, Boolean],
            required: false,
            default: null,
        },
        list: {
            type: Array,
            required: false,
            default: () => [],
        },
    },
    computed: {
        inputVal: {
            get() {
                return this.value;
            },
            set(val) {
                this.$emit("input", val);
            },
        },
    },
});
</script>

<style scoped lang="scss">
@import "~vuetify/src/styles/settings/_variables";

.custom-toggle-group {
    display: flex;
    width: 100%;
    border: 1px solid #1976d2 !important;

    &-button {
        color: #9e9e9e !important;
        background-color: white !important;
        border: none !important;
        border-radius: 3px !important;
        flex: 1;

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
            font-size: 12px !important;
        }

        &:first-child {
            border-radius: 3px 0 0 3px !important;
        }

        &:last-child {
            border-radius: 0 3px 3px 0 !important;
        }
    }

    &--active {
        background: #1976d2 !important;
        color: white !important;
    }
}
</style>
<style>
.custom-toggle-group-button span {
    text-wrap: auto !important;
    flex: unset !important;
}
</style>
