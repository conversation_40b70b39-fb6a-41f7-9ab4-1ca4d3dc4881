<template>
    <v-form ref="displayOptionsForm" v-model="isFormValid">
        <div class="display-options">
            <ToggleBtn
                v-model="toggleDisplay"
                label="Display Button"
                @input="handleChange($event, 'display', values.display?.key)"
            />
            <template v-if="toggleDisplay">
                <CustomToggleGroup
                    :key="values.enableImageCta?.key"
                    v-model="toggleButton"
                    :list="[
                        { label: 'HTML button', value: false },
                        { label: 'image as button', value: true },
                    ]"
                    @input="handleChange($event, 'enableImageCta', values.enableImageCta?.key)"
                />
                <div>
                    <template v-if="!enableImageCta">
                        <div class="select-group">
                            <v-text-field
                                :key="values.buttonText?.key"
                                :value="values.buttonText?.value"
                                label="Display Name"
                                placeholder="Display Name"
                                hint="Max 30 characters"
                                :maxlength="30"
                                :rules="buttonTextRules"
                                persistent-hint
                                outlined
                                dense
                                @input="handleChange($event, 'buttonText', values.buttonText?.key)"
                            />
                            <v-select
                                :key="values.linkDestination?.key"
                                :value="values.linkDestination?.value"
                                :items="linkDestinationOptions"
                                item-text="label"
                                item-value="value"
                                label="Link Destination"
                                dense
                                outlined
                                hide-details
                                @input="handleChange($event, 'linkDestination', values.linkDestination?.key)"
                            />
                        </div>
                        <div class="label mb-3 mt-5">Default Color</div>
                        <div class="color-group">
                            <InputColor
                                :key="values.defaultTextColor?.key"
                                :value="values.defaultTextColor?.value"
                                label="Text Color"
                                placeholder="#000000"
                                @change="handleChange($event, 'defaultTextColor', values.defaultTextColor?.key)"
                            />
                            <InputColor
                                :value="values.defaultBackgroundColor?.value"
                                label="Background Color"
                                placeholder="#FFFFFF"
                                @change="
                                    handleChange($event, 'defaultBackgroundColor', values.defaultBackgroundColor?.key)
                                "
                            />
                        </div>
                        <div class="label mt-3 mb-3">Hover Color (Desktop Only)</div>
                        <div class="color-group">
                            <InputColor
                                :key="values.hoverTextColor?.key"
                                :value="values.hoverTextColor?.value"
                                label="Text Color"
                                placeholder="#000000"
                                @change="handleChange($event, 'hoverTextColor', values.hoverTextColor?.key)"
                            />
                            <InputColor
                                :key="values.hoverBackgroundColor?.key"
                                :value="values.hoverBackgroundColor?.value"
                                label="Background Color"
                                placeholder="#FFFFFF"
                                @change="handleChange($event, 'hoverBackgroundColor', values.hoverBackgroundColor?.key)"
                            />
                        </div>
                    </template>
                    <template v-if="enableImageCta">
                        <div class="select-group">
                            <v-text-field
                                :key="values.imageDisplayName?.key"
                                :value="values.imageDisplayName?.value"
                                label="Button Name"
                                placeholder="Button Name"
                                hint="Image alt text"
                                persistent-hint
                                outlined
                                :rules="imageDisplayNameRules"
                                :maxlength="256"
                                dense
                                @input="handleChange($event, 'imageDisplayName', values.imageDisplayName?.key)"
                            />
                            <v-select
                                :key="values.linkDestination?.key"
                                :value="values.linkDestination?.value"
                                :items="linkDestinationOptions"
                                item-text="label"
                                item-value="value"
                                label="Link Destination"
                                dense
                                outlined
                                hide-details
                                @input="handleChange($event, 'linkDestination', values.linkDestination?.key)"
                            />
                        </div>
                        <CustomFileUpload
                            :id="id"
                            :base-key-path="baseKeyPath"
                            :initial-file="imageObject"
                            :initial-image-background-color="values.imageBackgroundColor?.value"
                            @file-selected="handleChange($event, 'imageButtonFile', values.image?.key)"
                            @file-removed="handleRemoveFile($event, 'imageButtonFile', values.image?.key)"
                            @change="handleChange($event.value, $event.property, $event.key)"
                        />
                    </template>
                </div>
            </template>
        </div>
    </v-form>
</template>

<script>
import { defineComponent } from "vue";
import InputColor from "Components/FormInputs/InputColor.vue";
import ToggleBtn from "Components/FormInputs/ToggleBtn.vue";
import CustomFileUpload from "Components/common/CustomFileUpload.vue";
import CustomToggleGroup from "./CustomToggleGroup.vue";
import { call } from "vuex-pathify";

export default defineComponent({
    name: "DisplayOptions",
    components: { CustomFileUpload, ToggleBtn, InputColor, CustomToggleGroup },
    props: {
        id: {
            type: String,
            default: "",
        },
        values: {
            type: Object,
            default: () => {},
            required: false,
        },
        linkDestinationOptions: {
            type: Array,
            default: () => [],
            required: false,
        },
        baseKeyPath: {
            type: String,
            default: "",
        },
    },
    data() {
        return {
            isFormValid: true,
            toggleDisplay: false,
            toggleButton: null,
            imageDisplayNameRules: [
                (v) => !!v || "Button name is required",
                (v) => !v || v.length <= 256 || "Max 256 characters allowed",
            ],
            buttonTextRules: [
                (v) => !!v || "Display name is required",
                (v) => !v || v.length <= 30 || "Max 30 characters allowed",
            ],
        };
    },
    computed: {
        enableImageCta() {
            return this.toggleButton;
        },
        imageObject() {
            const url = this.values?.imageUrl?.value;
            const name = this.values?.imageName?.value;
            const size = this.values?.imageSize?.value;
            return { url, name, size };
        },
        displayId() {
            return this.baseKeyPath + this.values?.display?.key;
        },
    },
    watch: {
        values: {
            deep: true,
            handler(newVal, oldVal) {
                this.toggleDisplay = newVal?.display?.value ?? false;
                this.toggleButton = newVal?.enableImageCta?.value;

                if (!this.enableImageCta && newVal?.buttonText?.value !== oldVal?.buttonText?.value) {
                    this.triggerValidation();
                }
                if (this.enableImageCta && newVal?.imageDisplayName?.value !== oldVal?.imageDisplayName?.value) {
                    this.triggerValidation();
                }
            },
        },
        isFormValid: {
            handler(isValid) {
                this.setValidateForm({ name: this.displayId, valid: isValid });
            },
            immediate: true,
        },
        toggleDisplay: {
            handler(isDisplayed) {
                if (isDisplayed) {
                    this.$nextTick(() => this.triggerValidation());
                } else {
                    this.removeValidateForm(this.displayId);
                }
            },
        },
        enableImageCta(isImageMode) {
            this.$nextTick(() => {
                this.triggerValidation();
            });
        },
    },
    mounted() {
        this.toggleDisplay = this.values?.display?.value ?? false;
        this.toggleButton = this.values?.enableImageCta?.value;

        this.$nextTick(() => {
            if (this.toggleDisplay) {
                this.validate();
            }
        });
    },
    methods: {
        setFormChange: call("pageConfigs/manualFormChange"),
        setValidateForm: call("pageConfigs/setValidateForm"),
        removeValidateForm: call("pageConfigs/removeValidateForm"),
        validate() {
            if (this.$refs.displayOptionsForm) {
                return this.$refs.displayOptionsForm.validate();
            } else {
                return false;
            }
        },
        triggerValidation() {
            this.$nextTick(() => {
                if (this.$refs.displayOptionsForm) {
                    this.$refs.displayOptionsForm.validate();
                }
            });
        },
        handleRemoveFile(value, property, key) {
            this.handleChange("null", "image", key); // set to "null" as a temporary work around
            this.handleChange(null, "imageUrl", key + "-url");
            this.handleChange(null, "imageName", key + "-name");
            this.handleChange(null, "imageSize", key + "-size");
        },
        handleChange(value, property, key) {
            this.$emit("change", { value, property, key });
        },
        handleToggle(value) {
            this.$emit("change", {
                value,
                property: "display",
                key: this.values?.display?.key,
            });
        },
    },
});
</script>

<style scoped lang="scss">
@import "~vuetify/src/styles/settings/_variables";
.display-options {
    display: flex;
    flex-direction: column;
    gap: 24px;
}
p {
    margin: 0;
}
.label {
    font-size: px2rem(14);
    font-weight: 500;
}
.select-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: baseline;
}
.select-group > * {
    width: 100% !important;
}
.color-group {
    display: flex;
    gap: 12px;
}
.color-group > * {
    flex-basis: 50% !important;
}
</style>
