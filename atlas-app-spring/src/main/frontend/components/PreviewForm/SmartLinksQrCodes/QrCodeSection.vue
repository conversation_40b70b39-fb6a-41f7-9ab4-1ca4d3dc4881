<template>
    <Collapsible>
        <template #title> {{ title }} </template>
        <template #description> Download your Smart Links/QR Codes </template>
        <template #body>
            <v-expansion-panels
                :value="subPanels[title]"
                multiple
                class="flex-col-gap-16"
                @change="handleSubPanelChange"
            >
                <QrCodeBlock
                    v-for="(block, n) in getBlocks"
                    :key="block.title"
                    ref="qrBlock"
                    :block="block"
                    :sr-no="n + 1"
                />
            </v-expansion-panels>
        </template>
    </Collapsible>
</template>

<script>
import { defineComponent } from "vue";
import Collapsible from "Components/CollapsibleTypes/Collapsible/index.vue";
import QrCodeBlock from "Components/PreviewForm/SmartLinksQrCodes/QrCodeBlock.vue";
import { get, sync } from "vuex-pathify";

export default defineComponent({
    name: "QrCodeSection",
    components: { QrCodeBlock, Collapsible },
    data() {
        return {
            title: "Smart Links/QR Codes Configurations",
        };
    },
    computed: {
        subPanels: sync("pageConfigs/subPanels"),
        getBuilderData: get("pageConfigs/pageBuilderData"),
        expandAll: get("pageConfigs/expandAll"),

        getBlocks() {
            return this.getBuilderData.sections?.filter((block) => block.title.startsWith("SmartLink QR Code"));
        },
    },
    watch: {
        expandAll: {
            handler(val) {
                if (val) {
                    this.expandAllSubPanels();
                } else {
                    this.closeAllSubPanels();
                }
            },
            immediate: true,
        },
    },
    methods: {
        expandAllSubPanels() {
            this.subPanels = { ...this.subPanels, [this.title]: this.getBlocks.map((_, i) => i) };
        },
        closeAllSubPanels() {
            this.subPanels = { ...this.subPanels, [this.title]: null };
        },
        handleSubPanelChange(value) {
            this.subPanels = { ...this.subPanels, [this.title]: value };
        },
    },
});
</script>

<style scoped lang="scss">
@import "~vuetify/src/styles/settings/_variables";
.flex-col-gap-16 {
    display: flex;
    flex-direction: column;
    gap: 16px;
}
</style>
