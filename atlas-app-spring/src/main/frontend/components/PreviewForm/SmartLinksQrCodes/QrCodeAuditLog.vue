<template>
    <Collapsible>
        <template #title> {{ title }} </template>
        <template #body>
            <v-card class="pa-6 pt-0" flat>
                <v-card-title class="timeline-card__title px-0">Audit ({{ pagination?.totalItems || 0 }})</v-card-title>
                <Loading v-if="loading" />
                <div v-else-if="noAuditData">
                    <v-card flat class="no-auditdata__card">
                        <span class="no-auditdata__info">No activity has been saved</span>
                    </v-card>
                </div>
                <v-expansion-panels
                    v-else
                    :value="subPanels[title]"
                    multiple
                    class="pt-2 pb-6 timeline-cards"
                    @change="handleSubPanelChange"
                >
                    <QrCodeAuditLogCard v-for="(event, i) in auditData" :key="i" :event="event"></QrCodeAuditLogCard>
                </v-expansion-panels>
                <Pagination
                    v-if="!noAuditData"
                    :pagination="pagination"
                    @update:pagination="handlePaginationUpdate"
                ></Pagination>
            </v-card>
        </template>
    </Collapsible>
</template>

<script>
import { defineComponent } from "vue";
import { sync, get, call } from "vuex-pathify";
import Collapsible from "Components/CollapsibleTypes/Collapsible";
import QrCodeAuditLogCard from "Components/PreviewForm/SmartLinksQrCodes/QrCodeAuditLogCard.vue";
import Pagination from "Components/Pagination";
import Loading from "Components/PreviewForm/SmartLinksQrCodes/Loading.vue";

export default defineComponent({
    name: "QrCodeAuditLogs",
    components: { Collapsible, QrCodeAuditLogCard, Pagination, Loading },
    data() {
        return {
            itemsPerPage: 20,
            title: "Smart Link/QR Code Audit",
        };
    },
    computed: {
        selectedDealerId: get("loggedInUser/selectedDealer@id"),
        auditData: get("pageConfigs/auditData@data"),
        loading: get("pageConfigs/<EMAIL>"),
        pagination: get("pageConfigs/auditData@pageData"),
        expandAll: get("pageConfigs/expandAll"),
        subPanels: sync("pageConfigs/subPanels"),
        noAuditData() {
            return this.auditData.length === 0;
        },
    },
    watch: {
        expandAll: {
            handler(val) {
                if (val) {
                    this.expandAllSubPanels();
                } else {
                    this.closeAllSubPanels();
                }
            },
            immediate: true,
        },
    },
    mounted() {
        const payload = {
            pageName: "Smart Links QR Codes",
            dealerId: this.selectedDealerId,
        };
        this.fetchAuditData(payload);
    },
    methods: {
        fetchAuditData: call("pageConfigs/fetchAuditData"),
        handlePaginationUpdate(newPagination, paginationMetaData) {
            if (paginationMetaData?.local) {
                this.handleLocalPagination(newPagination);
            } else {
                this.paginationUpdated(newPagination);
            }
        },
        paginationUpdated(newPagination) {
            const payload = {
                pageName: "Smart Links QR Codes",
                dealerId: this.selectedDealerId,
                page: newPagination.page,
                size: newPagination.itemsPerPage,
            };
            this.fetchAuditData(payload);
        },
        expandAllSubPanels() {
            this.subPanels = { ...this.subPanels, [this.title]: this.handleOpenSubPanels() };
        },
        closeAllSubPanels() {
            this.subPanels = { ...this.subPanels, [this.title]: [] };
        },
        handleSubPanelChange(value) {
            this.subPanels = { ...this.subPanels, [this.title]: value };
        },
        handleLocalPagination(newPagination) {
            this.itemsPerPage = newPagination.itemsPerPage;
            this.expandAllSubPanels();
        },
        handleOpenSubPanels() {
            return Array.from({ length: this.itemsPerPage }).map((_, i) => i);
        },
    },
});
</script>

<style scoped lang="scss">
@import "~vuetify/src/styles/settings/_variables";
.flex-col-gap-16 {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.timeline-card__title {
    color: var(--grey-grey-darken-4, #212121);
    font-size: 20px;
    font-weight: 600;
}

.timeline-cards {
    display: flex;
    flex-direction: column;
    gap: 16px;
    > div {
        width: 100% !important;
    }
}

.no-auditdata__card {
    border-radius: 8px;
    border: 1px solid var(--grey-grey-lighten-1, #bdbdbd);
    display: flex;
    padding: 16px 24px;
    align-self: stretch;
    justify-content: center;
}

.no-auditdata__info {
    font-size: 16px;
    color: var(--opacity-text--disabled, rgba(0, 0, 0, 0.37));
    text-align: center;
    font-weight: 600;
    display: flex;
}
</style>
