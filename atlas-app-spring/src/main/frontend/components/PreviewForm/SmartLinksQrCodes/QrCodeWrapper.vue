<template>
    <v-expansion-panels v-model="panels" class="d-flex flex-column wrapper-container" accordion multiple>
        <QrCodeManager />
        <QrCodeSection v-show="smartLinksOptIn" />
        <QrCodeAuditLogs v-show="smartLinksOptIn" />
    </v-expansion-panels>
</template>

<script>
import { defineComponent } from "vue";
import QrCodeAuditLogs from "Components/PreviewForm/SmartLinksQrCodes/QrCodeAuditLog.vue";
import QrCodeManager from "Components/PreviewForm/SmartLinksQrCodes/QrCodeManager.vue";
import QrCodeSection from "Components/PreviewForm/SmartLinksQrCodes/QrCodeSection.vue";
import { call, get, sync } from "vuex-pathify";

export default defineComponent({
    name: "QrCodeWrapper",
    components: { QrCodeSection, QrCodeManager, QrCodeAuditLogs },
    data() {
        return {
            localPanels: [0, 1, 2],
        };
    },
    computed: {
        panels: sync("pageConfigs/panels"),
        getFormData: get("pageConfigs/form"),
        builderData: get("pageConfigs/pageBuilderData"),
        expandAll: get("pageConfigs/expandAll"),

        smartLinksOptIn() {
            const res =
                this.getFormData.data?.["marketingSettings/smartLinkQrCode/optIn/enabled"] ??
                this.getFormData.defaults?.["marketingSettings/smartLinkQrCode/optIn/enabled"] ??
                "false";
            return res + "" === "true";
        },
    },
    watch: {
        expandAll: {
            handler(val) {
                if (val) {
                    this.expandAllPanels();
                } else {
                    this.closeAllPanels();
                }
            },
            immediate: true,
        },
    },
    mounted() {
        // Handled by FE, tech debt move this to BE
        this.handleSetPanels(this.localPanels);
    },
    methods: {
        handleSetPanels: call("pageConfigs/handleSetPanels"),

        expandAllPanels() {
            this.panels = [...this.localPanels];
        },
        closeAllPanels() {
            this.panels = [];
        },
    },
});
</script>

<style lang="scss" scoped>
.wrapper-container {
    gap: 8px;
}
</style>
