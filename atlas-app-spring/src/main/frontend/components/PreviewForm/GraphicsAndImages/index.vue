<template>
    <Loading v-if="isLoading" />
    <GraphicsAndImagesWrapper v-else />
</template>

<script>
import { get } from "vuex-pathify";
import Loading from "./Loading.vue";
import GraphicsAndImagesWrapper from "./GraphicsAndImagesWrapper.vue";

export default {
    name: "GraphicsAndImages",
    components: { GraphicsAndImagesWrapper, Loading },
    props: {
        pageData: {
            type: Object,
            required: false,
            default: null,
        },
    },
    computed: {
        getPageBuilderLoader: get("pageConfigs/loader"),
        getFormDefaultsLoader: get("pageConfigs/form@loader"),
        isLoading() {
            return this.getPageBuilderLoader.isLoading || this.getFormDefaultsLoader.isLoading;
        },
    },
};
</script>
