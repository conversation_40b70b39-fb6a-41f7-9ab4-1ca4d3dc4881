<template>
    <div class="overlay-preview">
        <v-img class="overlay-img" :src="overlaySettingImageUrl" />
    </div>
</template>

<script>
import CompactPreview from "./images/compact_preview.png";
import DesktopPreview from "./images/desktop_preview.png";
import OverlayPreview from "./images/overlay_preview.png";

export default {
    name: "Preview",
    props: {
        form: {
            type: Object,
            required: true,
        },
    },
    computed: {
        overlaySettingImageUrl() {
            if (this.form.integrationMethod === "OVERLAY" && this.form.displayType === "COMPACT") {
                return CompactPreview;
            } else if (this.form.integrationMethod === "TAB") {
                return DesktopPreview;
            }
            return OverlayPreview;
        },
    },
};
</script>

<style scoped lang="scss">
.overlay-preview {
    display: flex;
    flex-direction: column;
    align-items: center;

    .overlay-img {
        max-width: 100%;
        max-height: 100%;
        overflow: hidden;
        flex-shrink: 0;
        display: inline-block;
    }
}
</style>
