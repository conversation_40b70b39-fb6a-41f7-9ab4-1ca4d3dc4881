<template>
    <Loading v-if="isLoading" />
    <OverlaySettingsWrapper v-else />
</template>

<script>
import { get } from "vuex-pathify";
import Loading from "./Loading.vue";
import OverlaySettingsWrapper from "./OverlaySettingsWrapper.vue";

export default {
    name: "OverlaySettings",
    components: { OverlaySettingsWrapper, Loading },
    props: {
        pageData: {
            type: Object,
            required: false,
            default: null,
        },
    },
    computed: {
        getPageBuilderLoader: get("pageConfigs/loader"),
        getFormDefaultsLoader: get("pageConfigs/form@loader"),
        isLoading() {
            return this.getPageBuilderLoader.isLoading || this.getFormDefaultsLoader.isLoading;
        },
    },
};
</script>
