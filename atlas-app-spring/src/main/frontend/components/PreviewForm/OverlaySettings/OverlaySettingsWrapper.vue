<template>
    <v-expansion-panels v-model="panels" class="d-flex flex-column wrapper-container" accordion multiple>
        <PreviewBuilder
            title="Display Settings"
            description="This determines how CarSaver's digital retail application will integrate with your website."
        >
            <template #preview>
                <Preview :form="form" class="preview" />
            </template>

            <template #configuration>
                <v-expansion-panels v-model="subPanels" accordion multiple class="configuration">
                    <SubCollapsible
                        title="Integration Method"
                        description="This determines where users engage with the digital retail experience."
                    >
                        <template #body>
                            <CustomToggleGroup
                                id="integrationMethod"
                                v-model="form.integrationMethod"
                                :rules="[(v) => !!v || 'Integration Method is required']"
                                :list="integrationMethodOptions"
                                class="mb-6"
                                @input="updateIntegrationFormData"
                            />

                            <div v-if="form.integrationMethod === 'OVERLAY'" class="note">
                                <span class="note-title">On-Screen Overlay*</span>
                                This will display the digital retail application as an overlay. Users can engage the
                                digital retail experience without leaving your website.
                                <span class="mt-1 disclaimer">
                                    *User browser settings for blocking cookies or not allowing cross-website traffic
                                    will result in users being re-directed to a new browser tab.
                                </span>
                            </div>
                            <div v-if="form.integrationMethod === 'TAB'" class="note">
                                <span class="note-title">New Browser Tab</span>
                                Launching the new digital retail application will take users to a new browser tab.
                            </div>
                        </template>
                    </SubCollapsible>

                    <template v-if="form.integrationMethod !== 'TAB'">
                        <SubCollapsible
                            title="Desktop Display Type"
                            description="This determines the formatting of your digital retail experience when users are on a desktop computer or larger tablet."
                        >
                            <template #body>
                                <CustomToggleGroup
                                    id="displayType"
                                    v-model="form.displayType"
                                    :rules="[(v) => !!v || 'Desktop Display Type is required']"
                                    :list="displayTypeOptions"
                                    class="mb-6"
                                    @input="updateDisplayTypeFormData"
                                />

                                <div v-if="form.displayType === 'COMPACT'" class="note">
                                    <span class="note-title">Compact View</span>
                                    This will provide users with the mobile view on both desktop and mobile devices.
                                </div>
                                <div v-if="form.displayType === 'EXPANDED'" class="note">
                                    <span class="note-title">Expanded View</span>
                                    This will allow users to see an expanded view when using a desktop computer or
                                    larger tablet device.
                                </div>
                            </template>
                        </SubCollapsible>
                    </template>
                </v-expansion-panels>
            </template>

            <template #footer>
                <update-btn :loading="saveButtonLoading.isLoading" @clicked="handleSubmit" />
            </template>
        </PreviewBuilder>
    </v-expansion-panels>
</template>

<script>
import { get, sync, call } from "vuex-pathify";
import PreviewBuilder from "Components/ConfigFormBuilder/PreviewFormBuilder/index.vue";
import Preview from "./Preview.vue";
import CustomToggleGroup from "./CustomToggleGroup.vue";
import SubCollapsible from "Components/CollapsibleTypes/SubCollapsible/index.vue";
import UpdateBtn from "Components/UpdateBtn.vue";

export default {
    name: "OverlaySettingsWrapper",
    components: {
        UpdateBtn,
        SubCollapsible,
        PreviewBuilder,
        Preview,
        CustomToggleGroup,
    },
    props: {
        pageData: {
            type: Object,
            required: false,
            default: null,
        },
    },
    data() {
        return {
            localPanels: [0],
            localSubPanels: [0, 1],
            valid: false,
            form: {
                integrationMethod: "OVERLAY",
                displayType: "EXPANDED",
            },
            integrationMethodOptions: [
                { label: "NEW BROWSER TAB", value: "TAB" },
                { label: "ON-SCREEN OVERLAY", value: "OVERLAY" },
            ],
            displayTypeOptions: [
                { label: "COMPACT VIEW", value: "COMPACT" },
                { label: "EXPANDED VIEW", value: "EXPANDED" },
            ],
        };
    },
    computed: {
        getPageBuilderLoader: get("pageConfigs/loader"),
        getFormDefaultsLoader: get("pageConfigs/form@loader"),
        panels: sync("pageConfigs/panels"),
        subPanels: sync("pageConfigs/subPanels"),
        expandAll: sync("pageConfigs/expandAll"),
        formData: sync("pageConfigs/form@data"),
        formDefaults: get("pageConfigs/form@defaults"),
        flattenedPageBuilderData: sync("pageConfigs/flattenedPageBuilderData"),
        saveButtonLoading: get("pageConfigs/saveForm@loader"),
        userId: get("loggedInUser/userId"),
        pageName: get("pageConfigs/pageBuilderData@page"),
        selectedDealer: get("loggedInUser/selectedDealer"),
        dealerId() {
            return this.selectedDealer?.id || this.$route.params.dealerId;
        },
        isInitLoadingComplete() {
            return this.getPageBuilderLoader.isComplete && this.getFormDefaultsLoader.isComplete;
        },
    },
    watch: {
        isInitLoadingComplete: {
            handler(val) {
                if (val) {
                    this.initFormData();
                }
            },
            immediate: true,
        },
        expandAll: {
            handler(val) {
                if (val) {
                    this.expandAllPanels();
                } else {
                    this.closeAllPanels();
                }
            },
            immediate: true,
        },
        "form.integrationMethod": {
            handler(newValue) {
                if (newValue === "OVERLAY") {
                    // Ensure the Desktop Display Type section is expanded when switching back to OVERLAY
                    if (!this.subPanels.includes(1)) {
                        this.subPanels.push(1);
                    }
                }
            },
            immediate: true,
        },
    },
    mounted() {
        this.handleSetPanels(this.localPanels);
        this.handleSetSubPanels(this.localSubPanels);
    },
    methods: {
        handleSetPanels: call("pageConfigs/handleSetPanels"),
        handleSetSubPanels: call("pageConfigs/handleSetSubPanels"),
        updateFormData: call("pageConfigs/updateFormData"),
        saveFormData: call("pageConfigs/saveFormData"),
        initFormData() {
            this.formData = { ...this.flattenedPageBuilderData, ...this.formDefaults };
            this.form.integrationMethod = this.formData?.integrationMethod || "OVERLAY";
            this.form.displayType = this.formData?.displayType || "EXPANDED";
        },
        expandAllPanels() {
            this.expandPanels();
            this.expandAllSubPanels();
        },
        closeAllPanels() {
            this.panels = [];
        },
        expandPanels() {
            this.panels = [...this.localPanels];
        },
        expandAllSubPanels() {
            this.subPanels = [...this.localSubPanels];
        },
        handleSubmit() {
            this.saveFormData({
                userId: this.userId,
                page: this.pageName || "Overlay Settings",
                dealerId: this.dealerId,
            });
        },
        updateDisplayTypeFormData() {
            this.updateFormData({
                key: "displayType",
                value: this.form.displayType,
            });
        },
        updateIntegrationFormData() {
            this.updateFormData({
                key: "integrationMethod",
                value: this.form.integrationMethod,
            });
        },
    },
};
</script>

<style scoped lang="scss">
.preview-wrapper {
    display: flex;
    justify-content: center;
}

.preview {
    width: 460px;
    margin: 0 auto;
}

.configuration {
    display: flex;
    flex-direction: column;
    gap: 16px;
}
.disclaimer {
    font-size: px2rem(12);
    font-style: italic;
}
.note {
    display: flex;
    flex-direction: column;
    font-size: px2rem(14);
    font-style: normal;
    line-height: 20px;

    .note-title {
        font-weight: 600;
        margin-bottom: 4px;
    }
}

@media (max-width: 768px) {
    .card-group {
        flex-direction: column !important;
    }

    .preview {
        margin-bottom: 16px;
        width: 100%;
        max-width: 460px;
    }
}
</style>
