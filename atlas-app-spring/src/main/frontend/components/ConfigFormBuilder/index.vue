<template>
    <v-expansion-panels v-model="panels" accordion multiple>
        <collapsible
            v-for="(section, index) in sections"
            :key="index"
            class="mb-4"
            :title="section.title"
            :description="section.description"
        >
            <template #body>
                <v-expansion-panels v-model="subPanels[section.title]" accordion multiple>
                    <template v-for="subSection in section.components">
                        <SectionWell
                            v-if="subSection.type === 'WELL'"
                            :key="subSection.title?.value"
                            :title="subSection.title?.value"
                            :subtitle="subSection.description?.value"
                        />
                        <sub-collapsible
                            v-else
                            :key="subSection.title?.value"
                            class="mb-4"
                            :title="subSection.title?.value"
                            :post-text="subSection.title?.postText"
                            :description="subSection.description?.value"
                            :expand="panels"
                            :video="subSection.documentation?.url"
                            :collapsible="subSection?.collapsible"
                        >
                            <template #body>
                                <FormInputBuilder :context="subSection" />
                                <div v-if="debugMode">
                                    <div v-for="input in subSection.inputs" :key="input.key" class="formWrapper my-5">
                                        <p class="mb-2 font-weight-bold">Mapped:</p>
                                        <div>Default: {{ getDefaultValue(input.key) }}</div>
                                        <div>Description: {{ input.description }}</div>
                                        <div>Groups: {{ input.groups }}</div>
                                        <div>Group: {{ input.group }}</div>
                                        <div>Hint: {{ input.hint }}</div>
                                        <div>Key: {{ input.key }}</div>
                                        <div>Label: {{ input.label }}</div>
                                        <div>Multiplier: {{ input.multiplier }}</div>
                                        <div>Origin Type: {{ input.originType }}</div>
                                        <div>Placeholder: {{ input.placeholder }}</div>
                                        <div>Post text: {{ input.postText }}</div>
                                        <div>Result Label: {{ input.resultLabel }}</div>
                                        <div>Type: {{ input.type }}</div>

                                        <div class="my-4">
                                            <p class="mb-2 font-weight-bold">Raw:</p>
                                            <p>{{ input }}</p>
                                        </div>
                                        <v-divider class="my-3"></v-divider>
                                    </div>
                                </div>
                            </template>
                            <template v-if="subSection.footer" #footer>
                                <div v-if="debugMode">
                                    <div class="formWrapper my-5">
                                        <p class="mb-2 font-weight-bold">Mapped:</p>
                                        <div>key: {{ subSection.footer.key }}</div>
                                        <div>type: {{ subSection.footer.type }}</div>
                                        <div>value: {{ subSection.footer.value }}</div>

                                        <div class="my-4">
                                            <p class="mb-2 font-weight-bold">Raw:</p>
                                            <p>{{ subSection.footer }}</p>
                                        </div>
                                        <v-divider class="my-3"></v-divider>
                                    </div>
                                </div>
                                <Footer :content="subSection.footer.value" />
                            </template>
                        </sub-collapsible>
                    </template>
                </v-expansion-panels>
            </template>
        </collapsible>
    </v-expansion-panels>
</template>
<script>
import { get, sync } from "vuex-pathify";
import Collapsible from "Components/CollapsibleTypes/Collapsible";
import SubCollapsible from "Components/CollapsibleTypes/SubCollapsible";
import FormInputBuilder from "./components/FormInputBuilder";
import Footer from "Components/ConfigFormBuilder/components/Footer.vue";
import SectionWell from "./components/SectionWell.vue";

export default {
    name: "ConfigFormBuilder",
    components: { Footer, Collapsible, SubCollapsible, FormInputBuilder, SectionWell },
    props: {},
    data() {
        return {};
    },
    computed: {
        panels: sync("pageConfigs/panels"),
        subPanels: sync("pageConfigs/subPanels"),
        allOpenedSubPanelsState: sync("pageConfigs/allOpenedSubPanelsState"),
        allOpenedPanelsState: sync("pageConfigs/allOpenedPanelsState"),
        expandAll: get("pageConfigs/expandAll"),
        debugMode: get("pageConfigs/debugMode"),
        builderData: get("pageConfigs/pageBuilderData"),
        formDefaults: get("pageConfigs/form@defaults"),
        loader: get("pageConfigs/loader"),
        sections() {
            let sections = [];

            if (this.builderData?.sections?.length > 0) {
                sections = this.builderData.sections;
            }

            return sections;
        },
    },
    watch: {
        loader: {
            handler(val) {
                if (val.isComplete) {
                    // this.init();
                }
            },
            immediate: true,
        },
        expandAll: {
            handler(val) {
                if (val) {
                    this.expandAllPanels();
                    this.expandAllSubPanels();
                } else {
                    this.closeAllPanels();
                    this.closeAllSubPanels();
                }
            },
            immediate: true,
        },
    },
    created() {},
    methods: {
        init() {
            this.setupPanels();
            this.setupSubPanels();
        },
        getDefaultValue(key) {
            return this.formDefaults[key] ? this.formDefaults[key] : null;
        },
        expandAllSubPanels() {
            this.subPanels = { ...this.allOpenedSubPanelsState };
        },
        expandAllPanels() {
            this.panels = [...this.allOpenedPanelsState];
        },
        setupPanels() {
            this.allOpenedPanelsState = [...this.sections.keys()];
        },
        setupSubPanels() {
            // const initialPanelState = null;
            this.sections.forEach((section) => {
                let sectionTitle = section.title;
                this.allOpenedSubPanelsState[sectionTitle] = [];

                section.components.forEach((component, index) => {
                    this.allOpenedSubPanelsState[sectionTitle].push(index);
                });
            });
        },

        expandSubPanels() {
            this.subPanels = [...this.sections.keys()];
        },
        closeAllPanels() {
            this.panels = [];
        },
        closeAllSubPanels() {
            this.subPanels = {};
        },
    },
};
</script>
<style lang="scss" scoped>
.no-box-shadow,
.no-box-shadow::before {
    box-shadow: none !important;
}

.formWrapper {
    line-height: 1.4;
}

.v-expansion-panels {
    > div {
        width: 100% !important;
    }
}
</style>
