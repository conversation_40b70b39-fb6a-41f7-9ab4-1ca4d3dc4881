<template>
    <SubCollapsible>
        <template #title> {{ title }} </template>
        <template #description>
            {{ description }}
        </template>
        <template #body>
            <div>
                <div class="card-group">
                    <div class="card-item">
                        <slot name="preview" />
                    </div>

                    <Configuration class="card-item">
                        <slot name="configuration" />
                    </Configuration>
                </div>

                <div class="card-footer">
                    <slot name="footer" />
                </div>
            </div>
        </template>
    </SubCollapsible>
</template>

<script>
import { defineComponent } from "vue";
import Configuration from "Components/ConfigFormBuilder/PreviewFormBuilder/components/Configuration.vue";
import SubCollapsible from "Components/CollapsibleTypes/SubCollapsible/index.vue";

export default defineComponent({
    name: "CollapsiblePreviewFormBuilder",
    components: { SubCollapsible, Configuration },
    props: {
        title: {
            type: String,
            required: false,
            default: "Title",
        },
        description: {
            type: String,
            required: false,
            default: "",
        },
        value: {
            type: [Number, String],
            required: false,
            default: "",
        },
    },
});
</script>

<style scoped lang="scss">
@import "~vuetify/src/styles/settings/_variables";
.card-group {
    display: flex;
    flex-direction: row;
    padding-bottom: 0;
    gap: 16px;
    @media #{map-get($display-breakpoints, 'md-and-down')} {
        flex-direction: column;
    }
}
.card-item {
    flex-basis: 50%;
    place-self: stretch;
}
.card-footer {
    display: flex;
    flex-direction: row-reverse;
    gap: 10px;
    margin-top: 16px;
    @media #{map-get($display-breakpoints, 'md-and-down')} {
        flex-direction: column-reverse;
        align-items: flex-end;
        button {
            width: fit-content;
        }
    }
}
</style>
