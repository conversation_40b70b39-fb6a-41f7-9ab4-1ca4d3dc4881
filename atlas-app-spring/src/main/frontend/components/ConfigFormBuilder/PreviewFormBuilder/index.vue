<template>
    <Collapsible>
        <template #title>
            {{ title }}
        </template>
        <template #description>
            {{ description }}
        </template>
        <template #body>
            <v-card flat color="#f5f5f5">
                <template v-if="$slots.header">
                    <slot name="header" />
                </template>

                <slot name="sub-header" />

                <v-card-text class="card-group">
                    <Preview class="card-item">
                        <slot name="preview" />
                    </Preview>
                    <Configuration class="card-item">
                        <slot name="configuration" />
                    </Configuration>
                </v-card-text>

                <v-card-actions class="card-footer">
                    <slot name="footer" />
                </v-card-actions>
            </v-card>
        </template>
    </Collapsible>
</template>

<script>
import { defineComponent } from "vue";
import Preview from "Components/ConfigFormBuilder/PreviewFormBuilder/components/Preview.vue";
import Configuration from "Components/ConfigFormBuilder/PreviewFormBuilder/components/Configuration.vue";
import Collapsible from "Components/CollapsibleTypes/Collapsible";
export default defineComponent({
    name: "PreviewBuilder",
    components: { Configuration, Preview, Collapsible },
    props: {
        title: {
            type: String,
            required: false,
            default: "Title",
        },
        description: {
            type: String,
            required: false,
            default: "Description",
        },
    },
});
</script>

<style scoped lang="scss">
@import "~vuetify/src/styles/settings/_variables";
.card-group {
    display: flex;
    flex-direction: row;
    padding-bottom: 0;
    padding: 8px 0 0 0 !important;
    gap: 16px;
    @media #{map-get($display-breakpoints, 'md-and-down')} {
        flex-direction: column-reverse;
    }
}
.card-item {
    flex: 1;
}
.card-footer {
    display: flex;
    flex-direction: row-reverse;
    padding: 0 24px 24px 24px;
    margin-top: 16px;
}
</style>
