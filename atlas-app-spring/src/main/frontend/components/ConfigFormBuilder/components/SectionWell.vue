<template>
    <div class="well mb-4">
        <p class="title">
            <slot name="title">{{ title }}</slot>
        </p>
        <p class="subtitle" :class="{ 'font-italic': italicSubtitle }">
            <slot name="subtitle">{{ subtitle }}</slot>
        </p>
    </div>
</template>

<script>
export default {
    name: "SectionWell",
    props: {
        title: {
            type: String,
            default: null,
        },
        subtitle: {
            type: String,
            default: null,
        },
        italicSubtitle: {
            type: Boolean,
            default: false,
        },
    },
};
</script>

<style lang="scss" scoped>
.well {
    background-color: white;
    padding: 16px;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
    line-height: 1.4;

    .title {
        font-size: 1rem !important;
        line-height: 24px !important;
        margin-bottom: 0 !important;
    }

    .subtitle {
        font-size: 0.875rem !important;
        color: #616161 !important;
        margin-bottom: 0 !important;
    }
}
</style>
