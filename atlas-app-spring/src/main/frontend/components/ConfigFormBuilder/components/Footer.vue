<template>
    <div id="config-form-builder-footer">
        <slot>
            <p class="label">{{ content }}</p>
        </slot>
    </div>
</template>

<script>
import { defineComponent } from "vue";

export default defineComponent({
    name: "Footer",
    props: {
        content: {
            type: String,
            required: false,
            default: "",
        },
    },
});
</script>

<style lang="scss">
#config-form-builder-footer {
    .label {
        white-space: pre-wrap;
        color: var(--grey-grey-darken-2, #616161);
    }
}
</style>
