<template>
    <component
        :is="type2Component(input.type)"
        :id="id"
        :label="input.label"
        :items="input.allowedValues"
        :groups="input.groups"
        :value="resultantValue"
        :origin-type="input.originType"
        :result-label="input.resultLabel"
        :multiplier="input.multiplier"
        :placeholder="input.placeholder"
        :hint="input.hint"
        :description="input.description"
        :group="input.group"
        :sub-inputs="input.subInputs"
        :range="input.range"
        :disabled="disabled"
        :columns="input.columns"
        :table-body="input.tableBody"
        :actions="input.actions"
        :persistent-hint="persistent"
        :component-title="component.title?.value"
        :component-title-tooltip="component.title?.tooltip"
        :base-id="baseId"
        :rules="rules"
        :clear-value="clearValue"
        :required="required"
        :single-row="input.singleRow"
        :form-name="formName"
        class="input-component"
        @input="handleInput"
    ></component>
</template>
<script>
import { defineComponent } from "vue";
import { COMPONENT_MAPPING } from "Util/ConfigManager/components";
import { get } from "vuex-pathify";

export default defineComponent({
    name: "FormInput",
    components: {
        // Components are imported dynamically giving them recursive ability
        [COMPONENT_MAPPING.DROPDOWN]: () => import("Components/FormInputs/Dropdown"),
        [COMPONENT_MAPPING.TOGGLE]: () => import("Components/FormInputs/ToggleBtn"),
        [COMPONENT_MAPPING.INPUT_WITH_MULTIPLIER]: () => import("Components/FormInputs/InputWithMultiplier"),
        [COMPONENT_MAPPING.CHECKBOX]: () => import("Components/FormInputs/CheckboxGroup"),
        [COMPONENT_MAPPING.GROUPED_TOGGLE]: () => import("Components/FormInputs/GroupedToggle"),
        [COMPONENT_MAPPING.ENABLED_GROUP]: () => import("Components/FormInputs/EnabledGroup"),
        [COMPONENT_MAPPING.INPUT_COLOR]: () => import("Components/FormInputs/InputColor"),
        [COMPONENT_MAPPING.INPUT]: () => import("Components/FormInputs/InputField"),
        [COMPONENT_MAPPING.LABEL]: () => import("Components/FormInputs/Label"),
        [COMPONENT_MAPPING.INPUT_RANGE]: () => import("Components/FormInputs/InputRange"),
        [COMPONENT_MAPPING.RADIO]: () => import("Components/FormInputs/RadioBtn"),
        [COMPONENT_MAPPING.TIME_DURATION]: () => import("Components/FormInputs/TimeDurationInput.vue"),
        [COMPONENT_MAPPING.INPUT_MONEY]: () => import("Components/FormInputs/InputNumber.vue"),
        [COMPONENT_MAPPING.INPUT_PERCENTAGE]: () => import("Components/FormInputs/InputNumber.vue"),
        [COMPONENT_MAPPING.TABLE]: () => import("Components/FormInputs/ActionableTable.vue"),
        [COMPONENT_MAPPING.GROUPED_TERM]: () => import("Components/FormInputs/GroupedTerm.vue"),
        [COMPONENT_MAPPING.RATE_SHEET_CONFIGURATOR]: () =>
            import("Components/FormInputs/RateSheetConfigurator/index.vue"),
    },

    props: {
        input: {
            type: Object,
            default: () => ({}),
        },
        component: {
            type: Object,
            default: () => ({}),
            required: false,
        },
        disabled: {
            type: Boolean,
            required: false,
            default: false,
        },
        persistentHint: {
            type: Boolean,
            required: false,
            default: null,
        },
        baseId: {
            type: String,
            required: false,
            default: null,
        },
        rules: {
            type: Array,
            required: false,
            default: () => [],
        },
        required: {
            type: Boolean,
            required: false,
            default: false,
        },
        value: {
            type: [String, Number, null],
            required: false,
            default: null,
        },
        clearValue: {
            type: Boolean,
            required: false,
            default: false,
        },
        formName: {
            type: String,
            required: false,
            default: null,
        },
    },
    computed: {
        formDefaults: get("pageConfigs/form@defaults"),
        id() {
            return this.baseId ? `${this.baseId}/${this.input.key}` : this.input.key;
        },
        defaultValue() {
            const builderDefault = this.input.default ? this.input.default : null;
            const key = this.baseId ? `${this.baseId}/${this.input.key}` : this.input.key;
            const isGroupedToggle = this.input.type.toUpperCase() === "GROUPED_TOGGLE";
            const postFix = isGroupedToggle ? "/type" : "";
            const value = this.formDefaults[key + postFix];
            // console.log("key: ", key, value);  // use for debugging
            return value ? value : builderDefault;
        },
        resultantValue() {
            return this.value ? this.value : this.defaultValue;
        },
        persistent() {
            const hintValue = this.input.persistentHint;
            // If explicitly set (true or false), use that value.
            if (typeof hintValue === "boolean") {
                return hintValue;
            }
            // If not explicitly set (null or undefined) AND description exists, return true.
            // Note: Checks for null (prop default) or undefined (if key missing)
            if ((hintValue === null || hintValue === undefined) && this.input.description) {
                return true;
            }
            // Otherwise (not set & no description, or explicitly set to null/undefined without description), default to false.
            return false;
        },
    },
    methods: {
        type2Component(type) {
            return COMPONENT_MAPPING[type];
        },
        handleInput(value) {
            this.$emit("input", value);
        },
    },
});
</script>

<style scoped lang="scss">
.input-component {
    min-width: 45%;
}
</style>
