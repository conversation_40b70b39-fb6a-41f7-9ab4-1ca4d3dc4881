package com.carsaver.partner.web.api;


import com.carsaver.elasticsearch.model.DealerDoc;
import com.carsaver.magellan.client.BasicUserAssociationClient;
import com.carsaver.magellan.client.DealerClient;
import com.carsaver.partner.elasticsearch.DealerDocService;
import com.carsaver.partner.search.facets.DealerDocFacets;
import com.carsaver.partner.util.ExportHelper;
import com.carsaver.search.support.FacetInfoResult;
import com.carsaver.search.support.SearchResults;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpSession;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import uk.co.jemos.podam.api.PodamFactory;
import uk.co.jemos.podam.api.PodamFactoryImpl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.IntStream;

import static org.mockito.ArgumentMatchers.any;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@AutoConfigureMockMvc(addFilters = false)
@WebMvcTest(useDefaultFilters = false, includeFilters = @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = DealersApiController.class))
public class DealersApiControllerTest extends ControllerTest {

    public static final PodamFactory FACTORY = new PodamFactoryImpl();
    @MockBean
    private DealerDocService docService;

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    DealerClient dealerClient;

    @MockBean
    ExportHelper exportHelper;

    @MockBean
    BasicUserAssociationClient basicUserAssociationClient;

    private static final String DEALER_ID = "35bc2ba1-bcd5-4da0-bd96-6618483164f6" ;

    @Test
    void searchDealersTest() throws Exception {

        List<DealerDoc> dealerDocList  = this.getResulDealerDocList();
        SearchResults<DealerDoc> dealerDocSearchResults = SearchResults.<DealerDoc>builder().content(dealerDocList).build();

        MockHttpSession sessionAttr = getMockHttpSessionForDealerUser();
        Mockito.when(docService.search(any(), (Pageable) any())).thenReturn(dealerDocSearchResults);

        mockMvc.perform(post("/api/dealers/search")
                .session(sessionAttr)
                .queryParam("page", "1")
                .queryParam("sort", "name,desc")
                .queryParam("size", "20")
                .content("{\"searchMethods\":{},\"includes\":null,\"excludes\":null}")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON));
    }

    @Test
    void dealersFacetTest() throws Exception {
        DealerDocFacets dealerDocFacets = DealerDocFacets.builder().build();
        FacetInfoResult facetInfoResult = FacetInfoResult.builder().results(dealerDocFacets).build();

        Mockito.when(docService.facets(any(), any(), any())).thenReturn(facetInfoResult);

        MockHttpSession sessionAttr = getMockHttpSessionForDealerUser();
        mockMvc.perform(post("/api/dealers/facet_info")
                .session(sessionAttr)
                .queryParam("id", "1")
                .content("{\"searchMethods\":{},\"includes\":null,\"excludes\":null}")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON));
    }

    private List<DealerDoc> getResulDealerDocList( ) {
        List<DealerDoc> dealerDocList =new ArrayList<>();
        IntStream.range(0,5).forEach(i -> {
            DealerDoc dealerDoc = DealerDoc.builder()
                .id(DEALER_ID + i)
                .build();
            dealerDocList.add(dealerDoc);
        });
        return dealerDocList;
    }

}
