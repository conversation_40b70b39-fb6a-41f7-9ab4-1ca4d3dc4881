package com.carsaver.partner.web.api.user;

import com.carsaver.magellan.api.VehicleService;
import com.carsaver.magellan.client.DealerLinkClient;
import com.carsaver.magellan.client.FinancierClient;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.client.UserVehicleClient;
import com.carsaver.magellan.client.VehicleClient;
import com.carsaver.magellan.model.DealerLinkView;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.FinancierView;
import com.carsaver.magellan.model.chrome.MakeView;
import com.carsaver.magellan.model.chrome.ModelView;
import com.carsaver.magellan.model.chrome.StyleView;
import com.carsaver.magellan.model.user.CreditPreApproval;
import com.carsaver.magellan.model.user.CreditProfile;
import com.carsaver.magellan.model.user.UserVehicleView;
import com.carsaver.partner.exception.ForbiddenException;
import com.carsaver.partner.web.api.SecurityHelperService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.hateoas.CollectionModel;
import org.springframework.test.util.ReflectionTestUtils;
import uk.co.jemos.podam.api.PodamFactory;
import uk.co.jemos.podam.api.PodamFactoryImpl;

import javax.servlet.http.HttpSession;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class UserPreApprovalsApiControllerTest {

    private static final PodamFactory FACTORY = new PodamFactoryImpl();
    public static final String DEALER_ID_DEALER_LINK_1 = "DEALER_ID_DEALER_LINK_1";
    public static final String DEALER_ID_DEALER_LINK_2 = "DEALER_ID_DEALER_LINK_2";
    private final UserClient userClient = Mockito.mock(UserClient.class);
    private final UserVehicleClient userVehicleClient = Mockito.mock(UserVehicleClient.class);
    private final FinancierClient financierClient = Mockito.mock(FinancierClient.class);
    private final SecurityHelperService securityHelperService =Mockito.mock(SecurityHelperService.class);
    private final DealerLinkClient dealerLinkClient = Mockito.mock(DealerLinkClient.class);

    final UserPreApprovalsApiController enablePreApprovalFeatureController = new UserPreApprovalsApiController(
            userClient, userVehicleClient, financierClient, true,
            securityHelperService, dealerLinkClient);
    private final PodamFactory factory = new PodamFactoryImpl();
    private final DealerView DEALER = new DealerView();//Dealer is used by Spring security to check roles and permisions to endpoint
    private final String USER_ID = "USER_ID";
    private final HttpSession session = mock(HttpSession.class);

    @BeforeEach
    void beforeEach(){
        this.DEALER.setId(DEALER_ID_DEALER_LINK_1);
        DealerLinkView dealerLink1 = FACTORY.manufacturePojo(DealerLinkView.class);
        dealerLink1.setDealerId(DEALER_ID_DEALER_LINK_1);
        DealerLinkView dealerLink2 = FACTORY.manufacturePojo(DealerLinkView.class);
        dealerLink2.setDealerId(DEALER_ID_DEALER_LINK_2);
        when(dealerLinkClient.findAllByUser(any())).thenReturn(CollectionModel.of(List.of(dealerLink1, dealerLink2)));
    }

    @Test
    void getUserPreApprovalsDataCrossDealerTest() {
        final DealerView INVALID_DEALER = new DealerView();
        INVALID_DEALER.setId("INVALID_DEALER");
        doThrow(new ForbiddenException("Forbidden Exception thrown")).when(securityHelperService).checkPermission(any(), any(), anyString());
        assertThrows(ForbiddenException.class, () -> enablePreApprovalFeatureController.getUserPreApprovalsData(List.of(INVALID_DEALER.getId()), USER_ID, session));
    }

    @Test
    void getUserPreApprovalsDataDisableFeatureToggleTest() {
        UserPreApprovalsApiController disabledPreApprovalFeatureController = new UserPreApprovalsApiController(
                userClient, userVehicleClient, financierClient, false,
                securityHelperService, dealerLinkClient);
        var emptyList = disabledPreApprovalFeatureController.getUserPreApprovalsData(null, null, session);
        assertEquals(Collections.emptyList(), emptyList);
    }

    @Test
    void getUserPreApprovalsDataEmptyCreditProfileTest() {
        when(userClient.findCreditProfileByUser(ArgumentMatchers.anyString())).thenReturn(Optional.empty());
        var emptyList = enablePreApprovalFeatureController.getUserPreApprovalsData(List.of(DEALER.getId()), USER_ID, session);
        assertEquals(Collections.emptyList(), emptyList);
    }

    @Test
    void getUserPreApprovalsDataNullPreApprovalsTest() {
        CreditProfile creditProfileNullPreApprovals = factory.manufacturePojo(CreditProfile.class);
        creditProfileNullPreApprovals.setPreApprovals(null);
        when(userClient.findCreditProfileByUser(ArgumentMatchers.anyString())).thenReturn(Optional.of(creditProfileNullPreApprovals));
        var emptyList = enablePreApprovalFeatureController.getUserPreApprovalsData(List.of(DEALER.getId()), USER_ID, session);
        assertEquals(Collections.emptyList(), emptyList);
    }

    @Test
    void getUserPreApprovalsDataEmptyPreApprovalsTest() {
        CreditProfile creditProfileNullPreApprovals = factory.manufacturePojo(CreditProfile.class);
        creditProfileNullPreApprovals.setPreApprovals(Collections.emptyMap());
        when(userClient.findCreditProfileByUser(ArgumentMatchers.anyString())).thenReturn(Optional.of(creditProfileNullPreApprovals));
        var emptyList = enablePreApprovalFeatureController.getUserPreApprovalsData(List.of(DEALER.getId()), USER_ID, session);
        assertEquals(Collections.emptyList(), emptyList);
    }

    @Test
    void getUserPreApprovalsDataFinancierNotFoundTest() {
        CreditPreApproval creditPreApproval = factory.manufacturePojo(CreditPreApproval.class);
        CreditProfile creditProfileNullPreApprovals = factory.manufacturePojo(CreditProfile.class);
        creditProfileNullPreApprovals.setPreApprovals(Map.of(creditPreApproval.getFinancierId(), creditPreApproval));
        when(userClient.findCreditProfileByUser(ArgumentMatchers.anyString())).thenReturn(Optional.of(creditProfileNullPreApprovals));
        when(financierClient.findById(anyInt())).thenReturn(Optional.empty());
        List<UserPreApprovalsApiController.UserPreApproval> result = enablePreApprovalFeatureController.getUserPreApprovalsData(List.of(DEALER.getId()), USER_ID, session);
        assertEquals(1, result.size());
        assertEquals("Unknown Financier", result.get(0).getLenderName());
    }

    @Test
    void getUserPreApprovalsDataNotLinkedToPayoffQuoteTest() {
        CreditPreApproval creditPreApproval = factory.manufacturePojo(CreditPreApproval.class);
        creditPreApproval.setLinkedToPayoffQuoteVin(null);

        CreditProfile creditProfileNullPreApprovals = factory.manufacturePojo(CreditProfile.class);
        creditProfileNullPreApprovals.setPreApprovals(Map.of(creditPreApproval.getFinancierId(), creditPreApproval));
        when(userClient.findCreditProfileByUser(ArgumentMatchers.anyString())).thenReturn(Optional.of(creditProfileNullPreApprovals));

        FinancierView financier = new FinancierView();
        financier.setName("FINANCIER");
        when(financierClient.findById(anyInt())).thenReturn(Optional.of(financier));
        var preApprovalsList = enablePreApprovalFeatureController.getUserPreApprovalsData(List.of(DEALER.getId()), USER_ID, session);
        assertNotNull(preApprovalsList);
        assertEquals(1, preApprovalsList.size());
        var firstPreApproval = preApprovalsList.get(0);
        assertEquals(creditPreApproval.getApprovalNumber(), firstPreApproval.getAccountNumber());
        assertEquals(financier.getName(), firstPreApproval.getLenderName());
        assertNull(firstPreApproval.getCurrentPayment());
        assertNull(firstPreApproval.getRemainingNumberOfPayments());
        assertNull(firstPreApproval.getVehicle());
        assertNull(firstPreApproval.getFinanceType());
    }

    @Test
    void getUserPreApprovalsDataLinkedToPayoffQuoteAndUserVehicleNotFoundTest() {
        CreditPreApproval creditPreApproval = factory.manufacturePojo(CreditPreApproval.class);
        creditPreApproval.setLinkedToPayoffQuoteVin("VIN123");
        CreditProfile creditProfileNullPreApprovals = factory.manufacturePojo(CreditProfile.class);
        creditProfileNullPreApprovals.setPreApprovals(Map.of(creditPreApproval.getFinancierId(), creditPreApproval));
        when(userClient.findCreditProfileByUser(ArgumentMatchers.anyString())).thenReturn(Optional.of(creditProfileNullPreApprovals));

        final FinancierView financier = new FinancierView();
        financier.setName("FINANCIER");

        when(financierClient.findById(anyInt())).thenReturn(Optional.of(financier));
        when(userVehicleClient.findByUserAndVin(anyString(), anyString())).thenReturn(Optional.empty());
        List<UserPreApprovalsApiController.UserPreApproval> result = enablePreApprovalFeatureController.getUserPreApprovalsData(List.of(DEALER.getId()), USER_ID, session);

        assertEquals(1, result.size());
        UserPreApprovalsApiController.UserPreApproval userPreApproval = result.get(0);

        assertNull(userPreApproval.getVehicle());
        assertNull(userPreApproval.getCurrentPayment());
        assertNull(userPreApproval.getRemainingNumberOfPayments());
        assertNull(userPreApproval.getFinanceType());
    }

    //TODO: this test throws an Arithmetic error on in CLI build- making ticket to fix, need build for release
    void getUserPreApprovalsDataLinkedToPayoffQuoteTest() {
        CreditPreApproval creditPreApproval = factory.manufacturePojo(CreditPreApproval.class);
        CreditProfile creditProfileNullPreApprovals = factory.manufacturePojo(CreditProfile.class);
        creditProfileNullPreApprovals.setPreApprovals(Map.of(creditPreApproval.getFinancierId(), creditPreApproval));
        when(userClient.findCreditProfileByUser(ArgumentMatchers.anyString())).thenReturn(Optional.of(creditProfileNullPreApprovals));

        final FinancierView financier = new FinancierView();
        financier.setName("FINANCIER");
        when(financierClient.findById(anyInt())).thenReturn(Optional.of(financier));

        final UserVehicleView tradeInVehicle = factory.manufacturePojo(UserVehicleView.class);
        final VehicleClient vehicleClient = mock(VehicleClient.class);
        ReflectionTestUtils.setField(tradeInVehicle, "vehicleClient", vehicleClient);

        final StyleView styleView = factory.manufacturePojo(StyleView.class);
        final VehicleService vehicleService = mock(VehicleService.class);
        ReflectionTestUtils.setField(styleView, "vehicleService", vehicleService);
        final ModelView modelView = factory.manufacturePojo(ModelView.class);
        ReflectionTestUtils.setField(modelView, "vehicleService", vehicleService);
        when(vehicleService.getModel(anyInt())).thenReturn(modelView);
        when(vehicleService.getMake(anyInt())).thenReturn(factory.manufacturePojo(MakeView.class));
        when(vehicleClient.getStyle(anyInt())).thenReturn(styleView);

        when(userVehicleClient.findByUserAndVin(anyString(), anyString())).thenReturn(Optional.ofNullable(tradeInVehicle));

        var preApprovalsList = enablePreApprovalFeatureController.getUserPreApprovalsData(List.of(DEALER.getId()), USER_ID, session);
        assertNotNull(preApprovalsList);
        assertEquals(1, preApprovalsList.size());
        var firstPreApproval = preApprovalsList.get(0);
        assertEquals(creditPreApproval.getApprovalNumber(), firstPreApproval.getAccountNumber());
        assertEquals(financier.getName(), firstPreApproval.getLenderName());
        assertEquals(tradeInVehicle.getMonthlyPayment(), firstPreApproval.getCurrentPayment());
        assertEquals(tradeInVehicle.getRemainingPayments(), firstPreApproval.getRemainingNumberOfPayments());
        assertEquals(tradeInVehicle.getYearMakeModel(), firstPreApproval.getVehicle());
        assertEquals(tradeInVehicle.getPurchaseType(), firstPreApproval.getFinanceType());
    }
}
