package com.carsaver.partner.web.api.user.elasticsearch;

import com.carsaver.elasticsearch.model.UserAndProspectDoc;
import com.carsaver.search.support.SearchResults;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.List;

public class LocaleServiceTest {

    LocaleService localeService = new LocaleService();

    @Test
    void setUserAndProspectDocLanguage_SetLanguageName_ShouldSetLanguage() {
        UserAndProspectDoc doc1 = new UserAndProspectDoc();
        doc1.setLocale("en");

        UserAndProspectDoc doc2 = new UserAndProspectDoc();
        doc2.setLocale("es");

        UserAndProspectDoc doc3 = new UserAndProspectDoc();

        SearchResults<UserAndProspectDoc> results = SearchResults.<UserAndProspectDoc>builder().build();
        results.setContent(List.of(doc1, doc2, doc3));

        localeService.setUserAndProspectDocLanguage(results);

        Assertions.assertEquals("English", doc1.getLocale());
        Assertions.assertEquals("Español", doc2.getLocale());
        Assertions.assertEquals("English", doc3.getLocale());
    }

    @Test
    void getLanguageNameByCode_WithValidCodes() {
        Assertions.assertEquals("English", localeService.getLanguageNameByCode("en"));
        Assertions.assertEquals("English", localeService.getLanguageNameByCode("EN"));
        Assertions.assertEquals("Español", localeService.getLanguageNameByCode("es"));
        Assertions.assertEquals("Español", localeService.getLanguageNameByCode("ES"));
    }

    @Test
    void testGetLanguageCodeByName_withValidNames() {
        Assertions.assertEquals("en", localeService.getLanguageCodeByName("English"));
        Assertions.assertEquals("en", localeService.getLanguageCodeByName("english"));
        Assertions.assertEquals("es", localeService.getLanguageCodeByName("Español"));
        Assertions.assertEquals("es", localeService.getLanguageCodeByName("Español"));
    }
}
