package com.carsaver.partner.web.api.user.elasticsearch;


import com.carsaver.elasticsearch.ElasticClient;
import com.carsaver.magellan.client.ZipCodeClient;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import com.carsaver.partner.util.SearchHelper;
import com.carsaver.partner.web.api.user.elasticsearch.model.SearchRequest;
import com.carsaver.search.model.SearchMethod;
import com.carsaver.search.support.FacetParser;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.data.domain.PageRequest;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.mock;

@Tag("integration")
class DealerUserElasticSearchServiceIntegrationTest {

    @Test
    public void test() {
        ElasticClient client = mock(ElasticClient.class);
        ZipCodeClient zipCodeClient = mock(ZipCodeClient.class);
        ExportService exportService = mock(ExportService.class);
        SplitFeatureFlags splitFeatureFlags = mock(SplitFeatureFlags.class);
        LocaleService localeService = mock(LocaleService.class);
        DealerUserElasticSearchService searchService = new DealerUserElasticSearchService(
            new CustomersDocService(client, new FacetParser(), zipCodeClient, splitFeatureFlags),
            true, exportService,
            new SearchHelper(), splitFeatureFlags,
            localeService
        );

        SearchRequest searchRequest = new SearchRequest();
        searchRequest.setDealerIds("aa0f1f3d-ec97-4bbe-9811-38365891043a");
        Map<String, SearchMethod> searchMethodMap = new HashMap<>();
        searchMethodMap.put("otherLeads", SearchMethod.NEGATIVE);
        searchMethodMap.put("connectionLeads", SearchMethod.POSITIVE);
        searchRequest.setSearchMethods(searchMethodMap);
        searchRequest.setOtherLeads(List.of("saved-to-garage"));
        searchRequest.setConnectionLeads(List.of("Financing"));
        searchService.search(searchRequest, PageRequest.of(1, 10));


    }

}
