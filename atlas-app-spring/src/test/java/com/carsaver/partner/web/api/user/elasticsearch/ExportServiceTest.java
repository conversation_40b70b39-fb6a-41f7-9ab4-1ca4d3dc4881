package com.carsaver.partner.web.api.user.elasticsearch;

import com.carsaver.elasticsearch.model.UserAndProspectDoc;
import com.carsaver.magellan.model.user.traits.UserTraits;
import com.carsaver.partner.util.ExportHelper;
import com.carsaver.partner.web.api.user.elasticsearch.model.CriteriaFilterRequest;
import org.elasticsearch.common.unit.TimeValue;
import org.junit.jupiter.api.Test;
import org.springframework.mock.web.MockHttpServletResponse;
import uk.co.jemos.podam.api.DefaultClassInfoStrategy;
import uk.co.jemos.podam.api.PodamFactory;
import uk.co.jemos.podam.api.PodamFactoryImpl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class ExportServiceTest {

    private final PodamFactory factory;


    public static final List<String> COLUMNS = Arrays.asList(
        "pname", "fn", "ln", "st", "em", "ca", "lla", "cd", "wvs", "lg", "wvbs", "tic", "ty", "tm", "tmo", "tv",
        "tmil", "te", "tpt", "tpd", "tp", "lnm", "tptp", "rp", "tt", "md", "t", "pac", "pq", "lc", "lt", "fasc",
        "sc", "pn", "sms", "voist", "voiit", "voic", "voiy", "voim", "voisn", "voimo", "tz", "addr", "addrst",
        "addrc", "addrs", "addrz", "dmn", "dmr", "dmc", "traits.activeVehiclesInGarageCount", "gsvc", "gvc",
        "traits.vehiclesInGarageCount", "traits.new", "traits.used", "stdt", "cs", "lcl"
    );

    public static final List<String> LABELS = Arrays.asList(
        "pname", "fn", "ln", "st", "em", "ca", "lla", "cd", "wvs", "lg", "wvbs", "tic", "ty", "tm", "tmo", "tv",
        "tmil", "te", "tpt", "tpd", "tp", "lnm", "tptp", "rp", "tt", "md", "t", "pac", "pq", "lc", "lt", "fasc",
        "sc", "pn", "sms", "voist", "voiit", "voic", "voiy", "voim", "voisn", "voimo", "tz", "addr", "addrst",
        "addrc", "addrs", "addrz", "dmn", "dmr", "dmc", "avigc", "gsvc", "gvc", "vigc", "dun", "duu", "stdt",
        "cs", "lcl"
    );

    private final CustomersDocService customersDocService = mock(CustomersDocService.class);
    private final ExportHelper exportHelper = new ExportHelper();
    private final LocaleService localeService = new LocaleService();
    private final TraitsService traitsService = mock(TraitsService.class);
    private final MockHttpServletResponse response = new MockHttpServletResponse();

    private final ExportService exportService = new ExportService(
        customersDocService,
        exportHelper,
        localeService,
        traitsService, 100);

    public ExportServiceTest() {

        DefaultClassInfoStrategy instance = DefaultClassInfoStrategy.getInstance();
        instance.addExcludedField(UserAndProspectDoc.class, "notes");
        instance.addExcludedField(UserAndProspectDoc.class, "userRefs");
        instance.addExcludedField(UserAndProspectDoc.class, "dealerLinks");
        instance.addExcludedField(UserAndProspectDoc.class, "signUpVehicle");
        instance.addExcludedField(UserAndProspectDoc.class, "vehicleOfInterest");
        instance.addExcludedField(UserAndProspectDoc.class, "globalInventoryVehicles");
        instance.addExcludedField(UserAndProspectDoc.class, "dealerInventoryVehicles");
        instance.addExcludedField(UserAndProspectDoc.class, "userLeads");
        instance.addExcludedField(UserAndProspectDoc.class, "mostRecentDealerCheckIn");
        this.factory = new PodamFactoryImpl();
        this.factory.setClassStrategy(instance);
    }

    @Test
    void testExportUserToCsv_Traits_ShouldExportTraits() throws Exception {

        CriteriaFilterRequest searchForm = new CriteriaFilterRequest();
        searchForm.setDealerAcls(List.of("dealerId"));

        UserAndProspectDoc userAndProspectDoc = getUserAndProspectDoc();
        UserTraits userTraits = userAndProspectDoc.getTraits();
        userTraits.setUserId(userAndProspectDoc.getId());

        when(customersDocService.scrollStream(searchForm, TimeValue.timeValueMinutes(1), 100)).thenReturn(Stream.of(userAndProspectDoc));
        when(traitsService.fetchUserTraits(List.of(userAndProspectDoc.getId()), Set.of("dealerId"))).thenReturn(List.of(userTraits));

        exportService.exportUserToCsv(response, COLUMNS, LABELS, searchForm, "America/New_York");

        verify(customersDocService).scrollStream(searchForm, TimeValue.timeValueMinutes(1), 100);
        verify(traitsService).fetchUserTraits(List.of(userAndProspectDoc.getId()), Set.of("dealerId"));

        var content = response.getContentAsString();
        List<Map<String, String>> maps = parseCSV(content);
        assertEquals(maps.get(0).get("dun"), "1");
        assertEquals(maps.get(0).get("avigc"), "5");
    }

    private UserAndProspectDoc getUserAndProspectDoc() {
        UserAndProspectDoc userAndProspectDoc = factory.manufacturePojo(UserAndProspectDoc.class);
        userAndProspectDoc.setLocale("en");

        userAndProspectDoc.getTraits().getDealerUserTraitsList().get(0).setActiveVehiclesInGarageCount(5);
        userAndProspectDoc.getTraits().getDealerUserTraitsList().get(0).setGarageSavedVehicleCount(5);
        userAndProspectDoc.getTraits().getDealerUserTraitsList().get(0).setStockTypeNewVehiclesInGarageCount(1);

        return userAndProspectDoc;
    }

    private static List<Map<String, String>> parseCSV(String content) throws IOException {
        List<Map<String, String>> data = new ArrayList<>();
        List<String> lines = content.lines().collect(Collectors.toList());

        if (lines.isEmpty()) {
            return data;
        }

        // Read headers
        String[] headers = lines.get(0).split(",");

        // Read data rows
        for (int i = 1; i < lines.size(); i++) {
            String[] values = lines.get(i).split(",");
            Map<String, String> rowMap = new HashMap<>();
            for (int j = 0; j < headers.length; j++) {
                rowMap.put(headers[j], j < values.length ? values[j] : ""); // Handle missing values
            }
            data.add(rowMap);
        }
        return data;
    }
}
