package com.carsaver.partner.web.api;

import com.carsaver.elasticsearch.model.UserAndProspectDoc;
import com.carsaver.elasticsearch.model.vehicle.VehicleDoc;
import com.carsaver.elasticsearch.service.UserAndProspectDocService;
import com.carsaver.magellan.api.DealerLinkService;
import com.carsaver.magellan.api.EmailValidationService;
import com.carsaver.magellan.api.LeadService;
import com.carsaver.magellan.auth.CarSaverUserDetails;
import com.carsaver.magellan.client.DealerLinkClient;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.model.Source;
import com.carsaver.magellan.model.UserView;
import com.carsaver.partner.elasticsearch.criteria.ProspectSearchCriteria;
import com.carsaver.partner.model.user.ExtendedUserView;
import com.carsaver.partner.service.LoggedInUserProgramService;
import com.carsaver.partner.service.SmsValidationService;
import com.carsaver.partner.service.UserDealerAssociationService;
import com.carsaver.partner.service.nesna.DealerNesnaProductsService;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import com.carsaver.partner.web.support.DealerShareSupport;
import com.carsaver.search.support.DocFacets;
import com.carsaver.search.support.PageMetadata;
import com.carsaver.search.support.SearchResults;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@AutoConfigureMockMvc(addFilters = false)
@WebMvcTest(useDefaultFilters = false, includeFilters = @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = {UserApiController.class, CarSaverUserDetails.class}))
class UserApiControllerTest extends ControllerTest {

    public static final String USER_ID = "jdoe";
    public static final String STAGE_TITLE = "Test";

    @MockBean
    private UserAndProspectDocService userAndProspectDocService;

    @MockBean
    private UserClient userClient;

    @MockBean
    private DealerLinkClient dealerLinkClient;

    @MockBean
    private DealerLinkService dealerLinkService;

    @MockBean
    private EmailValidationService emailValidationService;

    @MockBean
    private SmsValidationService smsValidationService;

    @MockBean
    private LeadService leadService;

    @MockBean
    private DealerShareSupport dealerShareSupport;

    @MockBean
    private LoggedInUserProgramService loggedInUserProgramSerivice;

    @MockBean
    private SecurityHelperService securityHelperService;

    @MockBean
    private UserDealerAssociationService userDealerAssociationService;

    @MockBean
    DealerNesnaProductsService dealerNesnaProductsService;

    @MockBean
    private SplitFeatureFlags splitFeatureFlags;
    @Autowired
    private MockMvc mockMvc;

    @AfterEach
    void cleanUp() {
        SecurityContextHolder.clearContext();
    }

    @Test
    void findUser() throws Exception {
        //404 no path ID
        final String empty = "";
        mockMvc.perform(get("/api/users/" + empty)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isNotFound());

        //200 path ID found user
        final String userId = "USER_ID";
        final UserView userView = new UserView();
        userView.setId(userId);
        when(userClient.findById(userId)).thenReturn(userView);
        mockMvc.perform(get("/api/users/" + userId)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk());

        //200 path ID not found user but matched source.prospectId
        final String prospectId = "PROSPECT_ID";
        final String userAndProspectId = "USER_AND_PROSPECT_ID";
        userView.setId(userAndProspectId);
        when(userClient.findById(userId)).thenReturn(null);
        when(userClient.findById(userAndProspectId)).thenReturn(userView);
        var userAndProspect = UserAndProspectDoc.builder()
            .source(Source.builder()
                .prospectId(prospectId).build())
            .id(userAndProspectId)
            .build();
        final SearchResults<UserAndProspectDoc> result = FACTORY.manufacturePojo(SearchResults.class, VehicleDoc.class);
        final Collection<UserAndProspectDoc> userAndProspects = List.of(userAndProspect);
        result.setContent(userAndProspects);
        result.setPageMetadata(null);
        result.setFacets(null);
        when(userAndProspectDocService.search((ProspectSearchCriteria) any(), (Pageable) any())).thenReturn(result);
        mockMvc.perform(get("/api/users/" + userId)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().is3xxRedirection());

        //404 path ID not found user neither matched source.prospectId
        final SearchResults<UserAndProspectDoc> noContentResult = FACTORY.manufacturePojo(SearchResults.class, VehicleDoc.class);
        noContentResult.setContent(Collections.emptyList());
        when(userAndProspectDocService.search((ProspectSearchCriteria) any(), (Pageable) any())).thenReturn(noContentResult);
        mockMvc.perform(get("/api/users/" + userId)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isNotFound());

    }

    @Test
    void setProspectPinAndClientAdvisorIfExistsTest() {
        reset(userAndProspectDocService);
        UserApiController controller = new UserApiController();
        ReflectionTestUtils.setField(controller, "userAndProspectDocService", userAndProspectDocService);
        assertThrows(NullPointerException.class, () -> controller.setProspectPinAndClientAdvisorIfExists(null));

        final DocFacets facets = FACTORY.manufacturePojo(DocFacets.class);
        final UserAndProspectDoc noProspectDoc = new UserAndProspectDoc();
        noProspectDoc.setId("USER_AND_PROSPECT_DOC_1");
        noProspectDoc.setType("USER");
        noProspectDoc.setStage(STAGE_TITLE);
        noProspectDoc.setRankedStage(1);
        final Collection<UserAndProspectDoc> emptyContent = Collections.checkedCollection(Collections.emptyList(), UserAndProspectDoc.class);
        final PageMetadata pageMetadata = new PageMetadata(1, 1, 1);
        final SearchResults<UserAndProspectDoc> result = FACTORY.manufacturePojo(SearchResults.class, UserAndProspectDoc.class);
        result.setContent(emptyContent);
        result.setPageMetadata(pageMetadata);
        result.setFacets(facets);
        when(userAndProspectDocService.search(any(), (Pageable) any())).thenReturn(result);

        ExtendedUserView user = new ExtendedUserView();
        user.setId("USER_ID");
        assertDoesNotThrow(() -> controller.setProspectPinAndClientAdvisorIfExists(user));

        final Collection<UserAndProspectDoc> contentNoProspectDoc = Collections.checkedCollection(Collections.singletonList(noProspectDoc), UserAndProspectDoc.class);
        result.setContent(contentNoProspectDoc);
        controller.setProspectPinAndClientAdvisorIfExists(user);
        assertNull(user.getPin(), "User was no created using a pin");
        assertNull(user.getClientAdvisor(), "Does not contain clientAdvisor");

        final UserAndProspectDoc prospectDoc = new UserAndProspectDoc();
        prospectDoc.setId("USER_AND_PROSPECT_DOC_1");
        prospectDoc.setType("USER");
        prospectDoc.setStage(STAGE_TITLE);
        prospectDoc.setRankedStage(1);
        prospectDoc.setPin("PIN");
        prospectDoc.setClientAdvisor("Mike Bourke");
        final Collection<UserAndProspectDoc> content = Collections.checkedCollection(Collections.singletonList(prospectDoc), UserAndProspectDoc.class);
        result.setContent(content);
        controller.setProspectPinAndClientAdvisorIfExists(user);
        assertEquals(prospectDoc.getPin(), user.getPin());
        assertEquals(prospectDoc.getClientAdvisor(), user.getClientAdvisor());
    }
}
