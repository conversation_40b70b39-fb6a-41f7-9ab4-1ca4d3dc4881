package com.carsaver.partner.web.api;

import com.carsaver.magellan.client.DealerClient;
import com.carsaver.magellan.client.request.DealerUpdateRequest;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.preferences.DealerPreferences;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;

@ExtendWith(MockitoExtension.class)
class DealerApiControllerTest {

    private final String ROUTE_ONE = "RouteOne";
    private final String DEALER_TRACK = "DealerTrack";

    private DealerView dealerView;

    private DealerClient dealerClient = mock(DealerClient.class);

    @InjectMocks
    private DealerApiController dealerApiController;

    @BeforeEach
    void setUp() {
        dealerView = new DealerView();
        dealerView.setId("21bc4613-3078-43ae-a456-1ad4d3e05eec");
    }

    @Test
    void updateDealerPreferencesLMSPreference_ErrorResponse_EmptyDealer() throws Exception {
        ResponseEntity<DealerPreferences> dealerPreferencesResponseEntity = dealerApiController.updateDealerPreferencesLMSPreference(dealerView, ROUTE_ONE);

        assertNotNull(dealerPreferencesResponseEntity);
        assertTrue(dealerPreferencesResponseEntity.getStatusCode() == HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @Test
    void updateDealerPreferencesLMSPreference_ErrorResponse_UpdateFails() throws Exception {
        Mockito.when(dealerClient.update(anyString(), any(DealerUpdateRequest.class))).thenReturn(null);
        ResponseEntity<DealerPreferences> dealerPreferencesResponseEntity = dealerApiController.updateDealerPreferencesLMSPreference(dealerView, ROUTE_ONE);

        assertTrue(dealerPreferencesResponseEntity.getStatusCode() == HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @Test
    void updateDealerPreferencesLMSPreference_OkResponse_UpdateSucceeds() throws Exception {
        DealerPreferences preferences = new DealerPreferences();
        preferences.setLmsPreference(ROUTE_ONE);
        dealerView.setPreferences(preferences);
        Mockito.when(dealerClient.update(anyString(), any(DealerUpdateRequest.class))).thenReturn(dealerView);

        ResponseEntity<DealerPreferences> dealerPreferencesResponseEntity = dealerApiController.updateDealerPreferencesLMSPreference(dealerView, ROUTE_ONE);

        assertTrue(dealerPreferencesResponseEntity.getStatusCode() == HttpStatus.OK);
    }

    @Test
    void updateDealerPreferencesLMSPreference_PreferencesMatchResult_ValidInputs() throws Exception {
        DealerPreferences preferences = new DealerPreferences();
        preferences.setLmsPreference(ROUTE_ONE);
        dealerView.setPreferences(preferences);

        Mockito.when(dealerClient.update(anyString(), any(DealerUpdateRequest.class))).thenReturn(dealerView);

        ResponseEntity<DealerPreferences> dealerPreferencesResponseEntity = dealerApiController.updateDealerPreferencesLMSPreference(dealerView, ROUTE_ONE);

        assertTrue(dealerPreferencesResponseEntity.getBody().equals(preferences));
    }

    @Test
    void updateDealerPreferencesLMSPreference_PreferencesDoNotMatchResult_BadRead() throws Exception {
        DealerPreferences preferences = new DealerPreferences();
        preferences.setLmsPreference(ROUTE_ONE);
        dealerView.setPreferences(preferences);

        Mockito.when(dealerClient.update(anyString(), any(DealerUpdateRequest.class))).thenReturn(dealerView);

        ResponseEntity<DealerPreferences> dealerPreferencesResponseEntity = dealerApiController.updateDealerPreferencesLMSPreference(dealerView, ROUTE_ONE);

        DealerPreferences preferencesExpected = new DealerPreferences();
        preferencesExpected.setLmsPreference(DEALER_TRACK);

        assertFalse(dealerPreferencesResponseEntity.getBody().equals(preferencesExpected));
    }

}
