package com.carsaver.partner.web.api;

import com.carsaver.magellan.client.AppointmentClient;
import com.carsaver.magellan.client.ConnectionClient;
import com.carsaver.magellan.client.DealerLinkClient;
import com.carsaver.magellan.model.AppointmentView;
import com.carsaver.magellan.model.CertificateView;
import com.carsaver.magellan.model.ConnectionView;
import com.carsaver.magellan.model.DealerLinkView;
import com.carsaver.magellan.model.lead.AppointmentRequest;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DealApiControllerTest {

    @InjectMocks
    private  DealApiController dealApiController;

     DealerLinkClient dealerLinkClient = mock(DealerLinkClient.class);

     ConnectionClient connectionClient = mock(ConnectionClient.class);

     AppointmentClient appointmentClient = mock(AppointmentClient.class);

    @Test
    void sendLeadData() {
        CertificateView certificateView = CertificateView.builder().dealerId("123").build();
        when(connectionClient.create(any())).thenReturn(new ConnectionView());
        when(dealerLinkClient.create((DealerLinkView) any())).thenReturn(new DealerLinkView());
        ResponseEntity result = dealApiController.sendLead(certificateView,new ConnectionView());
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());
    }

    @Test
    void scheduleAppointment()  {
        CertificateView certificateView = CertificateView.builder().dealerId("123").build();
        AppointmentRequest request = AppointmentRequest.builder().build();
        when(appointmentClient.schedule(request)).thenReturn(new AppointmentView());
        ResponseEntity result = dealApiController.scheduleAppointment(certificateView,request);
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());
    }

}
