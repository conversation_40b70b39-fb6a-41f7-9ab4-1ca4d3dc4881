package com.carsaver.partner.web.api;

import com.carsaver.partner.web.api.LeadApiController.DealerLeadSearchCriteria;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

public class LeadApiControllerTest extends ControllerTest {
    @Test
    void removeTestUsersFromSearchForm() {
        LeadApiController leadApiController = new LeadApiController();
        DealerLeadSearchCriteria dealerLeadSearchCriteria = mock(DealerLeadSearchCriteria.class);

        ReflectionTestUtils.setField(leadApiController, "excludeTestUsers", false);
        leadApiController.removeTestUsersFromSearchForm(dealerLeadSearchCriteria);

        // when in non-prod, do not remove test users
        verify(dealerLeadSearchCriteria, never()).getSearchMethods();

        ReflectionTestUtils.setField(leadApiController, "excludeTestUsers", true);
        leadApiController.removeTestUsersFromSearchForm(dealerLeadSearchCriteria);

        // when in prod, remove test users
        verify(dealerLeadSearchCriteria, atLeast(1)).getSearchMethods();
    }
}
