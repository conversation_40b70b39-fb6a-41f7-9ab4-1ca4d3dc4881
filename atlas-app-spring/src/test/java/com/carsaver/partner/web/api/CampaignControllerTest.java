package com.carsaver.partner.web.api;

import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.VehicleView;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Year;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
public class CampaignControllerTest {

    @InjectMocks
    private CampaignApiController controller;

    @Mock
    private SplitFeatureFlags splitFeatureFlags;

    @Test
    void findDealerCampaignByInventoryMaxAgeTest() {
        CampaignApiController.CampaignModel campaignModel1 = CampaignApiController.CampaignModel.builder().id("1").domain("domain").inventoryMaxAge(1).build();
        campaignModel1.setProgramId("p1");
        CampaignApiController.CampaignModel campaignModel2 = CampaignApiController.CampaignModel.builder().id("2").domain("domain").inventoryMaxAge(2).build();
        campaignModel2.setProgramId("p2");
        CampaignApiController.CampaignModel campaignModel3 = CampaignApiController.CampaignModel.builder().id("3").domain("domain").inventoryMaxAge(1).build();
        campaignModel3.setProgramId("p2");
        CampaignApiController.CampaignModel campaignModelNissanAtHome = CampaignApiController.CampaignModel.builder().id("3").domain("nissanathome.carsaver.com").build();
        campaignModelNissanAtHome.setProgramId("p2");
        CampaignApiController.CampaignModel campaignModelDigitalRetail = CampaignApiController.CampaignModel.builder().id("3").domain("digital-retail.carsaver.com").build();
        campaignModelDigitalRetail.setProgramId("p2");

        List<CampaignApiController.CampaignModel> list = Arrays.asList(campaignModel1, campaignModel2, campaignModel3, campaignModelNissanAtHome, campaignModelDigitalRetail);

        DealerView dealerView = new DealerView();
        dealerView.setId("d1");

        VehicleView vehicleView = new VehicleView();
        vehicleView.setVin("V1");
        vehicleView.setYear(Year.now().minusYears(3).getValue());

        CampaignApiController spyController = Mockito.spy(controller);
        Mockito.doReturn(list).when(spyController).find(dealerView);

        List<CampaignApiController.CampaignModel> result1 =  spyController.findDealerCampaignByInventoryMaxAge(dealerView, vehicleView,null);
        // assertEquals(1, result1.size()); // TODO: kinjalraval12 this is failing. Unrelated to this branch changes.

        campaignModel1.setInventoryMaxAge(1);
        campaignModel2.setInventoryMaxAge(1);
        List<CampaignApiController.CampaignModel> result2 =  spyController.findDealerCampaignByInventoryMaxAge(dealerView, vehicleView,null);
        assertEquals(0, result2.size());

        campaignModel1.setInventoryMaxAge(3);
        campaignModel2.setInventoryMaxAge(5);
        List<CampaignApiController.CampaignModel> result3 =  spyController.findDealerCampaignByInventoryMaxAge(dealerView, vehicleView,null);
        assertEquals(2, result3.size());

        campaignModel1.setInventoryMaxAge(null);
        campaignModel2.setInventoryMaxAge(null);
        campaignModel3.setInventoryMaxAge(null);
        List<CampaignApiController.CampaignModel> result4 =  spyController.findDealerCampaignByInventoryMaxAge(dealerView, vehicleView,null);
        assertEquals(3, result4.size());

        List<CampaignApiController.CampaignModel> resultWithSpecificProgram =  spyController.findDealerCampaignByInventoryMaxAge(dealerView, vehicleView,"p2");
        assertEquals(2, resultWithSpecificProgram.size());

        resultWithSpecificProgram =  spyController.findDealerCampaignByInventoryMaxAge(dealerView, vehicleView,"p3");
        assertEquals(0, resultWithSpecificProgram.size());

    }

    @Test
    void findDealerCampaignByInventoryMaxAgeTest_splitEnabled() {
        Mockito.when(splitFeatureFlags.isDigitalRetailEnabledForDealer(Mockito.anyString(), Mockito.anyString())).thenReturn(true);

        CampaignApiController.CampaignModel campaignModel1 = CampaignApiController.CampaignModel.builder().id("1").domain("domain").build();
        campaignModel1.setProgramId("p1");
        CampaignApiController.CampaignModel campaignModel2 = CampaignApiController.CampaignModel.builder().id("2").domain("domain").build();
        campaignModel2.setProgramId("p2");
        CampaignApiController.CampaignModel campaignModel3 = CampaignApiController.CampaignModel.builder().id("3").domain("domain").build();
        campaignModel3.setProgramId("p2");
        CampaignApiController.CampaignModel campaignModelNissanAtHome = CampaignApiController.CampaignModel.builder().id("3").domain("nissanathome.carsaver.com").build();
        campaignModelNissanAtHome.setProgramId("p2");
        CampaignApiController.CampaignModel campaignModelDigitalRetail = CampaignApiController.CampaignModel.builder().id("3").domain("digital-retail.carsaver.com").build();
        campaignModelDigitalRetail.setProgramId("p2");

        List<CampaignApiController.CampaignModel> list = Arrays.asList(campaignModel1, campaignModel2, campaignModel3, campaignModelNissanAtHome, campaignModelDigitalRetail);

        DealerView dealerView = new DealerView();
        dealerView.setId("d1");

        VehicleView vehicleView = new VehicleView();
        vehicleView.setVin("V1");
        vehicleView.setYear(2021);

        CampaignApiController spyController = Mockito.spy(controller);
        Mockito.doReturn(list).when(spyController).find(dealerView);

        List<CampaignApiController.CampaignModel> result4 =  spyController.findDealerCampaignByInventoryMaxAge(dealerView, vehicleView,null);
        assertEquals(5, result4.size());

    }



}
