package com.carsaver.partner.web.api.activity;

import com.carsaver.partner.service.activity.DigitalRetailActivityLogContainer;
import com.carsaver.partner.service.activity.ActivityLogsService;
import org.junit.jupiter.api.Test;
import org.springframework.http.ResponseEntity;

import java.util.Collections;
import java.util.List;

import static com.carsaver.partner.TestUtils.generate;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class ActivityLogDTOControllerTest {

    @Test
    void test() {
        ActivityLogsService mock = mock(ActivityLogsService.class);
        DigitalRetailActivityLogController controller = new DigitalRetailActivityLogController(mock);

        DigitalRetailActivityLogContainer container = generate(DigitalRetailActivityLogContainer.class);
        when(mock.process("userId", List.of("dealerId"))).thenReturn(List.of(container));

        var actual = controller.retrieveLogs("userId", List.of("dealerId"));
        assertEquals(200, actual.getStatusCodeValue());
        assertEquals(List.of(container), actual.getBody());


    }

}
