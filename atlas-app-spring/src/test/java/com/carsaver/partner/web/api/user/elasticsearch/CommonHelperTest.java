package com.carsaver.partner.web.api.user.elasticsearch;

import com.carsaver.partner.web.api.user.elasticsearch.model.CriteriaFilterRequest;
import com.carsaver.partner.web.api.user.elasticsearch.model.SearchRequest;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

class CommonHelperTest {

    @Test
    void handleMaturityDateFilter_test() {
        SearchRequest request = new SearchRequest();
        CriteriaFilterRequest searchForm = new CriteriaFilterRequest();

        // Test when withinMaturityDate is null
        CommonHelper.handleMaturityDateFilter(request, searchForm);
        assertNull(searchForm.getMaturityDate());

        // Test when withinMaturityDate is "12+"
        request.setWithinMaturityDate("12+");
        CommonHelper.handleMaturityDateFilter(request, searchForm);
        assertNotNull(searchForm.getMaturityDate());
        assertEquals(LocalDate.now(),searchForm.getMaturityDate().getStart());
        assertNull(searchForm.getMaturityDate().getEnd());

        // Test when withinMaturityDate is "12"
        request.setWithinMaturityDate("365");
        CommonHelper.handleMaturityDateFilter(request, searchForm);
        assertNotNull(searchForm.getMaturityDate());
        assertEquals(LocalDate.now(), searchForm.getMaturityDate().getStart());
        assertEquals(LocalDate.now().plusDays(365), searchForm.getMaturityDate().getEnd());


        // Test when withinMaturityDate is "90"
        request.setWithinMaturityDate("90");
        CommonHelper.handleMaturityDateFilter(request, searchForm);
        assertNotNull(searchForm.getMaturityDate());
        assertEquals(LocalDate.now(), searchForm.getMaturityDate().getStart());
        assertEquals(LocalDate.now().plusDays(90), searchForm.getMaturityDate().getEnd());


        // Test when value is not a number nor 12+
        request.setWithinMaturityDate("abc");
        assertThrows(NumberFormatException.class,()->CommonHelper.handleMaturityDateFilter(request, searchForm));

    }

    @Test
    void handleLastActiveFilter_test() {

        SearchRequest request = new SearchRequest();
        CriteriaFilterRequest searchForm = new CriteriaFilterRequest();

        // Test when withinLastActive is null
        CommonHelper.handleLastActiveFilter(request, searchForm);
        assertNull(searchForm.getLastActive());

        // Test when withinLastActive is "90+"
        request.setWithinLastActive("90+");
        CommonHelper.handleLastActiveFilter(request, searchForm);
        assertNotNull(searchForm.getLastActive());
        assertNull(searchForm.getLastActive().getStart());
        assertEquals(LocalDate.now(), searchForm.getLastActive().getEnd());

        // Test when withinLastActive is "90"
        request.setWithinLastActive("3");
        CommonHelper.handleLastActiveFilter(request, searchForm);
        assertNotNull(searchForm.getLastActive());
        assertEquals(LocalDate.now().minusDays(3), searchForm.getLastActive().getStart());
        assertEquals(LocalDate.now(), searchForm.getLastActive().getEnd());

        // Test when value is not a number nor 90+
        request.setWithinLastActive("abc");
        assertThrows(NumberFormatException.class,()->CommonHelper.handleLastActiveFilter(request, searchForm));
    }
}
