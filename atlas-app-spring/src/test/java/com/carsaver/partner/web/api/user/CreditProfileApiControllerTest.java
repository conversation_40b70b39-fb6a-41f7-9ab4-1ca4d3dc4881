package com.carsaver.partner.web.api.user;

import com.carsaver.magellan.client.CertificateClient;
import com.carsaver.magellan.client.FinancierClient;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.model.CertificateView;
import com.carsaver.magellan.model.FinancierView;
import com.carsaver.magellan.model.financiers.CreditTier;
import com.carsaver.magellan.model.user.CreditEvaluator;
import com.carsaver.magellan.model.user.CreditProfile;
import com.carsaver.magellan.model.user.CreditRange;
import org.junit.jupiter.api.Test;
import org.springframework.hateoas.PagedModel;
import uk.co.jemos.podam.api.PodamFactory;
import uk.co.jemos.podam.api.PodamFactoryImpl;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class CreditProfileApiControllerTest {

    public static final String USER_ID = "USER_ID";
    public static final String DEALER_ID = "DEALER_ID";
    public static final int CREDIT_SCORE = 750;
    private final UserClient userClient = mock(UserClient.class);
    private final CertificateClient certificateClient = mock(CertificateClient.class);
    private final boolean featureTogglePreApproval = true;
    private final CreditProfileApiController controller = new CreditProfileApiController(userClient, certificateClient, featureTogglePreApproval);

    PodamFactory podamFactory = new PodamFactoryImpl();
    @Test
    void getCreditProfile_emptyCredtiProfile() {
        Optional<CreditProfile> emptyCreditProfile = Optional.empty();
        when(userClient.findCreditProfileByUser(anyString())).thenReturn(emptyCreditProfile);
        when(certificateClient.findByUserAndDealer(anyString(),anyString(), any())).thenReturn(PagedModel.empty());
        assertDoesNotThrow(() -> {
            CreditProfileApiController.UserCreditProfile result = controller.getCreditProfile(USER_ID, DEALER_ID);
            assertNull(result.getCreditRange());
            assertNull(result.getCreditEvaluator());
            assertNull(result.getLenderName());
            assertNull(result.getApproved());
            assertNull(result.getPreApprovedByCaptiveLender());
            assertNull(result.getSoftPullFailureReason());
        });
    }

    @Test
    void getCreditProfile_creditProfileFound_noQuote_notPreapproved() {
        Optional<CreditProfile> creditProfile = Optional.of(CreditProfile.builder()
                .creditEvaluator(CreditEvaluator.BUREAU)
                .creditScore(CREDIT_SCORE)
                .birthYear(1990)
                .income(6000)
                .build());
        when(userClient.findCreditProfileByUser(anyString())).thenReturn(creditProfile);
        when(certificateClient.findByUserAndDealer(anyString(),anyString(), any())).thenReturn(PagedModel.empty());
        assertDoesNotThrow(() -> {
            CreditProfileApiController.UserCreditProfile result = controller.getCreditProfile(USER_ID, DEALER_ID);
            assertEquals(CreditRange.EXCELLENT, result.getCreditRange());
            assertEquals(CreditEvaluator.BUREAU, result.getCreditEvaluator());
            assertNull(result.getLenderName());
            assertNull(result.getApproved());
            assertFalse(result.getPreApprovedByCaptiveLender());
            assertNull(result.getSoftPullFailureReason());
        });
    }

    @Test
    void getCreditProfile_creditProfileFound_withQuote_notPreapproved() {
        Optional<CreditProfile> creditProfile = Optional.of(CreditProfile.builder()
                .creditEvaluator(CreditEvaluator.BUREAU)
                .creditScore(CREDIT_SCORE)
                .birthYear(1990)
                .income(6000)
                .build());
        when(userClient.findCreditProfileByUser(anyString())).thenReturn(creditProfile);

        final CertificateView certificateView = podamFactory.manufacturePojo(CertificateView.class);
        final FinancierClient financierClient = mock(FinancierClient.class);
        certificateView.setFinancierClient(financierClient);
        certificateView.getQuote().setBeacon(CREDIT_SCORE);


        final FinancierView financierView = podamFactory.manufacturePojo(FinancierView.class);
        final CreditTier scoreTier = new CreditTier();
        scoreTier.setId("CRISTIAN_TIER_ID");
        scoreTier.setMaxScore(751);
        scoreTier.setMinScore(749);
        scoreTier.setName("CRISTIAN_TIER_NAME");

        var tiers = List.of(scoreTier);
        financierView.getConfig().getCreditConfig().setTiers(tiers);
        when(financierClient.findById(anyInt())).thenReturn(Optional.ofNullable(financierView));

        when(certificateClient.findByUserAndDealer(anyString(),anyString(), any()))
                .thenReturn(PagedModel.of(Collections.singleton(certificateView), new PagedModel.PageMetadata(1,1,1)));

        assertDoesNotThrow(() -> {
            CreditProfileApiController.UserCreditProfile result = controller.getCreditProfile(USER_ID, DEALER_ID);
            assertEquals(CreditRange.EXCELLENT, result.getCreditRange());
            assertEquals(CreditEvaluator.BUREAU, result.getCreditEvaluator());
            assertNotNull(result.getLenderName());
            assertNull(result.getApproved());
            assertFalse(result.getPreApprovedByCaptiveLender());
            assertNull(result.getSoftPullFailureReason());
            assertEquals("CRISTIAN_TIER_NAME",result.getLenderTier());
        });
    }
}