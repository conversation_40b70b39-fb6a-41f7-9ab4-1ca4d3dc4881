package com.carsaver.partner.web.api.desking;

import com.carsaver.partner.model.desking.DealNotesRequest;
import com.carsaver.partner.model.desking.DealNotesResponse;
import com.carsaver.partner.service.desking.DealNotesService;
import com.carsaver.partner.web.advice.RestGlobalExceptionHandler;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
class DealNotesApiControllerTest {
    private static final String NOTE = "This is test note";
    private static final boolean CUSTOMER_ACCESS = true;
    private static final long CLONE_DEAL_ID = 1009L;
    private static final String ID = UUID.randomUUID().toString();

    MockMvc mockMvc;
    ObjectMapper objectMapper;
    DealNotesService dealNotesService = mock(DealNotesService.class);
    RestGlobalExceptionHandler exceptionHandler = mock(RestGlobalExceptionHandler.class);

    @InjectMocks
    DealNotesApiController dealNotesApiController;


    @BeforeEach
    void setup() {
        this.mockMvc = MockMvcBuilders.standaloneSetup(dealNotesApiController).setControllerAdvice(exceptionHandler).build();
        objectMapper = new ObjectMapper();
    }


    void retrieveAllDealNotesByCloneDealId() throws Exception {
        List<DealNotesResponse> dealNotesResponseList = buildDealNotesResponseList();
        when(dealNotesService.retrieveAllDealNotesByCloneDealId(anyLong())).thenReturn(dealNotesResponseList);
        mockMvc.perform(get("/deal-notes/{cloneDealId}", 1))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(content().json(objectMapper.writeValueAsString(dealNotesResponseList)));
    }


    void retrieveAllDealNotesByCloneDealId_EmptyResult() throws Exception {
        List<DealNotesResponse> dealNotesResponseList = new ArrayList<>();
        when(dealNotesService.retrieveAllDealNotesByCloneDealId(anyLong())).thenReturn(dealNotesResponseList);
        mockMvc.perform(get("/deal-notes/{cloneDealId}", 1))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(content().json(objectMapper.writeValueAsString(dealNotesResponseList)));
    }


    void saveDealNoteTest() throws Exception {
        DealNotesResponse dealNotesResponse = buildDealNotesResponse();
        DealNotesRequest dealNotesRequest = buildDealNotesRequest();
        when(dealNotesService.saveDealNote(any(DealNotesRequest.class))).thenReturn(dealNotesResponse);
        mockMvc.perform(post("/deal-notes")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(objectMapper.writeValueAsString(dealNotesRequest)))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(content().json(objectMapper.writeValueAsString(dealNotesResponse)));
    }


    void saveDealNoteTest_NullNote_Test() throws Exception {
        DealNotesResponse dealNotesResponse = buildDealNotesResponse();
        DealNotesRequest dealNotesRequest = new DealNotesRequest();
        dealNotesRequest.setCloneDealId(10001L);
        dealNotesRequest.setNote(null);
        dealNotesRequest.setCustomerAccess(true);

        when(dealNotesService.saveDealNote(any(DealNotesRequest.class))).thenReturn(dealNotesResponse);
        mockMvc.perform(post("/deal-notes")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(objectMapper.writeValueAsString(dealNotesRequest)))
            .andExpect(status().isBadRequest());
    }


    void saveDealNote_Null_CloneDealIdTest() throws Exception {
        DealNotesResponse dealNotesResponse = buildDealNotesResponse();
        DealNotesRequest dealNotesRequest = buildDealNotesRequest();
        dealNotesRequest.setCloneDealId(null);
        when(dealNotesService.saveDealNote(any())).thenReturn(dealNotesResponse);
        mockMvc.perform(post("/deal-notes")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(objectMapper.writeValueAsString(dealNotesRequest)))
            .andExpect(status().isBadRequest());
    }



    private DealNotesRequest buildDealNotesRequest() {
        DealNotesRequest dealNotesRequest = new DealNotesRequest();
        dealNotesRequest.setNote(NOTE);
        dealNotesRequest.setCloneDealId(CLONE_DEAL_ID);
        dealNotesRequest.setCustomerAccess(CUSTOMER_ACCESS);
        return dealNotesRequest;
    }

    DealNotesResponse buildDealNotesResponse() {
        DealNotesResponse dealNotesResponse = new DealNotesResponse();
        dealNotesResponse.setId(ID);
        dealNotesResponse.setCloneDealId(CLONE_DEAL_ID);
        dealNotesResponse.setNote(NOTE);
        dealNotesResponse.setCustomerAccess(CUSTOMER_ACCESS);
        return dealNotesResponse;
    }

    List<DealNotesResponse> buildDealNotesResponseList() {
        List<DealNotesResponse> result = IntStream.of(0, 4).mapToObj(i -> {
            DealNotesResponse dealNotesResponse = new DealNotesResponse();
            dealNotesResponse.setNote("This is note " + i);
            dealNotesResponse.setId(ID);
            dealNotesResponse.setCloneDealId(1001L);
            dealNotesResponse.setCustomerAccess(CUSTOMER_ACCESS);
            return dealNotesResponse;
        }).collect(Collectors.toList());
        return result;
    }
}
