package com.carsaver.partner.model;

import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.preferences.DealerPreferences;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(MockitoExtension.class)
class DealerInfoTest {

    private final String ROUTE_ONE = "RouteOne";
    private final String DEALER_TRACK = "DealerTrack";

    private DealerView dealerView;

    @BeforeEach
    void setUp() {
        dealerView = new DealerView();
        dealerView.setEnrolledInWarrantyProgram(true);
        dealerView.setSpanishEnabled(true);
        dealerView.setDeliveryEnabled(true);
    }

    @Test
    void fromGetPreferences_ResultNotNull_NoPreferencesInput() {
        DealerInfo result = DealerInfo.from(dealerView);

        assertNotNull(result);
    }

    @Test
    void fromGetPreferences_PreferencesNull_NoPreferencesInput() {
        DealerInfo result = DealerInfo.from(dealerView);

        assertNull(result.getPreferences());
    }

    @Test
    void fromGetPreferences_PreferencesMatch_PreferencesInput() {
        DealerPreferences preferences = new DealerPreferences();
        preferences.setLmsPreference(ROUTE_ONE);
        dealerView.setPreferences(preferences);
        DealerInfo result = DealerInfo.from(dealerView);

        assertTrue(result.getPreferences().equals(preferences));
    }

}
