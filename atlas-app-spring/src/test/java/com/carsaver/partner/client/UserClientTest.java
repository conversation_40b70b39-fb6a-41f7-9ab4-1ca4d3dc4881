package com.carsaver.partner.client;

import com.carsaver.magellan.auth.CarSaverAuthService;
import com.carsaver.magellan.auth.TokenResponse;
import com.carsaver.partner.model.user.UserView;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xebialabs.restito.semantics.Action;
import com.xebialabs.restito.server.StubServer;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.glassfish.grizzly.http.util.HttpStatus;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration;
import org.springframework.boot.autoconfigure.web.reactive.HttpHandlerAutoConfiguration;
import org.springframework.boot.autoconfigure.web.reactive.function.client.ClientHttpConnectorAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cloud.client.CommonsClientAutoConfiguration;
import org.springframework.cloud.commons.httpclient.HttpClientConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.cloud.openfeign.FeignAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Profile;
import org.springframework.hateoas.CollectionModel;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import static com.xebialabs.restito.builder.stub.StubHttp.whenHttp;
import static com.xebialabs.restito.semantics.Action.contentType;
import static com.xebialabs.restito.semantics.Action.status;
import static com.xebialabs.restito.semantics.Action.stringContent;
import static io.netty.util.internal.SystemPropertyUtil.get;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@SpringBootTest
@ActiveProfiles("client-test")
@TestPropertySource(properties =
    "dealer-service.api-uri=http://localhost:52421")
@RequiredArgsConstructor
class UserClientTest {

    private static final Integer CERTIFICATE_ID = 12345;
    private static final String ID = "12345";
    private static final String FIRST_NAME = "Test";

    private final StubServer server = new StubServer(52421).run();

    @MockBean
    private CarSaverAuthService carSaverAuthService;

    private UserClient userClient = mock(UserClient.class);

    @Configuration
    @EnableFeignClients(clients = UserClient.class)
    @Import({FeignAutoConfiguration.class,
        ClientHttpConnectorAutoConfiguration.class,
        HttpClientConfiguration.class,
        HttpMessageConvertersAutoConfiguration.class,
        HttpHandlerAutoConfiguration.class,
        CommonsClientAutoConfiguration.class})

    @Profile("client-test")
    public static class UserClientTestConfiguration {
        @Bean
        public ObjectMapper mapper() {
            return new ObjectMapper();
        }
    }

    @Test
    public void test() {
        TokenResponse tokenResponse = new TokenResponse();
        tokenResponse.setAccessToken("access_token");
        Mockito.when(carSaverAuthService.getToken()).thenReturn(tokenResponse);

        UserView user =
            UserView.builder()
                .id(ID)
                .firstName(FIRST_NAME)
                .build();

        whenHttp(server)
            .match(get("/users/certificateId"))
            .then(status(HttpStatus.OK_200), jsonContent(CollectionModel.of(user)), contentType("application/json"));

        when(userClient.searchUserByCertificateId(CERTIFICATE_ID)).thenReturn(user);

        UserView result = userClient.searchUserByCertificateId(CERTIFICATE_ID);
        assertEquals(user.getFirstName(), result.getFirstName());
    }

    private final ObjectMapper objectMapper = new ObjectMapper();

    @SneakyThrows
    private Action jsonContent(Object object) {
        return stringContent(objectMapper.writeValueAsString(object));
    }
}
