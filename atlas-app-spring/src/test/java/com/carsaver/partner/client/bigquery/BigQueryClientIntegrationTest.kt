package com.carsaver.partner.client.bigquery

import com.fasterxml.jackson.databind.ObjectMapper
import lombok.SneakyThrows
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import software.amazon.awssdk.services.secretsmanager.SecretsManagerClient

@Tag("integration")
internal class BigQueryClientIntegrationTest {
    @Test
    @SneakyThrows
    fun test() {
        val bigQueryClient = BigQueryClient(
            "services/atlas-app/beta", "ga4-eecu.analytics_430636697.events_*",
            SecretsManagerClient.create(), ObjectMapper()
        )

        val data: List<Map<String, Any?>> =
            bigQueryClient.getVehicleSearchesData("3259a563-0277-4d17-b0eb-d5c83cc4b0c6", "500f87d74af4b50002000027")
        println(data)
    }

    @Test
    @SneakyThrows
    fun test2() {
        val bigQueryClient = BigQueryClient("services/atlas-app/beta", "ga4-eecu.analytics_430636697.events_*",
            SecretsManagerClient.create(), ObjectMapper())

        val data = bigQueryClient.getVehicleSearchCount(mutableListOf("500f87d74af4b50002000027"), "3259a563-0277-4d17-b0eb-d5c83cc4b0c6")
        println(data)
    }
}
