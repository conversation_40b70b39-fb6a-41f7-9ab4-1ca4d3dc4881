package com.carsaver.partner.client;

import com.carsaver.partner.model.UserProgram;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.List;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles("local")
@TestPropertySource(locations = {
    "classpath:/application-local.yml"
}, properties = {
    "spring.cloud.config.enabled=true"
})
@Tag("integration")
class UserProgramsClientIntegrationTest {

    @Autowired
    UserProgramsClient client;

    @Test
    public void test() throws JsonProcessingException {
        List<UserProgram> byId = client.findProgramsByUserId("af1fa7fa-6640-4284-9ff3-58d85d9a6890");
        System.out.println(new ObjectMapper().writeValueAsString(byId));
    }
}
