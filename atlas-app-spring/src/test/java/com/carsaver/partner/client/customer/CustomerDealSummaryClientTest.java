package com.carsaver.partner.client.customer;

import com.carsaver.magellan.auth.CarSaverAuthService;
import com.carsaver.magellan.auth.TokenResponse;
import com.carsaver.partner.model.retail.DealSummaryResponse;
import kong.unirest.GetRequest;
import kong.unirest.HttpResponse;
import kong.unirest.Unirest;
import kong.unirest.UnirestException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class CustomerDealSummaryClientTest {

    @Mock
    private CarSaverAuthService carSaverAuthService;

    @Mock
    private GetRequest mockRequest;

    @Mock
    private HttpResponse<DealSummaryResponse> mockResponse;

    @InjectMocks
    private CustomerDealSummaryClient customerDealSummaryClient;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(customerDealSummaryClient, "userVehicleServiceApiUri", "https://api.example.com");
    }

    @Test
    @Disabled
    void testFetchDealSummary_Success() {
        String customerId = "12345";
        TokenResponse tokenResponse = mock(TokenResponse.class);
        when(tokenResponse.getAccessToken()).thenReturn("valid_token");
        DealSummaryResponse expectedResponse = new DealSummaryResponse();

        when(carSaverAuthService.getToken()).thenReturn(tokenResponse);

        try (MockedStatic<Unirest> unirestMock = mockStatic(Unirest.class)) {
            unirestMock.when(() -> Unirest.get(anyString())).thenReturn(mockRequest);

            when(mockRequest.header(anyString(), anyString())).thenReturn(mockRequest);
            when(mockRequest.asObject(DealSummaryResponse.class)).thenReturn(mockResponse);
            when(mockResponse.isSuccess()).thenReturn(true);
            when(mockResponse.getBody()).thenReturn(expectedResponse);

            when(mockResponse.ifFailure(any(), any())).thenReturn(mockResponse);

            DealSummaryResponse actualResponse = customerDealSummaryClient.fetchDealSummary(customerId);

            assertEquals(expectedResponse, actualResponse);
            unirestMock.verify(() -> Unirest.get(anyString()));
            assertEquals(actualResponse,expectedResponse);
        }
    }

    @Test
    @Disabled
    void testFetchDealSummary_Failure() {
        String customerId = "12345";
        TokenResponse tokenResponse = mock(TokenResponse.class);
        when(tokenResponse.getAccessToken()).thenReturn("valid_token");

        when(carSaverAuthService.getToken()).thenReturn(tokenResponse);

        try (MockedStatic<Unirest> unirestMock = mockStatic(Unirest.class)) {
            unirestMock.when(() -> Unirest.get(anyString())).thenReturn(mockRequest);

            when(mockRequest.header(anyString(), anyString())).thenReturn(mockRequest);
            when(mockRequest.asObject(DealSummaryResponse.class)).thenReturn(mockResponse);
            when(mockResponse.getStatus()).thenReturn(500);

            when(mockResponse.ifFailure(any(), any())).thenReturn(mockResponse);

            assertThrows(UnirestException.class,() -> customerDealSummaryClient.fetchDealSummary(customerId));


        }
    }


}
