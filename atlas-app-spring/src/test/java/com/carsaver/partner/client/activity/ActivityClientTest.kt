package com.carsaver.partner.client.activity

import com.carsaver.partner.AtlasApplication
import com.carsaver.partner.ClientTestSupport
import com.carsaver.partner.TestUtils
import com.carsaver.partner.http.HttpService
import lombok.SneakyThrows
import org.junit.jupiter.api.Test

internal class getDigitalRetailLogsByUserIdAndDealerIds {
    @Test
    @SneakyThrows
    fun test() {
        ClientTestSupport().use { support ->
            val activityLogs = listOf(
                removeObject(
                    TestUtils.truncate(
                        support.generate(
                            ActivityLog::class.java
                        )
                    )
                )
            )
            support.path(
                "/events/digital-retail-events?userId=userId&dealerIds=dealerId&dealerIds=dealerId2",
                activityLogs
            )

            val client = ActivityClient(
                support.baseUrl(), HttpService(AtlasApplication.client()),
                support.mockAuthClient()
            )

            val logs = client.getDigitalRetailLogsByUserIdAndDealerIds(
                "userId",
                listOf("dealerId", "dealerId2")
            )
            support.assertReflectionMatches(logs)
        }
    }

    @Test
    fun testCount() {
        ClientTestSupport().use { support ->
            support.pathGenerated(
                "/events/digital-retail-events/count?dealerIds=dealerId&dealerIds=dealerId2&userId=userId",
                ActivityAggregation::class.java
            )

            val client = ActivityClient(
                support.baseUrl(), HttpService(AtlasApplication.client()),
                support.mockAuthClient()
            )

            val logs = client.getDigitalRetailLogsByUserIdAndDealerIdsCount(
                listOf("dealerId", "dealerId2"),
                "userId"
            )
            support.assertReflectionMatches(logs)
        }
    }

    private fun removeObject(generate: ActivityLog): ActivityLog {
        val map: MutableMap<String, Any> = HashMap()
        map["key"] = "value"
        generate.metadata = map
        return generate
    }
}
