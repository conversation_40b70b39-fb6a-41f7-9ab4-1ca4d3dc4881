package com.carsaver.partner.client.dealer

import com.carsaver.partner.AtlasApplication
import com.carsaver.partner.ClientTestSupport
import com.carsaver.partner.http.HttpService
import com.carsaver.partner.model.user.CreditProfile
import com.carsaver.partner.model.user.UserView
import org.junit.jupiter.api.Test

internal class DealerClientTest {
    @Test
    fun certificateById() {
        ClientTestSupport().use { support ->
            support.pathGeneratedTruncated(
                "/certificates/123",
                CertificateV2::class.java
            )
            val client =
                DealerClient(
                    support.baseUrl(), HttpService(AtlasApplication.client()),
                    support.mockAuthClient()
                )

            val certificateById = client.getCertificateById(123L)
            support.assertReflectionMatches(certificateById)
        }
    }

    @Test
    fun userById() {
        ClientTestSupport().use { support ->
            support.pathGeneratedTruncated(
                "/users/userId",
                UserView::class.java
            )
            removeProblematicFields(support.generated())

            val client =
                DealerClient(
                    support.baseUrl(), HttpService(AtlasApplication.client()),
                    support.mockAuthClient()
                )

            val certificateById = client.getUserById("userId")
            support.assertReflectionMatches(certificateById)
        }
    }

    @Test
    fun creditProfile() {
        ClientTestSupport().use { support ->
            support.pathGeneratedTruncated(
                "/users/userId/creditProfile",
                CreditProfile::class.java
            )
            val client =
                DealerClient(
                    support.baseUrl(), HttpService(AtlasApplication.client()),
                    support.mockAuthClient()
                )

            val certificateById = client.getCreditProfile("userId")
            support.assertReflectionMatches(certificateById)
        }
    }

    private fun removeProblematicFields(generated: UserView) {
        generated.metadata = null
        generated.phoneNumberInfo = null
    }
}
