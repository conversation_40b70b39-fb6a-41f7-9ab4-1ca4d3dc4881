package com.carsaver.partner.client;

import com.carsaver.partner.client.nissan.NissanWebClient;
import com.carsaver.partner.model.document_upload.DocumentDetail;
import com.carsaver.partner.model.document_upload.UserDocuments;
import com.carsaver.partner.service.UserDocumentMockUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class UserDocumentsClientTest {

    @InjectMocks
    UserDocumentsClient userDocumentsClient;

    @Mock
    NissanWebClient client;

    @Test
    void getUserDocumentsList() {
        String dealerId = UUID.randomUUID().toString();
        String userId = UUID.randomUUID().toString();
        List<UserDocuments.Documents> documents = UserDocumentMockUtil.userDocuments();
        ReflectionTestUtils.setField(userDocumentsClient, "client", client);
        when(userDocumentsClient.getUserDocuments(dealerId, userId)).thenReturn(documents);
        List<UserDocuments.Documents> userDocuments = userDocumentsClient.getUserDocuments(dealerId, userId);
        assertEquals(userDocuments, documents);
    }
    @Test
    void loadS3FileByFileId() {
        String fileId = UUID.randomUUID().toString();
        DocumentDetail documentDetail = DocumentDetail.builder()
            .fileName("abc")
            .bytes("This is file.".getBytes())
            .mediaType("application/json")
            .size(10).build();
        ReflectionTestUtils.setField(userDocumentsClient, "client", client);
        when(client.getFile(anyString(), any(),anyString())).thenReturn(documentDetail);
        DocumentDetail documentDetailResponse = userDocumentsClient.loadS3FileByFileId(fileId);
        assertEquals(documentDetailResponse, documentDetail);
    }
}
