package com.carsaver.partner.client.leads.v2

import com.carsaver.partner.AtlasApplication
import com.carsaver.partner.ClientTestSupport
import com.carsaver.partner.http.HttpService
import org.junit.jupiter.api.Test

internal class LeadClientV2Test {
    @Test
    fun leadById() {
        ClientTestSupport().use { support ->
            support.pathGeneratedTruncated(
                "/leads/leadId",
                LeadV2::class.java
            )
            val client = LeadClientV2(
                support.baseUrl(), HttpService(AtlasApplication.client()),
                support.mockAuthClient()
            )

            val leadId = client.getLeadById("leadId")
            support.assertReflectionMatches(leadId)
        }
    }
}
