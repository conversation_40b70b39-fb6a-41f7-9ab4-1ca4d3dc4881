package com.carsaver.partner.client.inventory.v2

import com.carsaver.partner.AtlasApplication
import com.carsaver.partner.ClientTestSupport
import com.carsaver.partner.http.HttpService
import org.junit.jupiter.api.Test

internal class InventoryClientV2Test {

    @Test
    fun inventoryVehicleById() {
        ClientTestSupport().use { support ->
            support.pathGeneratedTruncated(
                "/newOrUsed/v2/inventoryId",
                InventoryVehicleV2::class.java
            )
            val client = InventoryClientV2(
                support.baseUrl(), HttpService(AtlasApplication.client()),
                support.mockAuthClient()
            )

            val actual = client.getInventoryVehicleById("inventoryId")
            support.assertReflectionMatches(actual)
        }
    }
}
