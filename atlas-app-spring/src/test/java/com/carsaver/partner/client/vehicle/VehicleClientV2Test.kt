package com.carsaver.partner.client.vehicle

import com.carsaver.partner.AtlasApplication
import com.carsaver.partner.ClientTestSupport
import com.carsaver.partner.http.HttpService
import org.junit.jupiter.api.Test

internal class VehicleClientV2Test {

    @Test
    fun styleByStyleId() {
            ClientTestSupport().use { support ->
                support.pathGeneratedTruncated(
                    "/style-summaries/100",
                    StyleSummaryV2::class.java
                )
                val client = VehicleClientV2(
                    support.baseUrl(), HttpService(AtlasApplication.client()),
                    support.mockAuthClient()
                )

                val styleByStyleId = client.getStyleByStyleId(100)
                support.assertReflectionMatches(styleByStyleId)
            }
        }
}
