package com.carsaver.partner.client.activity

import com.carsaver.partner.AtlasApplication
import com.carsaver.partner.TestUtils
import com.carsaver.partner.client.oauth.OAuthClient
import com.carsaver.partner.http.HttpService
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test

internal class ActivityClientIntegrationTest {
    @Test
    @Tag("integration")
    fun test() {
        val httpService = HttpService(AtlasApplication.client())
        val properties = TestUtils.getBetaOauthProperties()

        val client =
            ActivityClient("https://api-beta.carsaver.com/activity", httpService, OAuthClient(httpService, properties))

        val digitalRetailLogsByUserIdAndDealerIds = client.getDigitalRetailLogsByUserIdAndDealerIds(
            "d35de609-7623-492f-9193-2b52f5e466a7",
            listOf("55c521b6-fc5e-47e3-bb10-ba14fe545308", "aa0f1f3d-ec97-4bbe-9811-38365891043a")
        )

        println(digitalRetailLogsByUserIdAndDealerIds)
    }
}
