package com.carsaver.partner.client.bigquery

import com.google.cloud.bigquery.*
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.mockito.ArgumentMatchers
import org.mockito.Mockito
import org.mockito.kotlin.whenever
import org.unitils.reflectionassert.ReflectionAssert

internal class BigQueryClientTest {
    private val client: BigQuery = Mockito.mock(BigQuery::class.java)
    private val url =
        "https://nissanathome.carsaver.com/buy/search-results-page?timeStamp=1732636776268&dealerId=b9c1c697-d17e-4cdc-a82c-a84d1f4d7157&utm_source=dealer&utm_medium=crm_email_smartlink&utm_campaign=path_to_1&utm_content=personalized_payments_day1&stockTypes=NEW&makes=Nissan&sort=price,asc&years=2023,2024"

    @Test
    @Throws(InterruptedException::class)
    fun test() {
        val bigQueryClient = BigQueryClient(client, "ga4-eecu.analytics_430636697.events_*")
        val tableResult = Mockito.mock(TableResult::class.java)

        whenever(tableResult.iterateAll()).thenReturn(
            listOf(
                FieldValueList.of(
                    listOf(
                        FieldValue.of(FieldValue.Attribute.PRIMITIVE, url)
                    ), FieldList.of(Field.of("page_location", StandardSQLTypeName.STRING))
                )
            )
        )
        whenever(tableResult.schema).thenReturn(Schema.of(Field.of("page_location", StandardSQLTypeName.STRING)))
        whenever(client.query(ArgumentMatchers.any())).thenReturn(tableResult)
        val data: List<Map<String, Any?>> = bigQueryClient.getVehicleSearchesData("userId", "dealerId")
        Assertions.assertEquals(1, data.size)

        val params: MutableMap<String, List<String>> = HashMap()
        params["timeStamp"] = listOf("1732636776268")
        params["utm_campaign"] = listOf("path_to_1")
        params["dealerId"] = listOf("b9c1c697-d17e-4cdc-a82c-a84d1f4d7157")
        params["utm_medium"] = listOf("crm_email_smartlink")
        params["makes"] = listOf("Nissan")
        params["stockTypes"] = listOf("NEW")
        params["sort"] = listOf("price", "asc")
        params["utm_content"] = listOf("personalized_payments_day1")
        params["utm_source"] = listOf("dealer")
        params["years"] = listOf("2023", "2024")
        val expected: MutableMap<String, Any> = HashMap()
        expected["page_location"] = url
        expected["params"] = params
        expected["uri_base"] = "https://nissanathome.carsaver.com/buy/search-results-page"

        ReflectionAssert.assertReflectionEquals(expected, data[0])
    }
}
