package com.carsaver.partner.client;

import com.carsaver.partner.client.inventory.VehicleForProgramsClient;
import com.carsaver.partner.model.ProgramSubscription;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;

import java.util.Collection;


@SpringBootTest
@ActiveProfiles("integration")
@Tag("integration")
class ProgramSubscriptionsClientIntegrationTest {

    @Autowired
    ProgramSubscriptionsClient client;

    @MockBean
    VehicleForProgramsClient vehicleForProgramsClient;

    @Test
    void test() throws JsonProcessingException {
        Collection<ProgramSubscription> byId = client.findByProgramId("2efa7b54-5b8f-4b98-9f03-69b720d02601").getContent();
        System.out.println(new ObjectMapper().writeValueAsString(byId));
    }
}
