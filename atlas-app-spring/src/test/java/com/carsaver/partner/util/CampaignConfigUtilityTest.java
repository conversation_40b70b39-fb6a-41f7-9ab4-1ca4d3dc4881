package com.carsaver.partner.util;

import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.model.Source;
import com.carsaver.magellan.model.UserView;
import com.carsaver.magellan.model.campaign.CampaignView;
import com.carsaver.magellan.model.campaign.UpgradeStrategy;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CampaignConfigUtilityTest {

    @Mock
    UserClient userClient;
    @InjectMocks
    CampaignConfigUtility campaignConfigUtility;

    @Test
    void testLeasePayoffConfig() {

        UserView userView = spy(createUser());
        CampaignView campaignView = spy(createCampaign());

        when(userClient.findById("userId")).thenReturn(userView);
        when(userView.getCampaign()).thenReturn(campaignView);

        assertTrue(campaignConfigUtility.isLeasePayoffEnabled("userId"));

        campaignView.getUpgradeStrategy().setLeasePayoffCalculationEnabled(false);
        assertFalse(campaignConfigUtility.isLeasePayoffEnabled("userId"));

        campaignView.getUpgradeStrategy().setLeasePayoffCalculationEnabled(null);
        assertFalse(campaignConfigUtility.isLeasePayoffEnabled("userId"));

        when(userClient.findById("userId")).thenReturn(null);
        assertFalse(campaignConfigUtility.isLeasePayoffEnabled("userId"));

    }

    UserView createUser() {
        UserView userView = new UserView();
        userView.setSource(new Source());
        return userView;
    }

    CampaignView createCampaign() {
        CampaignView campaignView = new CampaignView();
        UpgradeStrategy upgradeStrategy = new UpgradeStrategy();
        upgradeStrategy.setLeasePayoffCalculationEnabled(true);
        campaignView.setUpgradeStrategy(upgradeStrategy);
        return campaignView;
    }
}
