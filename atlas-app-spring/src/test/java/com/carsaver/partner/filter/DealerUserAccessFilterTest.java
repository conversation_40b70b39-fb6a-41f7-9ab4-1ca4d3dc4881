package com.carsaver.partner.filter;

import com.carsaver.core.DealerStatus;
import com.carsaver.magellan.client.ProgramClient;
import com.carsaver.magellan.client.ProgramSubscriptionClient;
import com.carsaver.magellan.model.foundation.ProgramSubscriptionView;
import com.carsaver.magellan.model.foundation.ProgramView;
import com.carsaver.magellan.model.foundation.ProductView;
import com.carsaver.partner.client.FeatureSubscriptionsClient;
import com.carsaver.partner.model.FeatureSubscriptionResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.hateoas.CollectionModel;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

class DealerUserAccessFilterTest {

    private DealerUserAccessFilter dealerUserAccessFilter;
    private ProgramSubscriptionClient programSubscriptionClient;
    private FeatureSubscriptionsClient featureSubscriptionsClient;
    private ProgramClient programClient;

    private static final String DEALER_ID = "123";
    private static final String NISSAN_PROGRAM_ID = "nissan-bah";
    private static final String BOOST_FEATURE_ID = "nissan-bah";
    private static final String BOOST_PROGRAM_ID = "boost-prog";
    private static final String OTHER_PROGRAM_ID = "other-prog";

    @BeforeEach
    void setUp() {
        dealerUserAccessFilter = new DealerUserAccessFilter();
        programSubscriptionClient = Mockito.mock(ProgramSubscriptionClient.class);
        programClient = Mockito.mock(ProgramClient.class);
        featureSubscriptionsClient = Mockito.mock(FeatureSubscriptionsClient.class);

        // Use ReflectionTestUtils to inject mocks and values
        ReflectionTestUtils.setField(dealerUserAccessFilter, "programSubscriptionClient", programSubscriptionClient);
        ReflectionTestUtils.setField(dealerUserAccessFilter, "programClient", programClient);
        ReflectionTestUtils.setField(dealerUserAccessFilter, "nissanBuyAtHomeProgramId", NISSAN_PROGRAM_ID);
        ReflectionTestUtils.setField(dealerUserAccessFilter, "boostFeaturesFeatureId", BOOST_FEATURE_ID);
        ReflectionTestUtils.setField(dealerUserAccessFilter, "featureSubscriptionsClient", featureSubscriptionsClient);
    }

    @Test
    void whenDealerIsEnrolledInBoost_thenReturnTrue() {
        // Arrange
        ProgramSubscriptionView subscription = new ProgramSubscriptionView();
        subscription.setStatus(DealerStatus.LIVE);
        subscription.setProgramId(BOOST_PROGRAM_ID);
        CollectionModel<ProgramSubscriptionView> subscriptions = CollectionModel.of(List.of(subscription));

        ProgramView program = new ProgramView();
        ProductView product = new ProductView();
        product.setId(DealerUserAccessFilter.ECOMMERCE_PRODUCT_ID);
        program.setProduct(product);

        when(programSubscriptionClient.findByDealer(DEALER_ID)).thenReturn(subscriptions);
        when(programClient.findById(BOOST_PROGRAM_ID)).thenReturn(Optional.of(program));

        // Act
        boolean result = dealerUserAccessFilter.isDealerEnrolledInBoost(DEALER_ID);

        // Assert
        assertTrue(result);
    }

    @Test
    void whenSubscriptionIsInactive_thenReturnFalse() {
        // Arrange
        ProgramSubscriptionView subscription = new ProgramSubscriptionView();
        subscription.setStatus(DealerStatus.CANCELLED);
        subscription.setProgramId(BOOST_PROGRAM_ID);
        CollectionModel<ProgramSubscriptionView> subscriptions = CollectionModel.of(List.of(subscription));

        when(programSubscriptionClient.findByDealer(DEALER_ID)).thenReturn(subscriptions);

        // Act
        boolean result = dealerUserAccessFilter.isDealerEnrolledInBoost(DEALER_ID);

        // Assert
        assertFalse(result);
    }

    @Test
    void whenSubscriptionIsNissanBuyAtHome_thenReturnFalse() {
        // Arrange
        ProgramSubscriptionView subscription = new ProgramSubscriptionView();
        subscription.setStatus(DealerStatus.LIVE);
        subscription.setProgramId(NISSAN_PROGRAM_ID);
        CollectionModel<ProgramSubscriptionView> subscriptions = CollectionModel.of(List.of(subscription));

        when(programSubscriptionClient.findByDealer(DEALER_ID)).thenReturn(subscriptions);

        // Act
        boolean result = dealerUserAccessFilter.isDealerEnrolledInBoost(DEALER_ID);

        // Assert
        assertFalse(result);
    }

    @Test
    void whenSubscriptionIsNissanBuyAtHome_andHasFeatureSubscriptionActive_thenReturnTrue() {
        // Arrange
        ProgramSubscriptionView subscription = new ProgramSubscriptionView();
        subscription.setStatus(DealerStatus.LIVE);
        subscription.setProgramId(NISSAN_PROGRAM_ID);
        CollectionModel<ProgramSubscriptionView> subscriptions = CollectionModel.of(List.of(subscription));

        when(programSubscriptionClient.findByDealer(DEALER_ID)).thenReturn(subscriptions);
        when(featureSubscriptionsClient.getFeatureSubscription(any())).thenReturn(FeatureSubscriptionResponse.builder().active(true).build());
        // Act
        boolean result = dealerUserAccessFilter.isDealerEnrolledInBoost(DEALER_ID);

        // Assert
        assertTrue(result);
    }

    @Test
    void whenSubscriptionIsNissanBuyAtHome_andHasFeatureSubscriptionInactive_thenReturnFalse() {
        // Arrange
        ProgramSubscriptionView subscription = new ProgramSubscriptionView();
        subscription.setStatus(DealerStatus.LIVE);
        subscription.setProgramId(NISSAN_PROGRAM_ID);
        CollectionModel<ProgramSubscriptionView> subscriptions = CollectionModel.of(List.of(subscription));

        when(programSubscriptionClient.findByDealer(DEALER_ID)).thenReturn(subscriptions);
        when(featureSubscriptionsClient.getFeatureSubscription(any())).thenReturn(FeatureSubscriptionResponse.builder().active(false).build());
        // Act
        boolean result = dealerUserAccessFilter.isDealerEnrolledInBoost(DEALER_ID);

        // Assert
        assertFalse(result);
    }

    @Test
    void whenProductIsNotEcommerce_thenReturnFalse() {
        // Arrange
        ProgramSubscriptionView subscription = new ProgramSubscriptionView();
        subscription.setStatus(DealerStatus.LIVE);
        subscription.setProgramId(OTHER_PROGRAM_ID);
        CollectionModel<ProgramSubscriptionView> subscriptions = CollectionModel.of(List.of(subscription));

        ProgramView program = new ProgramView();
        ProductView product = new ProductView();
        product.setId(999); // Not an e-commerce product ID
        program.setProduct(product);

        when(programSubscriptionClient.findByDealer(DEALER_ID)).thenReturn(subscriptions);
        when(programClient.findById(OTHER_PROGRAM_ID)).thenReturn(Optional.of(program));

        // Act
        boolean result = dealerUserAccessFilter.isDealerEnrolledInBoost(DEALER_ID);

        // Assert
        assertFalse(result);
    }

    @Test
    void whenDealerHasNoSubscriptions_thenReturnFalse() {
        // Arrange
        when(programSubscriptionClient.findByDealer(DEALER_ID)).thenReturn(CollectionModel.of(Collections.emptyList()));

        // Act
        boolean result = dealerUserAccessFilter.isDealerEnrolledInBoost(DEALER_ID);

        // Assert
        assertFalse(result);
    }

    @Test
    void whenClientThrowsException_thenReturnFalse() {
        // Arrange
        when(programSubscriptionClient.findByDealer(DEALER_ID)).thenThrow(new RuntimeException("API error"));

        // Act
        boolean result = dealerUserAccessFilter.isDealerEnrolledInBoost(DEALER_ID);

        // Assert
        assertFalse(result);
    }

    @Test
    void whenProgramIsNotFound_thenReturnFalse() {
        // Arrange
        ProgramSubscriptionView subscription = new ProgramSubscriptionView();
        subscription.setStatus(DealerStatus.LIVE);
        subscription.setProgramId(BOOST_PROGRAM_ID);
        CollectionModel<ProgramSubscriptionView> subscriptions = CollectionModel.of(List.of(subscription));

        when(programSubscriptionClient.findByDealer(DEALER_ID)).thenReturn(subscriptions);
        when(programClient.findById(BOOST_PROGRAM_ID)).thenReturn(Optional.empty());

        // Act
        boolean result = dealerUserAccessFilter.isDealerEnrolledInBoost(DEALER_ID);

        // Assert
        assertFalse(result);
    }
}
