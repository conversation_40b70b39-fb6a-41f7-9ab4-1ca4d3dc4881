package com.carsaver.partner.configuration.graphics

import com.carsaver.partner.client.configuration.ConfigServiceClient
import com.carsaver.partner.configuration.ConfigurationClient
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.whenever
import org.mockito.kotlin.eq
import org.mockito.kotlin.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertSame
import org.junit.jupiter.api.assertThrows

@ExtendWith(MockitoExtension::class)
class GraphicsConfigServiceTest {

    @Mock
    private lateinit var configServiceClient: ConfigServiceClient

    @Mock
    private lateinit var configurationClient: ConfigurationClient

    @InjectMocks
    private lateinit var graphicsConfigService: GraphicsConfigService

    @Test
    fun `should return complete graphics configuration when client returns full data`() {
        // Given
        val dealerId = "dealer123"
        val domain: String? = null
        val clientResponse = GraphicsConfigResponse(
            logoName = "company-logo.png",
            logoUrl = "https://cdn.example.com/logo.png",
            favIconName = "favicon.ico",
            favIconUrl = "https://cdn.example.com/favicon.ico",
            dealershipImages = ImagesResponse(
                mobileName = "mobile-image.jpg",
                mobileUrl = "https://cdn.example.com/mobile.jpg",
                desktopName = "desktop-image.jpg",
                desktopUrl = "https://cdn.example.com/desktop.jpg"
            )
        )

        whenever(configServiceClient.getGraphicsConfigByDealerId(eq(dealerId), eq(domain)))
            .thenReturn(clientResponse)

        // When
        val result = graphicsConfigService.getGraphicsConfigByDealer(dealerId, domain)

        // Then
        assertNotNull(result)
        assertEquals("company-logo.png", result.logoName)
        assertEquals("https://cdn.example.com/logo.png", result.logoUrl)
        assertEquals("favicon.ico", result.favIconName)
        assertEquals("https://cdn.example.com/favicon.ico", result.favIconUrl)
        
        assertNotNull(result.dealershipImages)
        assertEquals("mobile-image.jpg", result.dealershipImages?.mobileName)
        assertEquals("https://cdn.example.com/mobile.jpg", result.dealershipImages?.mobileUrl)
        assertEquals("desktop-image.jpg", result.dealershipImages?.desktopName)
        assertEquals("https://cdn.example.com/desktop.jpg", result.dealershipImages?.desktopUrl)

        // Verify client interaction
        verify(configServiceClient).getGraphicsConfigByDealerId(eq(dealerId), eq(domain))
    }

    @Test
    fun `should return graphics configuration with null values when client returns null data`() {
        // Given
        val dealerId = "dealer456"
        val domain: String? = null
        val clientResponse = GraphicsConfigResponse(
            logoName = null,
            logoUrl = null,
            favIconName = null,
            favIconUrl = null,
            dealershipImages = null
        )

        whenever(configServiceClient.getGraphicsConfigByDealerId(eq(dealerId), eq(domain)))
            .thenReturn(clientResponse)

        // When
        val result = graphicsConfigService.getGraphicsConfigByDealer(dealerId, domain)

        // Then
        assertNotNull(result)
        assertNull(result.logoName)
        assertNull(result.logoUrl)
        assertNull(result.favIconName)
        assertNull(result.favIconUrl)
        assertNull(result.dealershipImages)

        // Verify client interaction
        verify(configServiceClient).getGraphicsConfigByDealerId(eq(dealerId), eq(domain))
    }

    @Test
    fun `should return graphics configuration with partial data when client returns partial data`() {
        // Given
        val dealerId = "dealer789"
        val domain: String? = null
        val clientResponse = GraphicsConfigResponse(
            logoName = "logo.png",
            logoUrl = "https://example.com/logo.png",
            favIconName = null,
            favIconUrl = null,
            dealershipImages = ImagesResponse(
                mobileName = "mobile.jpg",
                mobileUrl = "https://example.com/mobile.jpg",
                desktopName = null,
                desktopUrl = null
            )
        )

        whenever(configServiceClient.getGraphicsConfigByDealerId(eq(dealerId), eq(domain)))
            .thenReturn(clientResponse)

        // When
        val result = graphicsConfigService.getGraphicsConfigByDealer(dealerId, domain)

        // Then
        assertNotNull(result)
        assertEquals("logo.png", result.logoName)
        assertEquals("https://example.com/logo.png", result.logoUrl)
        assertNull(result.favIconName)
        assertNull(result.favIconUrl)
        
        assertNotNull(result.dealershipImages)
        assertEquals("mobile.jpg", result.dealershipImages?.mobileName)
        assertEquals("https://example.com/mobile.jpg", result.dealershipImages?.mobileUrl)
        assertNull(result.dealershipImages?.desktopName)
        assertNull(result.dealershipImages?.desktopUrl)

        // Verify client interaction
        verify(configServiceClient).getGraphicsConfigByDealerId(eq(dealerId), eq(domain))
    }

    @Test
    fun `should handle empty strings in graphics configuration`() {
        // Given
        val dealerId = "dealer999"
        val domain: String? = null
        val clientResponse = GraphicsConfigResponse(
            logoName = "",
            logoUrl = "",
            favIconName = "",
            favIconUrl = "",
            dealershipImages = ImagesResponse(
                mobileName = "",
                mobileUrl = "",
                desktopName = "",
                desktopUrl = ""
            )
        )

        whenever(configServiceClient.getGraphicsConfigByDealerId(eq(dealerId), eq(domain)))
            .thenReturn(clientResponse)

        // When
        val result = graphicsConfigService.getGraphicsConfigByDealer(dealerId, domain)

        // Then
        assertNotNull(result)
        assertEquals("", result.logoName)
        assertEquals("", result.logoUrl)
        assertEquals("", result.favIconName)
        assertEquals("", result.favIconUrl)
        
        assertNotNull(result.dealershipImages)
        assertEquals("", result.dealershipImages?.mobileName)
        assertEquals("", result.dealershipImages?.mobileUrl)
        assertEquals("", result.dealershipImages?.desktopName)
        assertEquals("", result.dealershipImages?.desktopUrl)

        // Verify client interaction
        verify(configServiceClient).getGraphicsConfigByDealerId(eq(dealerId), eq(domain))
    }

    @Test
    fun `should work with domain parameter when provided`() {
        // Given
        val dealerId = "dealer111"
        val domain = "example.com"
        val clientResponse = GraphicsConfigResponse(
            logoName = "domain-logo.png",
            logoUrl = "https://domain.example.com/logo.png",
            favIconName = "domain-favicon.ico",
            favIconUrl = "https://domain.example.com/favicon.ico",
            dealershipImages = ImagesResponse(
                mobileName = "domain-mobile.jpg",
                mobileUrl = "https://domain.example.com/mobile.jpg",
                desktopName = "domain-desktop.jpg",
                desktopUrl = "https://domain.example.com/desktop.jpg"
            )
        )

        whenever(configServiceClient.getGraphicsConfigByDealerId(eq(dealerId), eq(domain)))
            .thenReturn(clientResponse)

        // When
        val result = graphicsConfigService.getGraphicsConfigByDealer(dealerId, domain)

        // Then
        assertNotNull(result)
        assertEquals("domain-logo.png", result.logoName)
        assertEquals("https://domain.example.com/logo.png", result.logoUrl)
        assertEquals("domain-favicon.ico", result.favIconName)
        assertEquals("https://domain.example.com/favicon.ico", result.favIconUrl)
        
        assertNotNull(result.dealershipImages)
        assertEquals("domain-mobile.jpg", result.dealershipImages?.mobileName)
        assertEquals("https://domain.example.com/mobile.jpg", result.dealershipImages?.mobileUrl)
        assertEquals("domain-desktop.jpg", result.dealershipImages?.desktopName)
        assertEquals("https://domain.example.com/desktop.jpg", result.dealershipImages?.desktopUrl)

        // Verify client interaction with domain parameter
        verify(configServiceClient).getGraphicsConfigByDealerId(eq(dealerId), eq(domain))
    }

    @Test
    fun `should preserve original object reference when creating response`() {
        // Given
        val dealerId = "dealer222"
        val domain: String? = null
        val originalImages = ImagesResponse(
            mobileName = "test-mobile.jpg",
            mobileUrl = "https://test.com/mobile.jpg",
            desktopName = "test-desktop.jpg",
            desktopUrl = "https://test.com/desktop.jpg"
        )
        val clientResponse = GraphicsConfigResponse(
            logoName = "test-logo.png",
            logoUrl = "https://test.com/logo.png",
            favIconName = "test-favicon.ico",
            favIconUrl = "https://test.com/favicon.ico",
            dealershipImages = originalImages
        )

        whenever(configServiceClient.getGraphicsConfigByDealerId(eq(dealerId), eq(domain)))
            .thenReturn(clientResponse)

        // When
        val result = graphicsConfigService.getGraphicsConfigByDealer(dealerId, domain)

        // Then
        assertNotNull(result)
        assertSame(originalImages, result.dealershipImages)

        // Verify client interaction
        verify(configServiceClient).getGraphicsConfigByDealerId(eq(dealerId), eq(domain))
    }

    @Test
    fun `should propagate exceptions from config service client`() {
        // Given
        val dealerId = "dealer333"
        val domain: String? = null
        val expectedException = RuntimeException("Configuration service unavailable")

        whenever(configServiceClient.getGraphicsConfigByDealerId(eq(dealerId), eq(domain)))
            .thenThrow(expectedException)

        // When & Then
        val exception = assertThrows<RuntimeException> {
            graphicsConfigService.getGraphicsConfigByDealer(dealerId, domain)
        }

        assertEquals("Configuration service unavailable", exception.message)

        // Verify client interaction
        verify(configServiceClient).getGraphicsConfigByDealerId(eq(dealerId), eq(domain))
    }

    @Test
    fun `should handle dealer ID with special characters`() {
        // Given
        val dealerId = "dealer-with-special@characters_123"
        val domain: String? = null
        val clientResponse = GraphicsConfigResponse(
            logoName = "special-logo.png",
            logoUrl = "https://special.example.com/logo.png",
            favIconName = "special-favicon.ico",
            favIconUrl = "https://special.example.com/favicon.ico",
            dealershipImages = ImagesResponse(
                mobileName = "special-mobile.jpg",
                mobileUrl = "https://special.example.com/mobile.jpg",
                desktopName = "special-desktop.jpg",
                desktopUrl = "https://special.example.com/desktop.jpg"
            )
        )

        whenever(configServiceClient.getGraphicsConfigByDealerId(eq(dealerId), eq(domain)))
            .thenReturn(clientResponse)

        // When
        val result = graphicsConfigService.getGraphicsConfigByDealer(dealerId, domain)

        // Then
        assertNotNull(result)
        assertEquals("special-logo.png", result.logoName)
        assertEquals("https://special.example.com/logo.png", result.logoUrl)

        // Verify client interaction
        verify(configServiceClient).getGraphicsConfigByDealerId(eq(dealerId), eq(domain))
    }

    @Test
    fun `should handle very long dealer ID`() {
        // Given
        val dealerId = "a".repeat(1000) // Very long dealer ID
        val domain: String? = null
        val clientResponse = GraphicsConfigResponse(
            logoName = "long-dealer-logo.png",
            logoUrl = "https://long.example.com/logo.png",
            favIconName = null,
            favIconUrl = null,
            dealershipImages = null
        )

        whenever(configServiceClient.getGraphicsConfigByDealerId(eq(dealerId), eq(domain)))
            .thenReturn(clientResponse)

        // When
        val result = graphicsConfigService.getGraphicsConfigByDealer(dealerId, domain)

        // Then
        assertNotNull(result)
        assertEquals("long-dealer-logo.png", result.logoName)
        assertEquals("https://long.example.com/logo.png", result.logoUrl)

        // Verify client interaction
        verify(configServiceClient).getGraphicsConfigByDealerId(eq(dealerId), eq(domain))
    }

    @Test
    fun `should handle unicode characters in response data`() {
        // Given
        val dealerId = "dealer123"
        val domain: String? = null
        val clientResponse = GraphicsConfigResponse(
            logoName = "logo-ñáéíóú.png",
            logoUrl = "https://example.com/logo-ñáéíóú.png",
            favIconName = "favicon-中文.ico",
            favIconUrl = "https://example.com/favicon-中文.ico",
            dealershipImages = ImagesResponse(
                mobileName = "mobile-русский.jpg",
                mobileUrl = "https://example.com/mobile-русский.jpg",
                desktopName = "desktop-العربية.jpg",
                desktopUrl = "https://example.com/desktop-العربية.jpg"
            )
        )

        whenever(configServiceClient.getGraphicsConfigByDealerId(eq(dealerId), eq(domain)))
            .thenReturn(clientResponse)

        // When
        val result = graphicsConfigService.getGraphicsConfigByDealer(dealerId, domain)

        // Then
        assertNotNull(result)
        assertEquals("logo-ñáéíóú.png", result.logoName)
        assertEquals("https://example.com/logo-ñáéíóú.png", result.logoUrl)
        assertEquals("favicon-中文.ico", result.favIconName)
        assertEquals("https://example.com/favicon-中文.ico", result.favIconUrl)
        
        assertNotNull(result.dealershipImages)
        assertEquals("mobile-русский.jpg", result.dealershipImages?.mobileName)
        assertEquals("https://example.com/mobile-русский.jpg", result.dealershipImages?.mobileUrl)
        assertEquals("desktop-العربية.jpg", result.dealershipImages?.desktopName)
        assertEquals("https://example.com/desktop-العربية.jpg", result.dealershipImages?.desktopUrl)

        // Verify client interaction
        verify(configServiceClient).getGraphicsConfigByDealerId(eq(dealerId), eq(domain))
    }
}
