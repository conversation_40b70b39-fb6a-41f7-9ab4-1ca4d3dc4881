package com.carsaver.partner.configuration

import com.carsaver.partner.client.mapper.JsonMapper
import com.carsaver.partner.configuration.ctaconfig.mappers.CtaConfigPayloadMapper
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import java.io.File

class ConfigurationClientTest {

    val mapper: ObjectMapper = JsonMapper.getObjectMapper()

    @Test
    fun `serialize full object - ctaConfig`() {
        val file = File("src/test/resources/json/ctaConfigPayload.json")
        var ctaConfig = CtaConfigPayloadMapper.mapToConfig(file.readText())

        // full object
        val json = mapper.writeValueAsString(ctaConfig)
        assertEquals(
            "{\"vdp\":{\"formattingOptions\":{\"width\":\"300\",\"height\":\"50\",\"font\":\"Arial\",\"fontSize\":\"16\",\"fontWeight\":\"bold\",\"textAlign\":\"center\",\"padding\":\"10\",\"margin\":\"15\",\"borderRadius\":\"5\"},\"primaryButton\":{\"destination\":\"Vehicle Details Page\",\"color\":\"#FFFFFF\",\"hoverColor\":\"#F0F0F0\",\"hoverBackgroundColor\":\"#0056b3\",\"backgroundColor\":\"#007BFF\",\"name\":\"View Details Text\",\"active\":\"true\",\"renderType\":\"IMAGE\",\"imageName\":\"View Details Text\"},\"secondButton\":{\"destination\":\"Get Prequalified\",\"color\":\"#FFFFFF\",\"hoverColor\":\"#F0F0F0\",\"hoverBackgroundColor\":\"#218838\",\"backgroundColor\":\"#28A745\",\"name\":\"Get Prequalified Text\",\"active\":\"true\",\"renderType\":\"IMAGE\",\"imageName\":\"Get Prequalified Text\"},\"thirdButton\":{\"destination\":\"Get Trade Value\",\"color\":\"#FFFFFF\",\"hoverColor\":\"#212529\",\"hoverBackgroundColor\":\"#E0A800\",\"backgroundColor\":\"#FFC107\",\"name\":\"Get Trade Value Text\",\"active\":\"true\",\"renderType\":\"HTML\",\"imageName\":\"Get Trade Value Text\"},\"fourthButton\":{\"destination\":\"Sell@Home\",\"color\":\"#FFFFFF\",\"hoverColor\":\"#F0F0F0\",\"hoverBackgroundColor\":\"#5A6268\",\"backgroundColor\":\"#6C757D\",\"name\":\"Sell@Home Text\",\"active\":\"false\",\"renderType\":\"HTML\",\"imageName\":\"Sell@Home Text\"}},\"listings\":{\"formattingOptions\":{\"width\":\"100%\",\"height\":\"50\",\"font\":\"Arial\",\"fontSize\":\"16\",\"fontWeight\":\"bold\",\"textAlign\":\"center\",\"padding\":\"10\",\"margin\":\"15\",\"borderRadius\":\"5\"},\"primaryButton\":{\"destination\":\"Vehicle Details Page\",\"color\":\"#FFFFFF\",\"hoverColor\":\"#F0F0F0\",\"hoverBackgroundColor\":\"#0056b3\",\"backgroundColor\":\"#007BFF\",\"name\":\"View Details Text\",\"active\":\"true\",\"renderType\":\"HTML\",\"imageName\":\"View Details Text\"},\"secondButton\":{\"destination\":\"Get Prequalified\",\"color\":\"#FFFFFF\",\"hoverColor\":\"#F0F0F0\",\"hoverBackgroundColor\":\"#218838\",\"backgroundColor\":\"#28A745\",\"name\":\"Get Prequalified Text\",\"active\":\"true\",\"renderType\":\"IMAGE\",\"imageName\":\"Get Prequalified Text\"},\"thirdButton\":{\"destination\":\"Get Trade Value\",\"color\":\"#FFFFFF\",\"hoverColor\":\"#212529\",\"hoverBackgroundColor\":\"#E0A800\",\"backgroundColor\":\"#FFC107\",\"name\":\"Get Trade Value Text\",\"active\":\"true\",\"renderType\":\"HTML\",\"imageName\":\"Get Trade Value Text\"},\"fourthButton\":{\"destination\":\"Sell@Home\",\"color\":\"#FFFFFF\",\"hoverColor\":\"#F0F0F0\",\"hoverBackgroundColor\":\"#5A6268\",\"backgroundColor\":\"#6C757D\",\"name\":\"Sell@Home Text\",\"active\":\"false\",\"renderType\":\"HTML\",\"imageName\":\"Sell@Home Text\"}}}",
            json
        )
        //validate deserializing to value
        ctaConfig = mapper.readValue(json)
        assertEquals("Arial", ctaConfig.vdp!!.get().formattingOptions!!.get().font!!.get())
        assertEquals("IMAGE", ctaConfig.vdp!!.get().primaryButton!!.get().renderType!!.get())
        assertEquals("IMAGE", ctaConfig.vdp!!.get().secondButton!!.get().renderType!!.get())
        assertEquals("HTML", ctaConfig.vdp!!.get().thirdButton!!.get().renderType!!.get())
        assertEquals("HTML", ctaConfig.vdp!!.get().fourthButton!!.get().renderType!!.get())
        assertEquals("HTML", ctaConfig.listings!!.get().primaryButton!!.get().renderType!!.get())
        assertEquals("IMAGE", ctaConfig.listings!!.get().secondButton!!.get().renderType!!.get())
        assertEquals("HTML", ctaConfig.listings!!.get().thirdButton!!.get().renderType!!.get())
        assertEquals("HTML", ctaConfig.listings!!.get().fourthButton!!.get().renderType!!.get())
    }

    @Test
    fun `serialize object with one attribute with value - ctaConfig`() {
        // one attribute only
        var ctaConfig = CtaConfigPayloadMapper
            .mapToConfig("{\"websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/formattingOptions/font/font-family\" : \"Arial\"}")
        val json = mapper.writeValueAsString(ctaConfig)
        assertEquals("{\"vdp\":{\"formattingOptions\":{\"font\":\"Arial\"}}}", json)
        //validate deserializing to value
        ctaConfig = mapper.readValue(json)
        assertEquals("Arial", ctaConfig.vdp!!.get().formattingOptions!!.get().font!!.get())
    }

    @Test
    fun `serialize object with one attribute with NO value - ctaConfig`() {
        // one attribute only with null
        var ctaConfig = CtaConfigPayloadMapper
            .mapToConfig("{\"websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/formattingOptions/font/font-family\" : null}")
        val json = mapper.writeValueAsString(ctaConfig)
        assertEquals("{\"vdp\":{\"formattingOptions\":{\"font\":null}}}", json)
        //validate deserializing to empty
        ctaConfig = mapper.readValue(json)
        assertTrue(ctaConfig.vdp!!.get().formattingOptions!!.get().font!!.isEmpty)
    }
}
