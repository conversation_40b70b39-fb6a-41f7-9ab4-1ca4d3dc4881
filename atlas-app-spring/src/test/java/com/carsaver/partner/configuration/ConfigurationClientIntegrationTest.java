package com.carsaver.partner.configuration;

import com.carsaver.partner.client.inventory.VehicleForProgramsClient;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Profile;
import org.springframework.test.context.ActiveProfiles;

@SpringBootTest
@ActiveProfiles("integration")
@Tag("integration")
@Profile("integration")
class ConfigurationClientIntegrationTest {

    @MockBean
    VehicleForProgramsClient vehicleForProgramsClient;

    @Autowired
    ConfigurationClient client;

    @Test
    void getBuilder() throws JsonProcessingException {
        Object config = client.getBuilder("", "Lease Settings", "c65bc713-8e4a-4ff6-ada1-970d90b4bcf5");
        System.out.println(new ObjectMapper().writeValueAsString(config));
    }

    @Test
    void getBuilderConfiguration() throws JsonProcessingException {
        Object config = client.getBuilderConfiguration(null,"Lease Settings", "carsaver", "c65bc713-8e4a-4ff6-ada1-970d90b4bcf5");
        System.out.println(new ObjectMapper().writeValueAsString(config));
    }
}
