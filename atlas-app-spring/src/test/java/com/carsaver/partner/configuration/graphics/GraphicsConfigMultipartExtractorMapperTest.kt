package com.carsaver.partner.configuration.graphics

import com.carsaver.partner.configuration.graphics.mappers.GraphicsConfigMultipartExtractorMapper
import org.junit.jupiter.api.Test
import org.springframework.mock.web.MockMultipartFile
import org.springframework.web.multipart.MultipartFile
import kotlin.test.assertEquals
import kotlin.test.assertNull
import kotlin.test.assertTrue

class GraphicsConfigMultipartExtractorMapperTest {

    @Test
    fun `extractFiles should handle only logoFile being uploaded`() {
        // Given
        val logoFile = MockMultipartFile("logoFile", "logo.png", "image/png", "logo content".toByteArray())
        val request = mapOf<String, Any?>(
            "logoFile" to logoFile,
            "someOtherField" to "value"
        )

        // When
        val result = GraphicsConfigMultipartExtractorMapper.extractFiles(request)

        // Then
        assertEquals(4, result.size)
        assertEquals(logoFile, result[0]) // logoFile at index 0
        assertNull(result[1]) // faviconFile at index 1
        assertNull(result[2]) // dealershipFile1 at index 2
        assertNull(result[3]) // dealershipFile2 at index 3
    }

    @Test
    fun `extractFiles should handle only faviconFile being uploaded`() {
        // Given
        val faviconFile = MockMultipartFile("faviconFile", "favicon.ico", "image/x-icon", "favicon content".toByteArray())
        val request = mapOf<String, Any?>(
            "faviconFile" to faviconFile
        )

        // When
        val result = GraphicsConfigMultipartExtractorMapper.extractFiles(request)

        // Then
        assertEquals(4, result.size)
        assertNull(result[0]) // logoFile at index 0
        assertEquals(faviconFile, result[1]) // faviconFile at index 1
        assertNull(result[2]) // dealershipFile1 at index 2
        assertNull(result[3]) // dealershipFile2 at index 3
    }

    @Test
    fun `extractFiles should handle multiple files being uploaded`() {
        // Given
        val logoFile = MockMultipartFile("logoFile", "logo.png", "image/png", "logo content".toByteArray())
        val dealershipFile1 = MockMultipartFile("dealershipFile1", "mobile.jpg", "image/jpeg", "mobile content".toByteArray())
        val request = mapOf<String, Any?>(
            "logoFile" to logoFile,
            "dealershipFile1" to dealershipFile1
        )

        // When
        val result = GraphicsConfigMultipartExtractorMapper.extractFiles(request)

        // Then
        assertEquals(4, result.size)
        assertEquals(logoFile, result[0]) // logoFile at index 0
        assertNull(result[1]) // faviconFile at index 1
        assertEquals(dealershipFile1, result[2]) // dealershipFile1 at index 2
        assertNull(result[3]) // dealershipFile2 at index 3
    }

    @Test
    fun `extractFiles should handle all files being uploaded`() {
        // Given
        val logoFile = MockMultipartFile("logoFile", "logo.png", "image/png", "logo content".toByteArray())
        val faviconFile = MockMultipartFile("faviconFile", "favicon.ico", "image/x-icon", "favicon content".toByteArray())
        val dealershipFile1 = MockMultipartFile("dealershipFile1", "mobile.jpg", "image/jpeg", "mobile content".toByteArray())
        val dealershipFile2 = MockMultipartFile("dealershipFile2", "desktop.jpg", "image/jpeg", "desktop content".toByteArray())
        val request = mapOf<String, Any?>(
            "logoFile" to logoFile,
            "faviconFile" to faviconFile,
            "dealershipFile1" to dealershipFile1,
            "dealershipFile2" to dealershipFile2
        )

        // When
        val result = GraphicsConfigMultipartExtractorMapper.extractFiles(request)

        // Then
        assertEquals(4, result.size)
        assertEquals(logoFile, result[0])
        assertEquals(faviconFile, result[1])
        assertEquals(dealershipFile1, result[2])
        assertEquals(dealershipFile2, result[3])
    }

    @Test
    fun `extractFiles should handle no files being uploaded`() {
        // Given
        val request = mapOf<String, Any?>(
            "someOtherField" to "value"
        )

        // When
        val result = GraphicsConfigMultipartExtractorMapper.extractFiles(request)

        // Then
        assertEquals(4, result.size)
        assertNull(result[0])
        assertNull(result[1])
        assertNull(result[2])
        assertNull(result[3])
    }

    @Test
    fun `extractFiles should ignore non-MultipartFile values for file keys`() {
        // Given - simulating the case where frontend sends "null" strings
        val logoFile = MockMultipartFile("logoFile", "logo.png", "image/png", "logo content".toByteArray())
        val request = mapOf<String, Any?>(
            "logoFile" to logoFile,
            "faviconFile" to "null", // String "null", not actual null
            "dealershipFile1" to null, // Actual null
            "dealershipFile2" to "some string value"
        )

        // When
        val result = GraphicsConfigMultipartExtractorMapper.extractFiles(request)

        // Then
        assertEquals(4, result.size)
        assertEquals(logoFile, result[0]) // logoFile should be extracted
        assertNull(result[1]) // faviconFile should be null (string "null" ignored)
        assertNull(result[2]) // dealershipFile1 should be null
        assertNull(result[3]) // dealershipFile2 should be null (string ignored)
    }

    @Test
    fun `removeNotStringValues should filter out non-string values`() {
        // Given
        val logoFile = MockMultipartFile("logoFile", "logo.png", "image/png", "logo content".toByteArray())
        val request = mapOf<String, Any?>(
            "logoFile" to logoFile,
            "stringField" to "value",
            "nullField" to null,
            "numberField" to 123
        )

        // When
        val result = GraphicsConfigMultipartExtractorMapper.removeNotStringValues(request)

        // Then
        assertEquals(2, result.size)
        assertEquals("value", result["stringField"])
        assertEquals("", result["nullField"]) // null becomes empty string
        assertTrue(!result.containsKey("logoFile")) // MultipartFile filtered out
        assertTrue(!result.containsKey("numberField")) // Number filtered out
    }
}
