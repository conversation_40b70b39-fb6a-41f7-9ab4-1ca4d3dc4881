package com.carsaver.partner.helper;

import software.amazon.awssdk.services.dynamodb.model.ScanResponse;
import software.amazon.awssdk.services.dynamodb.paginators.ScanIterable;

import java.util.Iterator;
import java.util.NoSuchElementException;

public class SingleScanResponseIterable extends ScanIterable {
    private final ScanResponse scanResponse;

    public SingleScanResponseIterable(ScanResponse scanResponse) {
        super(null, null);
        this.scanResponse = scanResponse;
    }

    @Override
    public Iterator<ScanResponse> iterator() {
        return new Iterator<ScanResponse>() {
            private boolean hasNext = true;

            @Override
            public boolean hasNext() {
                return hasNext;
            }

            @Override
            public ScanResponse next() {
                if (!hasNext) {
                    throw new NoSuchElementException();
                }
                hasNext = false;
                return scanResponse;
            }
        };
    }
}
