package com.carsaver.partner.service.toggle;

import com.carsaver.partner.client.FeatureSubscriptionsClient;
import com.carsaver.partner.exception.InternalServerError;
import com.carsaver.partner.model.DealerProgram;
import com.carsaver.partner.model.FeatureSubscriptionRequest;
import com.carsaver.partner.model.FeatureSubscriptionResponse;
import com.carsaver.partner.model.ToggleConfigRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.servlet.http.HttpSession;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class RouteOneFinanceAndInsuranceFeatureToggleHandlerTest {

    @Mock
    private FeatureSubscriptionsClient featureSubscriptionsClient;

    @Mock
    private HttpSession session;

    @InjectMocks
    private RouteOneFinanceAndInsuranceFeatureToggleHandler handler;

    private ToggleConfigRequest toggleConfigRequest;
    private FeatureSubscriptionResponse fiResponse;

    @BeforeEach
    void setUp() {
        toggleConfigRequest = new ToggleConfigRequest();
        toggleConfigRequest.setIsEnabled(true);

        fiResponse = new FeatureSubscriptionResponse();
        fiResponse.setActive(true);
    }

    @Test
    void testHandleFeatureToggle_Success() {
        when(featureSubscriptionsClient.saveFeatureSubscription(any(FeatureSubscriptionRequest.class))).thenReturn(fiResponse);

        DealerProgram result = handler.handleFeatureToggle("dealerId", "programId", toggleConfigRequest);

        assertTrue(result.getIsRouteOneFAndIEnabled());
        assertFalse(result.getIsNesnaFAndIEnabled());
        verify(session).setAttribute(RouteOneFinanceAndInsuranceFeatureToggleHandler.NESNA_FEATURE_SUBSCRIPTION_ENABLED, false);
        verify(featureSubscriptionsClient, times(2)).saveFeatureSubscription(any(FeatureSubscriptionRequest.class));
    }

    @Test
    void testHandleFeatureToggle_NullResponse() {
        when(featureSubscriptionsClient.saveFeatureSubscription(any(FeatureSubscriptionRequest.class))).thenReturn(null);

       assertThrows(InternalServerError.class, () ->
            handler.handleFeatureToggle("dealerId", "programId", toggleConfigRequest));

    }

    @Test
    void testHandleFeatureToggle_ActiveNull() {
        fiResponse.setActive(null);
        when(featureSubscriptionsClient.saveFeatureSubscription(any(FeatureSubscriptionRequest.class))).thenReturn(fiResponse);

       assertThrows(InternalServerError.class, () ->
            handler.handleFeatureToggle("dealerId", "programId", toggleConfigRequest));

    }

    @Test
    void testSupports() {
        assertTrue(handler.supports("route_one_finance_and_insurance"));
        assertFalse(handler.supports("other_feature"));
    }
}
