package com.carsaver.partner.service.nesna;

import com.carsaver.partner.client.nissan.NissanWebClient;
import com.carsaver.partner.model.nensa.DealerNesnaProductRequest;
import com.carsaver.partner.model.nensa.DealerNesnaProductResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class NesnaProtectionProductsServiceTest {

    @Mock
    private NissanWebClient nissanWebClient;

    @InjectMocks
    private NesnaProtectionProductsService nesnaProtectionProductsService;

    private final String insuranceHost = "http://localhost:8080";

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(nesnaProtectionProductsService, "insuranceHost", insuranceHost);
    }

    @Test
    void testSaveProtectionProducts() {
        String dealerId = "dealer123";
        DealerNesnaProductRequest request = new DealerNesnaProductRequest();
        DealerNesnaProductRequest expectedResponse = new DealerNesnaProductRequest();

        when(nissanWebClient.post(any(String.class), any(DealerNesnaProductRequest.class), eq(DealerNesnaProductRequest.class), eq(NesnaProtectionProductsService.NESNA)))
            .thenReturn(expectedResponse);

        DealerNesnaProductRequest actualResponse = nesnaProtectionProductsService.saveProtectionProducts(dealerId, request);

        assertEquals(expectedResponse, actualResponse);
        verify(nissanWebClient, times(1)).post(eq(insuranceHost + "/api/nesna/dealers/" + dealerId + "/nesna-products"), eq(request), eq(DealerNesnaProductRequest.class), eq(NesnaProtectionProductsService.NESNA));
    }

    @Test
    void testRetrieveNesnaProtectionProducts() {
        String dealerId = "dealer123";
        DealerNesnaProductResponse expectedResponse = new DealerNesnaProductResponse();

        when(nissanWebClient.get(any(String.class), eq(DealerNesnaProductResponse.class), eq(NesnaProtectionProductsService.NESNA)))
            .thenReturn(expectedResponse);

        DealerNesnaProductResponse actualResponse = nesnaProtectionProductsService.retrieveNesnaProtectionProducts(dealerId);

        assertEquals(expectedResponse, actualResponse);
        verify(nissanWebClient, times(1)).get(eq(insuranceHost + "/api/nesna/dealers/" + dealerId + "/nesna-products"), eq(DealerNesnaProductResponse.class), eq(NesnaProtectionProductsService.NESNA));
    }
}
