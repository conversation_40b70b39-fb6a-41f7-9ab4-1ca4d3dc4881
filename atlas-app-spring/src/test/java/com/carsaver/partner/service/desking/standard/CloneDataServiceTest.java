package com.carsaver.partner.service.desking.standard;

import com.carsaver.magellan.api.deal.DealJacketRequest;
import com.carsaver.magellan.model.campaign.CampaignView;
import com.carsaver.magellan.model.deal.DealSheet;
import com.carsaver.partner.model.desking.CloneDealRequest;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
class CloneDataServiceTest {

    @InjectMocks
    private CloneRequestDataToDealJacketConverter cloneDataService;

    @Test
    public void rebatesTest() {
        CloneDealRequest.LineItem item1 = new CloneDealRequest.LineItem();
        item1.setAmount(200.0);
        item1.setName("rebate1");

        CloneDealRequest.LineItem item2 = new CloneDealRequest.LineItem();
        item2.setAmount(300.0);
        item2.setName("rebate1");
        ArrayList list = new ArrayList();
        list.add(item1);
        list.add(item2);

        CloneDealRequest.Rebates test = CloneDealRequest.Rebates.builder().lineItems(list).build();
        CloneDealRequest cloneDealRequest = CloneDealRequest.builder().rebates(test).build();
        DealJacketRequest request = this.buildDealerJacketRequest();

        cloneDataService.setRebates(cloneDealRequest, request);

        assertEquals(2, request.getDealSheet().getDealerRebates().size());

    }

    private DealJacketRequest buildDealerJacketRequest() {
        DealJacketRequest request = DealJacketRequest.builder().zipCode("33076")
            .creditScore(850)
            .dealSheet(new DealSheet())
            .campaignView(CampaignView.builder().build())
            .build();
        return request;
    }

}
