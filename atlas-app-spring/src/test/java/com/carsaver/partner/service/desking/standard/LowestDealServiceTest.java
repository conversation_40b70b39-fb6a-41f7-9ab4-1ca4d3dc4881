package com.carsaver.partner.service.desking.standard;

import com.carsaver.magellan.model.CertificateView;
import com.carsaver.partner.model.desking.CloneDealRequest;
import com.carsaver.partner.model.desking.CloneDealRequest.DueAtSigning;
import com.carsaver.partner.model.desking.standard.LeaseQuoteResponse;
import com.carsaver.partner.service.desking.standard.lease.LeaseQuoteService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;


@ExtendWith(MockitoExtension.class)
class LowestDealServiceTest {

    final LeaseQuoteService leaseQuoteService = mock(LeaseQuoteService.class);

    @InjectMocks
    private LowestDealService service;


    @Test
    void getLeaseLowestDealForEditedDeals_nullReturn() {
        CertificateView certificateView = buildCertificateView();
        CloneDealRequest cloneDealRequest = buildCloneDealRequest();

        when(leaseQuoteService.getEditedLeaseQuotes(any(),any())).thenReturn(null);

        LeaseQuoteResponse.Quote result = service.getLeaseLowestDealForEditedDeals(cloneDealRequest, certificateView);
        assertNull(result);
    }


    @Test
    void getLeaseLowestDealForEditedDeals_noQuotesReturn() {
        CertificateView certificateView = buildCertificateView();
        CloneDealRequest cloneDealRequest = buildCloneDealRequest();

        LeaseQuoteResponse response = LeaseQuoteResponse.builder()
            .quotes(Collections.emptyList()).build();

        when(leaseQuoteService.getEditedLeaseQuotes(any(),any())).thenReturn(response);

        LeaseQuoteResponse.Quote result = service.getLeaseLowestDealForEditedDeals(cloneDealRequest, certificateView);
        assertNull(result);
    }


    private CertificateView buildCertificateView() {
        CertificateView certificateView = new CertificateView();
        return certificateView;
    }

    private CloneDealRequest buildCloneDealRequest() {

        DueAtSigning dueAtSigning = DueAtSigning.builder().consumerCash("3000.0").build();
        CloneDealRequest cloneDealRequest = CloneDealRequest.builder().dueAtSigning(dueAtSigning).build();

        return cloneDealRequest;
    }
}
