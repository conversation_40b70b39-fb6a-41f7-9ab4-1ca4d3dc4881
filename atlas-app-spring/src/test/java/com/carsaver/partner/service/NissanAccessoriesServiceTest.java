package com.carsaver.partner.service;

import com.carsaver.accessories.api.AccessoryDetail;
import com.carsaver.accessories.api.DealerAccessory;
import com.carsaver.accessories.client.DealerAccessoryServiceClient;
import com.carsaver.nissan.accessories.api.AccessoriesResponse;
import com.carsaver.nissan.accessories.api.Accessory;
import com.carsaver.nissan.accessories.client.AccessoriesClient;
import com.carsaver.nissan.dealerinventory.api.Division;
import com.carsaver.nissan.dealerinventory.api.ModelLines;
import com.carsaver.nissan.dealerinventory.api.ModelLinesResponse;
import com.carsaver.nissan.dealerinventory.api.VehicleType;
import com.carsaver.nissan.dealerinventory.client.NissanDealerInventoryClient;
import com.carsaver.partner.model.dealer.ModelDetails;
import com.carsaver.partner.model.mapper.ModelLinesCodeResponseMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.test.util.ReflectionTestUtils;

import java.sql.Date;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.UUID;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class NissanAccessoriesServiceTest {

    private List<DealerAccessory> accessoriesList;
    private List<DealerAccessory> updatedDealerAccessoryList;
    AccessoriesResponse accessoryResponse;

    @BeforeEach
    public void setUp() {
        ReflectionTestUtils.setField(nissanAccessoriesService, "modelLinesCodeResponseMapper", modelLinesCodeResponseMapper);
    }


    @InjectMocks
    private  NissanAccessoriesService nissanAccessoriesService;
    private final ModelLinesCodeResponseMapper modelLinesCodeResponseMapper = mock(ModelLinesCodeResponseMapper.class);

    private final NissanDealerInventoryClient nissanDealerInventoryClient = mock(NissanDealerInventoryClient.class);
    private final AccessoriesClient accessoriesClient = mock(AccessoriesClient.class);
    private final DealerAccessoryServiceClient dealerAccessoryServiceClient = mock(DealerAccessoryServiceClient.class);

    private static final String DEALER_ID = "5308" ;
    private static final String MODEL_LINE_CODE = "ALT";
    private static final String MODEL_YEAR = "MY20";
    private static final String PART_NUMBER = "T99J1-6CA5A";
    private static final String NAME = "Trash Bin - Small";

    @Test
    @DisplayName("Get All model list")
    void getModelList() {
        int elements = 5 ;
        ModelLinesResponse  result = getModelCodeList(elements);

        when(nissanDealerInventoryClient.getModelLine(any(Division.class), any(VehicleType.class), any(Locale.class))) .thenReturn(result);

        List<ModelDetails> modelList = nissanAccessoriesService.getModelList();

        assertEquals(5, modelList.size());
    }

    @Test
    @DisplayName("Get DealerAccessories by model No with no changes in Nissan response")
    void getAccessoriesByModelLineCodeAndYearTest() {
        int elements = 5;
        AccessoriesResponse accessoryResponse = getAccessoryResponse(elements);
        List<DealerAccessory> dealerAccessoryResponseList = getDealerAccessoryList(elements);

        when(accessoriesClient.getAccessories(anyString(), anyString(), anyString(), any())).thenReturn(accessoryResponse);
        when( dealerAccessoryServiceClient.findAllByDealerIdAndIsAvailableAndModelLineCodeAndModelYear(any(), anyBoolean(), any(), any())).thenReturn(dealerAccessoryResponseList);

        List<DealerAccessory> result = nissanAccessoriesService.getAccessoriesByModelLineCodeAndYear(DEALER_ID, MODEL_LINE_CODE, MODEL_YEAR);

        assertEquals(elements, result.size());
    }

    @Test
    @DisplayName("Get DealerAccessories with some new accessories in Nissan response Test")
    void getAccessoriesByModelLineCodeAndYearWithDeltaTest() {
        int nissanElement = 6;
        int dealerElement = 5;

        retrieveNissanElement(nissanElement, dealerElement);

        when(accessoriesClient.getAccessories(anyString(), anyString(), anyString(), any())).thenReturn(accessoryResponse);
        when(dealerAccessoryServiceClient.findAllByDealerIdAndIsAvailableAndModelLineCodeAndModelYear(anyString(), anyBoolean(), anyString(), anyString()))
            .thenReturn(accessoriesList)
            .thenReturn(updatedDealerAccessoryList);

        List<DealerAccessory> result = nissanAccessoriesService.getAccessoriesByModelLineCodeAndYear(DEALER_ID, MODEL_LINE_CODE, MODEL_YEAR);

        assertEquals(nissanElement, result.size());
    }



    @Test
    @DisplayName("Get DealerAccessories with removed accessories in Nissan response Test ")
    void getAccessoriesByModelLineCodeAndYearWithRemovedAccessoriesTest() {
        int nissanElement = 5;
        int dealerElement = 6;

        retrieveNissanElement(nissanElement, dealerElement);

        //last entity will be extra data so setting it to disabled
        DealerAccessory dealerAccessoryEntity = accessoriesList.get(dealerElement - 1);
        dealerAccessoryEntity.setIsEnabled(Boolean.FALSE);
        dealerAccessoryEntity.setIsAvailable(Boolean.FALSE);

        when( accessoriesClient.getAccessories(anyString(), anyString(), anyString(), any()) ).thenReturn(accessoryResponse);
        when( dealerAccessoryServiceClient.findAllByDealerIdAndIsAvailableAndModelLineCodeAndModelYear(anyString(), anyBoolean(), anyString(), anyString()) )
            .thenReturn(accessoriesList)
            .thenReturn(updatedDealerAccessoryList);

        when( dealerAccessoryServiceClient.disableDealerAccessoryByNissan(anyString(), any(AccessoryDetail.class))).thenReturn(dealerAccessoryEntity);

        List<DealerAccessory> result = nissanAccessoriesService.getAccessoriesByModelLineCodeAndYear(DEALER_ID, MODEL_LINE_CODE, MODEL_YEAR);

        assertEquals(nissanElement, result.size());

    }


    private AccessoriesResponse getAccessoryResponse(int elements) {
        AccessoriesResponse accessoriesResponse = new AccessoriesResponse();
        List<Accessory> accessories = new ArrayList<>();

        IntStream.range(0, elements).forEach(i -> {
            Accessory accessory = new Accessory();
            accessory.setModel(NAME + i);
            accessory.setModel_year("MY"+ (22 - i));
            accessory.setPart_number(PART_NUMBER + i );
            accessories.add(accessory);
        });

        accessoriesResponse.setAccessories(accessories);

        return accessoriesResponse;
    }

    private ModelLinesResponse getModelCodeList(int elements) {
        ModelLinesResponse modelLinesResponse = new ModelLinesResponse();
        List<ModelLines> modelLinesList = new ArrayList<>();
        ModelLines modelLines = new ModelLines();

        IntStream.range(0, elements).forEach(i -> {
            modelLines.setModelLineCode(MODEL_LINE_CODE+ i);
            modelLines.setModelLineName(NAME + i );

            modelLinesList.add(modelLines);
        });

        modelLinesResponse.setModelLines(modelLinesList);
        return modelLinesResponse;
    }

    void retrieveNissanElement(int nissanElement, int dealerElement) {
        accessoryResponse = getAccessoryResponse(nissanElement);
        accessoriesList = getDealerAccessoryList(dealerElement);
        updatedDealerAccessoryList = getDealerAccessoryList(nissanElement);
    }

    private List<DealerAccessory> getDealerAccessoryList( int elements ) {

        List<DealerAccessory> dealerAccessoryList = new ArrayList<>();

        IntStream.range(0, elements).forEach( i -> {
            DealerAccessory accessory = DealerAccessory.builder()
                .id(UUID.randomUUID().toString())
                .dealerId(DEALER_ID)
                .isAvailable(Boolean.TRUE)
                .isEnabled(Boolean.TRUE)
                .modelLineCode(MODEL_LINE_CODE)
                .modelYear("MY" + (22 - i))
                .partNumber(PART_NUMBER + i)
                .name(NAME + i)
                .createdDate(Date.from(Instant.now()))
                .updatedDate(Date.from(Instant.now()))
                .build();

            dealerAccessoryList.add(accessory);
        });

        return dealerAccessoryList;
    }

}
