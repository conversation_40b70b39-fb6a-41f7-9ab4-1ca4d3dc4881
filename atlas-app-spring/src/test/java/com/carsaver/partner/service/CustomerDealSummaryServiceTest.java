package com.carsaver.partner.service;

import com.carsaver.chrome.ads.wsdl.VehicleDescription;
import com.carsaver.magellan.client.ChromeAdsClient;
import com.carsaver.magellan.model.UserView;
import com.carsaver.magellan.model.user.CreditEvaluator;
import com.carsaver.magellan.model.user.CreditProfile;
import com.carsaver.magellan.model.user.CreditRange;
import com.carsaver.magellan.model.user.UserVehicleView;
import com.carsaver.magellan.model.vehicle.UserVehicleQuoteView;
import com.carsaver.partner.client.customer.CustomerDealSummaryClient;
import com.carsaver.partner.http.HttpException;
import com.carsaver.partner.model.deal.CustomerDealSummaryRequest;
import com.carsaver.partner.model.retail.CustomerDealSummaryResponse;
import com.carsaver.partner.model.retail.DealSummaryResponse;
import kong.unirest.UnirestException;
import org.elasticsearch.common.collect.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class CustomerDealSummaryServiceTest {

    @InjectMocks
    private CustomerDealSummaryService customerDealSummaryService;


    @Mock
    private UserView userView;

    @Mock
    private CreditProfile creditProfile;

    @Mock
    private DealSummaryResponse dealSummaryResponse;
    @Mock
    CustomerDealSummaryClient customerDealSummaryClient;
    @Mock
    UserVehicleService userVehicleService;
    @Mock
    ChromeAdsClient chromeAdsClient;


    @Test
    void testGetDealSummary_Success() throws HttpException {
        String dealerId = "dealerId";
        CustomerDealSummaryRequest request = CustomerDealSummaryRequest.builder().dealerIds(List.of(dealerId)).build();
        UserVehicleQuoteView userVehicleQuoteView = mock(UserVehicleQuoteView.class);
        UserVehicleView  userVehicleView = mock(UserVehicleView.class);
        VehicleDescription vehicleDescription = mock(VehicleDescription.class);

        when(vehicleDescription.getBestMakeName()).thenReturn("Nissan");
        when(vehicleDescription.getBestModelName()).thenReturn("Kicks");
        when(vehicleDescription.getModelYear()).thenReturn(2020);
        when(userVehicleView.getTradeType()).thenReturn("TRADE");

        when(chromeAdsClient.findByVin(any())).thenReturn(vehicleDescription);
        when(dealSummaryResponse.getPaymentSummary()).thenReturn(null);
        when(dealSummaryResponse.getDownPaymentSummary()).thenReturn(null);
        when(dealSummaryResponse.getRecentTradedVehicle()).thenReturn(userVehicleView);
        when(creditProfile.getCreditEvaluator()).thenReturn(CreditEvaluator.CUSTOMER);
        when(userView.getCreditProfile()).thenReturn(Optional.of(creditProfile));
        when(creditProfile.getCreditScore()).thenReturn(700);
        when(userVehicleService.getGuaranteedUserVehicleQuote(any())).thenReturn(userVehicleQuoteView);
        when(chromeAdsClient.findByVin(any())).thenReturn(vehicleDescription);
        when(customerDealSummaryClient.fetchDealSummary(any())).thenReturn(dealSummaryResponse);


        CustomerDealSummaryResponse response = customerDealSummaryService.getDealSummary(userView, dealerId);

        verify(customerDealSummaryClient).fetchDealSummary(any());
        assertEquals("Nissan", response.getTrade().getMake());
        assertEquals("Kicks", response.getTrade().getModel());
        assertEquals(2020, response.getTrade().getYear());
        assertEquals(0, response.getTradeEquity());
        assertEquals(CreditRange.findFor(700).orElse(null), response.getRange());
        assertEquals("Self-rated", response.getCreditRatingSource());
    }

    @Test
    void testGetDealSummary_SuccessV2() throws HttpException {
        String dealerId = "dealerId";
        CustomerDealSummaryRequest request = CustomerDealSummaryRequest.builder().dealerIds(List.of(dealerId)).build();
        UserVehicleQuoteView userVehicleQuoteView = mock(UserVehicleQuoteView.class);
        UserVehicleView  userVehicleView = mock(UserVehicleView.class);
        VehicleDescription vehicleDescription = mock(VehicleDescription.class);

        when(vehicleDescription.getBestMakeName()).thenReturn("Nissan");
        when(vehicleDescription.getBestModelName()).thenReturn("Kicks");
        when(vehicleDescription.getModelYear()).thenReturn(2020);
        when(userVehicleView.getTradeType()).thenReturn("TRADE");

        when(chromeAdsClient.findByVin(any())).thenReturn(vehicleDescription);
        when(dealSummaryResponse.getPaymentSummary()).thenReturn(null);
        when(dealSummaryResponse.getDownPaymentSummary()).thenReturn(null);
        when(dealSummaryResponse.getRecentTradedVehicle()).thenReturn(userVehicleView);
        when(creditProfile.getCreditEvaluator()).thenReturn(CreditEvaluator.CUSTOMER);
        when(userView.getCreditProfile()).thenReturn(Optional.of(creditProfile));
        when(creditProfile.getCreditScore()).thenReturn(700);
        when(userVehicleService.getGuaranteedUserVehicleQuote(any())).thenReturn(userVehicleQuoteView);
        when(chromeAdsClient.findByVin(any())).thenReturn(vehicleDescription);
        when(customerDealSummaryClient.fetchDealSummaryV2(any(), any())).thenReturn(dealSummaryResponse);


        CustomerDealSummaryResponse response = customerDealSummaryService.getDealSummaryV2(userView, request);

        verify(customerDealSummaryClient).fetchDealSummaryV2(any(),any());
        assertEquals("Nissan", response.getTrade().getMake());
        assertEquals("Kicks", response.getTrade().getModel());
        assertEquals(2020, response.getTrade().getYear());
        assertEquals(0, response.getTradeEquity());
        assertEquals(CreditRange.findFor(700).orElse(null), response.getRange());
        assertEquals("Self-rated", response.getCreditRatingSource());
    }

    @Test
    void testGetDealSummary_NullDealerIdsV2() throws HttpException {

        CustomerDealSummaryRequest request = CustomerDealSummaryRequest.builder().dealerIds(null).build();
        UserVehicleQuoteView userVehicleQuoteView = mock(UserVehicleQuoteView.class);
        UserVehicleView  userVehicleView = mock(UserVehicleView.class);
        VehicleDescription vehicleDescription = mock(VehicleDescription.class);

        when(vehicleDescription.getBestMakeName()).thenReturn("Nissan");
        when(vehicleDescription.getBestModelName()).thenReturn("Kicks");
        when(vehicleDescription.getModelYear()).thenReturn(2020);
        when(userVehicleView.getTradeType()).thenReturn("TRADE");

        when(chromeAdsClient.findByVin(any())).thenReturn(vehicleDescription);
        when(dealSummaryResponse.getPaymentSummary()).thenReturn(null);
        when(dealSummaryResponse.getDownPaymentSummary()).thenReturn(null);
        when(dealSummaryResponse.getRecentTradedVehicle()).thenReturn(userVehicleView);
        when(creditProfile.getCreditEvaluator()).thenReturn(CreditEvaluator.CUSTOMER);
        when(userView.getCreditProfile()).thenReturn(Optional.of(creditProfile));
        when(creditProfile.getCreditScore()).thenReturn(700);
        when(userVehicleService.getGuaranteedUserVehicleQuote(any())).thenReturn(userVehicleQuoteView);
        when(chromeAdsClient.findByVin(any())).thenReturn(vehicleDescription);
        when(customerDealSummaryClient.fetchDealSummaryV2(any(), any())).thenReturn(dealSummaryResponse);


        CustomerDealSummaryResponse response = customerDealSummaryService.getDealSummaryV2(userView, request);

        verify(customerDealSummaryClient).fetchDealSummaryV2(any(),any());
        verify(userVehicleService).getGuaranteedUserVehicleQuoteByDealer(any(), eq(null));
        assertEquals("Nissan", response.getTrade().getMake());
        assertEquals("Kicks", response.getTrade().getModel());
        assertEquals(2020, response.getTrade().getYear());
        assertEquals(0, response.getTradeEquity());
        assertEquals(CreditRange.findFor(700).orElse(null), response.getRange());
        assertEquals("Self-rated", response.getCreditRatingSource());
    }

    @Test
    void testGetDealSummary_EmptyDealerIdsV2() throws HttpException {

        CustomerDealSummaryRequest request = CustomerDealSummaryRequest.builder().dealerIds(List.of()).build();
        UserVehicleQuoteView userVehicleQuoteView = mock(UserVehicleQuoteView.class);
        UserVehicleView  userVehicleView = mock(UserVehicleView.class);
        VehicleDescription vehicleDescription = mock(VehicleDescription.class);

        when(vehicleDescription.getBestMakeName()).thenReturn("Nissan");
        when(vehicleDescription.getBestModelName()).thenReturn("Kicks");
        when(vehicleDescription.getModelYear()).thenReturn(2020);
        when(userVehicleView.getTradeType()).thenReturn("TRADE");

        when(chromeAdsClient.findByVin(any())).thenReturn(vehicleDescription);
        when(dealSummaryResponse.getPaymentSummary()).thenReturn(null);
        when(dealSummaryResponse.getDownPaymentSummary()).thenReturn(null);
        when(dealSummaryResponse.getRecentTradedVehicle()).thenReturn(userVehicleView);
        when(creditProfile.getCreditEvaluator()).thenReturn(CreditEvaluator.CUSTOMER);
        when(userView.getCreditProfile()).thenReturn(Optional.of(creditProfile));
        when(creditProfile.getCreditScore()).thenReturn(700);
        when(userVehicleService.getGuaranteedUserVehicleQuote(any())).thenReturn(userVehicleQuoteView);
        when(chromeAdsClient.findByVin(any())).thenReturn(vehicleDescription);
        when(customerDealSummaryClient.fetchDealSummaryV2(any(), any())).thenReturn(dealSummaryResponse);


        CustomerDealSummaryResponse response = customerDealSummaryService.getDealSummaryV2(userView, request);

        verify(customerDealSummaryClient).fetchDealSummaryV2(any(),any());
        verify(userVehicleService).getGuaranteedUserVehicleQuoteByDealer(any(), eq(null));
        assertEquals("Nissan", response.getTrade().getMake());
        assertEquals("Kicks", response.getTrade().getModel());
        assertEquals(2020, response.getTrade().getYear());
        assertEquals(0, response.getTradeEquity());
        assertEquals(CreditRange.findFor(700).orElse(null), response.getRange());
        assertEquals("Self-rated", response.getCreditRatingSource());
    }




    @Test
    void testGetDealSummary_ClientException() {
        String dealerId = "dealerId";
        // Mocking the exception
        when(customerDealSummaryClient.fetchDealSummary(any())).thenThrow(new UnirestException("Client error"));

        // Mocking the credit profile
        when(creditProfile.getCreditEvaluator()).thenReturn(CreditEvaluator.CUSTOMER);
        when(userView.getCreditProfile()).thenReturn(Optional.of(creditProfile));

        // Calling the service method
        CustomerDealSummaryResponse response = customerDealSummaryService.getDealSummary(userView, dealerId);

        // Verifying that an exception is handled and values are returned with default/null values
        verify(customerDealSummaryClient).fetchDealSummary(any());
        assertNull(response);
    }


}
