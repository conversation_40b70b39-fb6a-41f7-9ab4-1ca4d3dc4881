package com.carsaver.partner.service;

import com.carsaver.elasticsearch.model.DealerDoc;
import com.carsaver.magellan.client.DealerClient;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.partner.filter.DealerUserAccessFilter;
import com.carsaver.partner.reporting.service.ProgramService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;

class LoggedInUserProgramServiceTest {

    private static ProgramService programService;
    private static DealerClient dealerClient;
    private static DealerUserAccessFilter dealerUserAccessFilter;
    private static LoggedInUserProgramService loggedInUserProgramService;

    @BeforeAll
    static void beforeAll() {
        programService = Mockito.mock(ProgramService.class);
        dealerClient = Mockito.mock(DealerClient.class);
        dealerUserAccessFilter = Mockito.mock(DealerUserAccessFilter.class);

        loggedInUserProgramService = LoggedInUserProgramService
                .builder()
                .programService(programService)
                .dealerClient(dealerClient)
                .dealerUserAccessFilter(dealerUserAccessFilter)
                .build();
    }

    @Test
    void setSelectedProgramTest() {
        String loggedInUserId = "LOGGED_IN_USER_ID";

        Assertions.assertDoesNotThrow(() -> {
            Mockito.when(programService.getDealersByPrograms(any())).thenReturn(Collections.emptyList());
            List<DealerView> dealerViewList = loggedInUserProgramService.getSelectedProgram(loggedInUserId, Collections.emptyList());
            assertTrue(dealerViewList.isEmpty());
        }, "Empty ProgramIds param");

        Assertions.assertDoesNotThrow(() -> {
            DealerDoc dealer1 = DealerDoc.builder()
                    .id("DEALER_ID_1")
                    .name("DEALER_NAME_1")
                    .build();
            Mockito.when(programService.getDealersByPrograms(any())).thenReturn(Collections.singletonList(dealer1));

            DealerView dealerView1 = new DealerView();
            dealerView1.setId(dealer1.getId());
            dealerView1.setName(dealer1.getName());
            Mockito.when(dealerClient.findById(dealer1.getId())).thenReturn(dealerView1);

            List<DealerView> dealerViewList = loggedInUserProgramService.getSelectedProgram(loggedInUserId, Collections.singletonList("PROGRAM_1"));
            assertEquals(1, dealerViewList.size());
        }, "One Dealer set");


    }
}
