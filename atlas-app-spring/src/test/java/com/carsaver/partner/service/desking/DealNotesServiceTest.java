package com.carsaver.partner.service.desking;

import com.carsaver.partner.client.nissan.NissanWebClient;
import com.carsaver.partner.model.desking.DealNotesRequest;
import com.carsaver.partner.model.desking.DealNotesResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DealNotesServiceTest {
    private static final String DEAL_NOTES_URL = "http://localhost:3001/deal-notes";
    private static final String NOTE = "This is test note";
    private static final boolean CUSTOMER_ACCESS = true;
    private static final long CLONE_DEAL_ID = 1009L;
    private static final String ID = UUID.randomUUID().toString();

    @InjectMocks
    DealNotesService dealNotesService;
    NissanWebClient nissanWebClient = mock(NissanWebClient.class);

    @BeforeEach
    void init(){
        ReflectionTestUtils.setField(dealNotesService, "dealerServiceUrl", DEAL_NOTES_URL);
    }

    @Test
    void retrieveAllDealNotesByCloneDealId() {
        List<DealNotesResponse> dealNotesResponses = buildDealNotesResponseList();
        when(nissanWebClient.getList(anyString(),anyString() )).thenReturn(new ArrayList<>(dealNotesResponses));
        List<DealNotesResponse> result = dealNotesService.retrieveAllDealNotesByCloneDealId(1001L);
        assertEquals(2, result.size());
    }

    @Test
    void retrieveAllDealNotesByCloneDealId_EmptyList() {
        when(nissanWebClient.getList(anyString(), anyString())).thenReturn(new ArrayList<>());
        List<DealNotesResponse> result = dealNotesService.retrieveAllDealNotesByCloneDealId(1001L);
        assertEquals(0, result.size());
    }

    @Test
    void saveDealNote() {
        DealNotesRequest request = buildDealNotesRequest();
        DealNotesResponse response = buildDealNotesResponse();
        when(nissanWebClient.post(anyString(), any(), any(), anyString())).thenReturn(response);
        DealNotesResponse result = dealNotesService.saveDealNote(request);
        assertEquals(response, result);
    }

    List<DealNotesResponse> buildDealNotesResponseList() {
        List<DealNotesResponse> result = IntStream.of(10000, 10001).mapToObj(i -> {
            DealNotesResponse dealNotesResponse = new DealNotesResponse();
            dealNotesResponse.setNote("This is note " + i);
            dealNotesResponse.setId(UUID.randomUUID().toString());
            dealNotesResponse.setCloneDealId(1001L);
            dealNotesResponse.setCustomerAccess(true);
            return dealNotesResponse;
        }).collect(Collectors.toList());
        return result;
    }

    private DealNotesRequest buildDealNotesRequest() {
        DealNotesRequest dealNotesRequest = new DealNotesRequest();
        dealNotesRequest.setNote(NOTE);
        dealNotesRequest.setCloneDealId(CLONE_DEAL_ID);
        dealNotesRequest.setCustomerAccess(CUSTOMER_ACCESS);
        return dealNotesRequest;
    }

    DealNotesResponse buildDealNotesResponse() {
        DealNotesResponse dealNotesResponse = new DealNotesResponse();
        dealNotesResponse.setId(ID);
        dealNotesResponse.setCloneDealId(CLONE_DEAL_ID);
        dealNotesResponse.setNote(NOTE);
        dealNotesResponse.setCustomerAccess(CUSTOMER_ACCESS);
        return dealNotesResponse;
    }
}
